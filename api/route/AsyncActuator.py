#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/7/18 9:29
# <AUTHOR> 10263601
import asyncio
import copy
import datetime
import gc
import json
import re
import traceback
from asyncio import FIRST_COMPLETED

from fastapi import Request

from GlobalVars import statusUpdater, status
from api.Const import PIPELINE_STAGE_ID, RECORD_ID, TASK_ID, EXEC_ID
from api.Response import fail as failResponse, success as successResponse
from config.AppSettings import settings
from config.CommonCfg import HISTORY_FILE_PATH_PREFIX
from infrastructure.log.logging import loggerId, logStatus, startTime
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from infrastructure.resource.service.RouteResourceService import RouteResourceService
from infrastructure.utils.ActionTaskMonitor import is_active, activate, complete, check_executed
from infrastructure.utils.ContextRepository import ContextRepository
from infrastructure.utils.DataDealer import replace_placeholder_parameters
from infrastructure.utils.Exceptions import LoadConfigError
from infrastructure.utils.StatusUpdater import StatusUpdater
from infrastructure.utils.TimeHandler import get_format_datetime_stamp


class AsyncActuator:

    def __init__(self, request, original_route_handler, tags: list):
        self.request: Request = request
        self.resource = request.state.resource
        self.routeHandler = original_route_handler
        self.pipelineStageId = self.resource.get(PIPELINE_STAGE_ID)
        # start
        self.recordId = self.resource.get(RECORD_ID)
        self.taskId = self.resource.get(TASK_ID, "")
        self.execId = self.resource.get(EXEC_ID, "")
        # end
        self.identifier = f"{self.pipelineStageId}-{self.recordId}"
        self.tags = tags

    async def run(self):
        if not self._is_executable():
            return

        await self._setup()

        result = await self._get_original_exec_result()

        await self._cleanup(result)

    def _is_executable(self) -> bool:
        if (not is_active(self.taskId)) or (not check_executed(self.execId)):
            return False

        return True

    async def _get_original_exec_result(self):
        async def real_exec():
            activate(self.identifier)
            try:
                return await self.routeHandler(self.request)
            finally:
                complete(self.identifier)

        async def monitor():
            while is_active(self.identifier) and is_active(self.taskId):
                await asyncio.sleep(0.5)

        try:
            if "builtin" not in self.tags:
                self._load_config()

            await self._substitute_parameter()

            if settings.actionMonitor:
                actionTask, monitorTask = asyncio.create_task(real_exec()), asyncio.create_task(monitor())
                done, pending = await asyncio.wait([actionTask, monitorTask], return_when=FIRST_COMPLETED)
                if monitorTask in done and actionTask in pending:
                    actionTask.cancel()
                    Logger.warn(f"request cancelled!")
                    return failResponse(msg="action was cancelled!")
                result = actionTask.result()
            else:
                result = await self.routeHandler(self.request)
            return json.loads(result.body.decode('utf-8'))
        except LoadConfigError:
            Logger.error(f'biz_core load config failed! err: \n{traceback.format_exc(limit=10)}')
            return failResponse(msg=f"加载资源异常, 详情可查看日志")
        except Exception as err:
            Logger.error(f"request failed! err: {traceback.format_exc(limit=10)}")
            return failResponse(msg=f"执行异常，error: {err}, 详情可查看日志")

    async def _setup(self):
        self._set_status_updater()
        self._set_result_assert()

    async def _cleanup(self, result):
        if result:
            result = self._update_result_by_assert(result)
            await self._update_context(result)
            status().update_done_status(result)
        logStatus.set(True)
        gc.collect()
        Logger.info(f"======= Request processed =======")

    def _load_config(self):
        try:
            RouteResourceService().load_config(self.request)
        except:
            traceInfo = traceback.format_exc(limit=4)
            Logger.error(f"Biz_core load config fail! ,err:{traceInfo}")
            raise LoadConfigError

    def _set_logger(self):
        loggerId.set(self.identifier)
        startTime.set(datetime.datetime.now())
        logStatus.set(False)
        Logger.info(f"=====  {loggerId.get()} starting =====")

    def _set_status_updater(self):
        attrs = copy.deepcopy(self.resource)
        statusUpdater.set(StatusUpdater(self.pipelineStageId, self.taskId, attrs))
        status().update_init_status()

    def _set_result_assert(self):
        self.resultAssert = self.resource.get("resultAssert")

    async def _substitute_parameter(self):
        changed = False
        body = json.loads((await self.request.body()).decode())
        temp = body.pop("para")  # 暂存其他参数

        # 初版参数替换
        if replaceParas := await Refs(self.request).get_substitution_para():
            body = replace_placeholder_parameters(replaceParas, body)
            body.update({"para": temp})  # 写回其他参数
            changed = True

        if changed:
            self.request._json = body
            self.request._body = json.dumps(body).encode()  # 将修改后的请求体写回

    def _update_result_by_assert(self, result):
        if self.resultAssert:
            assertType, assertContent = self.resultAssert.get("type"), self.resultAssert.get("content", "")
            resStr = str(result)
            Logger.info(f"resStr: {resStr}")
            if assertType == "contain":
                try:
                    pattern = re.compile(assertContent)
                except Exception:
                    return failResponse(msg=f"断言异常，请检查断言格式是否正确: {assertContent}")
                if re.match(pattern, resStr):
                    return successResponse(msg=f"断言判定成功, 断言为: {assertContent}, 结果为: {resStr}")
                else:
                    return failResponse(msg=f"断言判定失败, 断言为: {assertContent}, 结果为: {resStr}")
            Logger.error(f"result assert failed! no assertType {assertType}")
        return result

    async def _update_context(self, result):
        context = {**self.resource, "output": result, "timestamp": get_format_datetime_stamp(),
                   "historyFilePath": self._get_history_file_path()}
        Logger.info(f"context!!!!!!", context)
        condition = {PIPELINE_STAGE_ID: self.pipelineStageId, RECORD_ID: self.recordId}
        contextRepo = ContextRepository()
        await contextRepo.update(context, condition)

    def _get_history_file_path(self):
        filePath = self.taskId + "/" + self.pipelineStageId + "/" + str(self.recordId) + ".html"
        historyFilePath = HISTORY_FILE_PATH_PREFIX + filePath
        return historyFilePath
