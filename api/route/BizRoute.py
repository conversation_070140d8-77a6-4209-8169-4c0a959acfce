#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/6/26 11:27
# <AUTHOR> ********
import asyncio
import traceback
from datetime import datetime
from typing import Callable

from fastapi import Request, Response
from fastapi.routing import APIRoute
from starlette.background import BackgroundTask
from starlette.responses import JSONResponse

from api.Const import EXEC_ID, PARA
from api.Response import fail as failResponse, success as successResponse
from api.route.AsyncActuator import AsyncActuator
from config.AppSettings import settings
from domain.platform.slave.Slave import Slave
from infrastructure.logger import Logger
from infrastructure.resource.service.MockResourceService import MockResourceService
from infrastructure.resource.service.RouteResourceService import RouteResourceService


class BizRoute(APIRoute):

    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            try:
                await MockResourceService().load_resource(request)
            except Exception as err:
                return JSONResponse(status_code=200, content=failResponse(msg=err))

            return await original_route_handler(request)

        return custom_route_handler


class BizAsyncRoute(APIRoute):

    def get_route_handler(self) -> Callable:

        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            slave: Slave = Slave()
            taskExecId = (await request.json()).get(PARA, {}).get(EXEC_ID, datetime.now())

            def get_route_params():
                routeParams = {"openapi_extra": self.openapi_extra}
                return routeParams

            await RouteResourceService().load_resource(request, get_route_params())

            slave.add_task_info(taskExecId)

            def run_background_task():
                asyncActuator = AsyncActuator(request, original_route_handler, self.tags)
                try:
                    asyncio.run(asyncActuator.run())
                except asyncio.exceptions.CancelledError:
                    return
                except:
                    err = traceback.format_exc(limit=10)
                    Logger.error(f"AsyncActuator run err: {err}")
                finally:
                    Logger.debug(f"========任务{taskExecId} 已结束========")
                    slave.remove_task_info(taskExecId)

            return JSONResponse(status_code=200, content=successResponse(),
                                background=BackgroundTask(run_background_task))

        return custom_route_handler


bizRoute = BizRoute if settings.debug else BizAsyncRoute
