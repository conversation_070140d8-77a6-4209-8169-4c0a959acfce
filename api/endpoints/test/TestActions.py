import asyncio
from api.endpoints.test.Schemas import TestInfo
from fastapi import APIRouter, Request
from api.Response import success
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


######## for test ########

@router.post("/test", summary="test", description=description("test", "10333721 许王禾子"))
async def test(testInfo: TestInfo):
    Logger.info("attrs: ", testInfo.attrs)
    if type(testInfo.attrs) == dict:
        Logger.info("dict!!!!! ")
    elif type(testInfo.attrs) == str:
        Logger.info("str!!!!! ")
    return success()


@router.post("/resource_test", summary="资源测试", description=description("test", "10234064 文樟"))
async def resource_test(request: Request):
    Logger.info("suc!")
    if msg := request.state.resource.get("config", {}).get("FilteredRlt", "No Rlt"):
        return success(msg=msg)
    return success(msg="No Filter Rlt")


@router.post("/test1")
async def test():
    await long_time_task()
    return success()


@router.post("/test2")
async def test():
    try:
        loop = asyncio.get_running_loop()
        task = asyncio.create_task(long_time_task())
        loop.run_until_complete(task)
    except Exception as err:
        Logger.error(f"err: {err}")
        asyncio.run(long_time_task())
    return success()


async def long_time_task():  # 耗时操作
    for i in range(100):
        await asyncio.sleep(1)  # time.sleep 改为 asyncio.sleep()
        print(i)
