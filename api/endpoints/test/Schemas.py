"""
@File: Schemas.py
@Author: 许王禾子10333721
@Time: 2024/4/11 9:38
@License: Copyright 2022-2030
@Desc: None
"""
from pydantic import BaseModel, Field
from typing import TypeVar

DataT = TypeVar("DataT")


class TestInfo(BaseModel):
    attrs: DataT = Field(..., type="jsonEditor", description='创建网元的参数，为json格式，例 {"moId": "1", "sd": 1} ')
    # attrs: str = Field(..., description='创建网元的参数，为json格式，例 {"moId": "1", "sd": 1} ')
    a1: dict = Field()

