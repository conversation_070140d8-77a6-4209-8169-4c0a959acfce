# -*- encoding: utf-8 -*-
"""
@Time    :   2023-8-24
<AUTHOR>   wen <PERSON><PERSON> 10234064
@Desc    :   None
"""
import copy

from fastapi import APIRouter
from api.Response import success, fail
from api.endpoints.platform.env.Schemas import GetConfig, DebugActionFilter, CompositeActionFilter, VerifyConfig, \
    DebugActionFilterMany
from domain.platform.ActionInfo import description
from domain.platform.env.EnvConfig import EnvConfig, merge
from infrastructure.resource.domain.config.ConfigVerifier import ConfigVerifier
from infrastructure.resource.service.ConfigGeneratorService import ConfigGeneratorService
from infrastructure.resource.service.DeviceManager import FILTERS

router = APIRouter()


@router.post("/apply", summary="ERMS环境标准化配置获取",
             description=description(
                 "根据ERMS的环境ID，从ERMS获取环境的配置，并生成标准化格式的配置，如果入参有历史数据，则会进行合并",
                 '文樟 10234064'))
async def apply(getConfig: GetConfig):
    config = {}
    try:
        config = EnvConfig(getConfig.ermsId, getConfig.groupNo, getConfig.collect).get_erms()
    except Exception as e0:
        # pipeline服务不会同时解析data和msg，erms数据解析异常并返回历史数据场景采用success消息，在data里携带warn提示信息
        return fail(f'从ERMS获取最新环境配置失败, \n错误信息:\n{e0}')

    try:
        config = merge(copy.deepcopy(config), getConfig.historyConfig)
    except Exception as e1:
        return success('success', data={'config': getConfig.historyConfig,
                                        'warn': f'合并ERMS最新配置和历史数据出现错误，将返回线上历史数据!\n错误信息：{e1}'})

    try:
        ConfigGeneratorService.verify_config(config)
    except Exception as e2:
        return success('success', data={'config': config,
                                        'warn': f'合并成功，残留部分历史数据需要用户自行判断和修改，否则会导致{e2}'})
    return success('success', data={'config': config})


@router.post("/verify", summary="环境资源校验接口",
             description=description(
                 "根据用户前端提供的config信息，进行校验，并提示失败的文本信息",
                 '文樟 10234064'))
async def verify(verifyConfig: VerifyConfig):
    try:
        if verifyConfig.config == {}:
            return success('success')
        ConfigGeneratorService.verify_config(verifyConfig.config)
        return success('success')
    except Exception as e:
        return fail(f'{e}')


@router.post("/verify_details", summary="环境资源校验接口",
             description=description(
                 "根据用户前端提供的config信息，进行校验，并返回失败的详细信息，返回字典格式",
                 '文樟 10234064'))
async def verify_details(verifyConfig: VerifyConfig):
    try:
        if verifyConfig.config == {}:
            return success('success', data={})
        cf = ConfigVerifier(verifyConfig.config)
        cf.verify()
        return success('success', data=cf.errMsg.details)
    except Exception as e:
        return fail(f'{e}')


@router.post("/action/config_filter", summary="Action的环境过滤调试接口",
             description=description(
                 "根据用户前端编辑的action级过滤信息，筛选出config中满足条件的对应deviceId",
                 '文樟 10234064'))
async def debug_action_filter(debugActionFilter: DebugActionFilter):
    try:
        obj = ConfigGeneratorService.debug_filters(debugActionFilter.config, debugActionFilter.filters,
                                                   debugActionFilter.stageId)
        return success('success', data={'result': obj.result, 'warm': obj.errMsg})
    except Exception as e:
        return fail(f'{e}')


@router.post("/action/config_filter_many", summary="批量Action的环境过滤调试接口",
             description=description(
                 "根据用户前端编辑的批量action级过滤信息，筛选出config中满足条件的对应deviceId",
                 '叶晨涛 10393540'))
async def debug_action_filter_many(debugActionFilterMany: DebugActionFilterMany):
    try:
        await ConfigGeneratorService().debug_filters_many(debugActionFilterMany.fullConfig,
                                                          debugActionFilterMany.debugActionFilterTree)
        return success('success', data={'result': debugActionFilterMany.debugActionFilterTree})
    except Exception as e:
        return fail(f'{e}')


@router.post("/composite_action/filter", summary="组合Action的环境资源过滤接口",
             description=description(
                 "根据组合action配置的过滤信息，筛选出config中满足条件的对应deviceId，并注入到config中",
                 '文樟 10234064'))
async def composite_action_filter(compositeActionFilter: CompositeActionFilter):
    try:
        obj = ConfigGeneratorService.get_filtered_rlt(compositeActionFilter.config,
                                                      compositeActionFilter.stageId,
                                                      compositeActionFilter.taskId,
                                                      compositeActionFilter.recordId,
                                                      compositeActionFilter.caseId,
                                                      compositeActionFilter.envId)
        return success('success', data={FILTERS: obj.result})
    except Exception as e:
        return fail(f'{e}')
