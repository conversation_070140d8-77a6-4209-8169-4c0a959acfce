# -*- encoding: utf-8 -*-
"""
@Time    :   2023-8-24
<AUTHOR>   wen <PERSON>hang 10234064
@Desc    :   None
"""

from fastapi import APIRouter

from api.Response import success, fail
from api.endpoints.platform.env.Schemas import UpdateDeviceInfo, DeleteDeviceInfo
from domain.platform.ActionInfo import description
from domain.platform.env.VisualEdit import VisualEdit
from infrastructure.resource.domain.config.Models import DEVICE_SCHEMAS

router = APIRouter()


@router.get("/visual_edit/device_schemas", summary="环境可视化获取schema接口",
            description=description(
                "获取当前bizcore支持的环境配置模型以及对应的接口schema信息",
                '文樟 10234064'))
async def device_schemas():
    try:
        return success(data=DEVICE_SCHEMAS)
    except Exception as e:
        return fail(f'{e}')


@router.post("/visual_edit/update", summary="环境可视化设备更新接口",
             description=description(
                 "环境可视化设备更新接口，支持新增和修改，当device参数的ID值为空的时候，即新增，不为空的时候,是修改",
                 '文樟 10234064'))
async def update(udi: UpdateDeviceInfo):
    try:
        ve = VisualEdit(udi.config)
        ve.update_device(udi.device)
        # ConfigVerifier(ve.config).verify()
        return success(data=ve.config)
    except Exception as e:
        return fail(f'{e}')


@router.delete("/visual_edit/delete", summary="环境可视化设备删除接口",
               description=description(
                   "环境可视化设备删除接口，参数为需要删除的设备ID和config",
                   '文樟 10234064'))
async def delete(ddi: DeleteDeviceInfo):
    try:
        ve = VisualEdit(ddi.config)
        ve.delete_device(ddi.deviceId)
        # ConfigVerifier(ve.config).verify()
        return success(data=ve.config)
    except Exception as e:
        return fail(f'{e}')


if __name__ == '__main__':
    print('debug')

    # print(json.dumps(DEVICE_SCHEMAS, indent=4, ensure_ascii=False))
