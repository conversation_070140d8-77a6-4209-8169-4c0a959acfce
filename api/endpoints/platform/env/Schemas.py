# -*- encoding: utf-8 -*-
"""
@Time    :   2023-8-24
<AUTHOR>   wen zhang 10234064
@Desc    :   None
"""

from pydantic import BaseModel

from infrastructure.resource.service.DeviceManager import Device
from typing import List, Optional, Dict


class GetConfig(BaseModel):
    ermsId: str
    groupNo: str | None
    collect: bool = False
    historyConfig: dict = {}


class VerifyConfig(BaseModel):
    config: dict = {}


class DebugActionFilter(BaseModel):
    config: dict
    filters: dict
    stageId: str | None


class DebugActionFilterMany(BaseModel):
    debugActionFilterTree: Optional[List[Dict]] = None
    fullConfig: Optional[Dict] = None


class CompositeActionFilter(BaseModel):
    config: dict
    stageId: str | None
    taskId: str | None
    recordId: int | None
    caseId: str | None
    envId: str | None


class UpdateDeviceInfo(BaseModel):
    device: Device
    config: dict


class DeleteDeviceInfo(BaseModel):
    deviceId: str
    config: dict
