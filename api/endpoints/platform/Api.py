#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/5/23 14:21
# <AUTHOR> 10263601
import importlib
import inspect

import httpx
from fastapi import APIRouter

from api.Response import success, fail
from api.endpoints.platform.Schemas import ParaInfo, Type, Source

router = APIRouter()


@router.post("/action/para/query")
async def query_action_para(paraInfo: ParaInfo):
    try:
        if paraInfo.type == Type.url:
            if paraInfo.source == Source.tdl:
                url, params = paraInfo.url, paraInfo.params
                formatUrl = url.format(*params)
                async with httpx.AsyncClient(timeout=5, proxies={}) as client:
                    response = await client.get(url=formatUrl, timeout=5)
                    resp = response.json()
                    if resp["result"]:
                        return success(data=resp["data"])
                    else:
                        raise Exception(resp["failReason"])
        elif paraInfo.type == Type.func:
            context, params = paraInfo.context, paraInfo.params
            funcInfo = paraInfo.func.rsplit(".", 2)
            moduleName, className, funcName = funcInfo[0], funcInfo[1], funcInfo[2]
            _module = importlib.import_module(moduleName)
            _class = getattr(_module, className)
            func = getattr(_class(context), funcName)
            if inspect.iscoroutinefunction(func):
                return success(data=await func(*params))
            return success(data=func(*params))
    except Exception as err:
        return fail(msg=f"查询错误, err: {err}")
    return fail(msg="查询格式有误，请检查代码")
