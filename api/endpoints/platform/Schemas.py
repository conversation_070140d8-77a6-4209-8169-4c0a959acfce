"""
@File: Schemas.py
@Author: 许王禾子10333721
@Time: 2023/8/29 下午3:58
@License: Copyright 2022-2030
@Desc: None
"""
from enum import Enum

from pydantic import BaseModel


class TaskInfo(BaseModel):
    name: str


class Type(Enum):
    url = "url"
    func = "func"


class Source(Enum):
    tdl = "tdl"
    ids = "ids"


class ParaInfo(BaseModel):
    type: Type = None  # type, 代表数据获取类型, 例：url 代表用 get 请求, func 代表执行内部方法
    returnType: str = ""  # returnType, 返回给前端的类型
    viewType: str = ""  # viewType, 前端的展示类型
    url: str | None = None  # url, type为url时必填
    func: str | None = None  # func的路径，type为func时必填，默认通过类方法去调用func, type为url时必填
    context: dict | None = None
    source: Source | None = None  # source, 获取数据来源, 根据来源不同需要对数据进行不同的解封装处理，例：tdl 代表来源tdl
    params: list | None = None  # params, 可能用到的参数，如url中的路由参数
