import asyncio
import os
import platform
import subprocess
import sys

import psutil
from fastapi import APIRouter
from starlette.background import BackgroundTask
from starlette.responses import JSONResponse

from api.Response import success as successResponse
from domain.platform.slave.Slave import Slave
from infrastructure.utils.SlaveManager import SlaveManager

router = APIRouter()


@router.post("/slave/upgrade")
async def upgrade():
    s: Slave = Slave()
    if s.is_slave_upgrade():
        return JSONResponse(status_code=200, content=successResponse())

    async def cloud_slave_upgrade():
        slave: Slave = Slave()
        slave.set_status_upgrade()
        SlaveManager.upload()
        while True:
            await asyncio.sleep(1)
            if slave.get_task_num() <= 0:
                os.kill(os.getpid(), 9)

    async def local_slave_upgrade():
        if platform.system() == "Linux":
            raise Exception("暂时不支持本地linux机器自动升级")
        slave: Slave = Slave()
        slave.set_status_upgrade()
        SlaveManager.upload()
        file_path = os.path.abspath(sys.argv[0])
        bat_file = os.path.join(os.path.dirname(os.path.dirname(file_path)), "start_biz.bat")
        current_os_pid = psutil.Process().pid
        commands = f'taskkill /F /PID {current_os_pid} & {bat_file}'
        while True:
            await asyncio.sleep(0.5)
            if slave.get_task_num() <= 0:
                subprocess.call(commands, shell=True)

    if s.is_local():
        task = local_slave_upgrade
    else:
        task = cloud_slave_upgrade

    return JSONResponse(status_code=200, content=successResponse(),
                        background=BackgroundTask(task))


if __name__ == '__main__':
    current_file_path = os.path.abspath(sys.argv[0])
    restart_file = os.path.join(os.path.dirname(os.path.dirname(current_file_path)), "start_biz.bat")
    print(dir)
