#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/12/19 14:02
# <AUTHOR> 10255283

from typing import TypeVar
from enum import Enum
from typing import Annotated, List
from pydantic import BaseModel, Field
from typing_extensions import Literal

DataT = TypeVar('DataT')


class PhaseShifterFile(BaseModel):

    phasePath: DataT = Field(..., type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                        allowedFileType=".csv", limit=1, params=[])









