from fastapi import APIRouter, Request
from api.Response import fail, success
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from service.action.phaseshifter.PhaseShifterService import PhaseShifterService
from api.endpoints.action.phaseshifter.Schemas import PhaseShifterFile
from domain.factories.PhaseShifterFactory import PhaseShifterFactory

router = APIRouter(route_class=bizRoute)


@router.post("/loadphasefile", summary="导入可调移相器文件",
             description=description("导入可调移相器文件,目前需要可调移相器接入专网环境或者部署本地bizcode", "10263798"))
async def load_phaseshifter_phase_file(request: Request, paras: PhaseShifterFile):
    phaseShifters = PhaseShifterFactory.create(request)
    if not phaseShifters:
        return fail(msg=f"找不到可调移相器设备，请检查环境配置！！！")
    errList = []
    for phaseShifter in phaseShifters:
        msg = await PhaseShifterService().set_phase(phaseShifter, paras.phasePath[0])
        if msg is not None:
            errList.append(f"!!!可调移相器导入文件失败，原因是：{msg}！！！")
    if errList:
        return fail(msg=errList)
    return success(data="导入可调移相器文件成功!!")



