"""
@File: Schemas.py
@Author: 许王禾子10333721
@Time: 2024/1/8 上午11:11
@License: Copyright 2022-2030
@Desc: None
"""
from typing import TypeVar, Literal
from enum import Enum
from pydantic import BaseModel, Field, validator

DataT = TypeVar('DataT')  # 创建一个 TypeVar 的实例


ECHAOS_TEMP_JSON = {
        "fault_type": '8',
        "docker_name": "lccm-1-lccm-0-rcm",
        "product_name": "lccm",
        "sessionType": "LCCM_COMMON",
        "sessionInst": None,
        "time": None,
        "loss": None,
        "ruin": False,
        "msgId": "2",
        "offset": None,
        "size": None,
        "count": "10",
        "resendInterval": "10000000",
        "randompercent": None,
        "timeout": "300"
}

class EChaosTaskManage(BaseModel):
    func: Literal["查询进度", "等待完成"] = Field(default="查询进度", description='任务类型')


class EChaosFault(BaseModel):
    guidebook: DataT = Field(None, type="label", viewType="url", urlName="使用说明",
                             url="https://i.zte.com.cn/index/ispace/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/92d7fd489731408998cf551c4ee177ef/view")
    attrJson: DataT = Field(default=ECHAOS_TEMP_JSON,
                                type="jsonEditor", description='用户F12抓取到创建的ECHAOS任务时,选择场景模版，请求collectionSceneDetail与网管交互payload中moIds的属性')