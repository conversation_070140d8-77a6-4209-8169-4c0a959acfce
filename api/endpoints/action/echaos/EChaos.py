"""
@File: EChaos.py
@Author: 侯小飞10270755
@Time: 2025-05-14 19:02:46
@License: Copyright 2022-2030
@Desc: None
"""
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import success, fail
from api.endpoints.action.echaos.Schemas import EChaosFault, EChaosTaskManage
from domain.platform.ActionInfo import description
from domain.factories.MeFactory import MeFactory
from infrastructure.resource.service.Refs import Refs
from service.action.eChaos.EChaosService import EChaosService
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/echaos_inject_fault_comm", summary="ECHAOS注入故障",
             description=description("在混沌系统中注入故障,混沌系统地址:http://************:8080/inject(注意:请确保用户已登陆过混沌系统并已生成token)",
                                     "侯小飞10270755"))
async def echaos_inject_fault_comm(request: Request, paras: EChaosFault):
    mes = await MeFactory.create(request)
    mes = [me for me in mes if me.serviceType in ["5gnr", "lte"]]
    user = request.state.resource.get("user", "10270755")
    flag, result = True, {}
    for me in mes:
        try:
            ret, info = await EChaosService().echaos_inject_fault_comm(me, user, paras.attrJson)
            if not ret:
                flag = False
                info = f"注入故障失败,{info}"
        except Exception as err:
            info = f"注入故障执行异常,{err}"
        result.update({f"{me.meId}": info})
    if not flag:
        return fail(msg="ECHAOS注入故障失败", data=result)
    return success(msg="ECHAOS注入故障成功", data=result)


@router.post("/echaos_clean_fault", summary="ECHAOS清除注入故障",
             description=description("在混沌系统中,清除网元已注入故障,可通过依赖清除指定故障;如果依赖为空,清除网元所有已注入故障;"
                                     "混沌系统地址:http://************:8080/inject(注意:请确保用户已登陆过混沌系统并已生成token)", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "ECHAOS注入故障", "refPattern": "0-*"}]})
async def echaos_clean_fault(request: Request):
    tasks = await Refs(request).get_output()
    mes = await MeFactory.create(request)
    taskInfos = tasks if tasks else [{me.meId: "" for me in mes if me.serviceType in ["5gnr", "lte"]}]
    user = request.state.resource.get("user", "10270755")
    Logger.info(f"{user}清除网元已注入故障信息:", taskInfos)
    flag, result = True, {}
    for meTask in taskInfos:
        for meId, taskId in meTask.items():
            try:
                me = await MeFactory.create_by_meid(request, meId)
                ret, err = await EChaosService().echaos_clean_fault(me, user, taskId)
                if not ret:
                    flag = False
                    ret = f"清除注入故障失败:{err}"
            except Exception as err:
                flag = False
                ret = f"清除注入故障执行异常:{err}"
            result.update({f"{meId}_{taskId}": ret})
    if not flag:
        return fail(msg="ECHAOS清除注入故障失败", data=result)
    return success(msg="ECHAOS清除注入故障成功", data=result)



@router.post("/echaos_task_manage", summary="ECHAOS任务管理",
             description=description("在混沌系统中,管理注入的故障;混沌系统地址:http://************:8080/inject(注意:请确保用户已登陆过混沌系统并已生成token)",
                                     "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "ECHAOS注入故障", "refPattern": "1"}]})
async def echaos_task_manage(request: Request, paras:EChaosTaskManage):
    taskInfos = await Refs(request).get_output()
    user = request.state.resource.get("user", "10270755")
    Logger.info(f"{user}管理网元已注入故障信息:", taskInfos)
    flag, result = True, {}
    for meTask in taskInfos:
        for meId, taskId in meTask.items():
            try:
                ret = await EChaosService().echaos_manage_task(paras.func, user=user, task_id=taskId)
                if not ret:
                    flag = False
                    ret = f"{paras.func}失败:{ret}"
            except Exception as err:
                flag = False
                ret = f"{paras.func}执行异常:{err}"
            result.update({f"{meId}_{taskId}": ret})
    if not flag:
        return fail(msg="ECHAOS任务管理失败", data=result)
    return success(msg="ECHAOS任务管理成功", data=result)


