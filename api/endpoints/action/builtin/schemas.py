# -*- coding: utf-8 -*-
"""
@File: Schemas.py
@Author: 许王禾子10333721
@Time: 2023/9/19 下午4:21
@License: Copyright 2022-2030
@Desc: None
"""
from typing import Literal, TypeVar

from pydantic import BaseModel, Field, validator, root_validator

from domain.platform.ai.prompt_template import Templates, Outputs
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator

DataT = TypeVar('DataT')


class SleepInfo(BaseModel):
    hour: int = Field(0, description='小时')
    min: int = Field(0, description='分钟')
    sec: int = Field(0, description='秒')


class CalculateInfo(BaseModel):
    para1: str | None = Field("0", description='参数1, 默认为0')
    opt: Literal["加", "减", "乘以", "除以"] = Field(..., description='运算符')
    para2: str | None = Field("0", description='参数2, 默认为0')


class AIChat(BaseModel):
    data: str = Field("", description='待处理的数据')
    template: Literal['通用', '取值', 'Python', '正则表达式'] = Field(default='通用', description='prompt模板')
    prompt: DataT = Field(Templates.COMMON, type="jsonEditor", viewType="text", description='描述你的需求')
    output: DataT = Field(Outputs.COMMON, type="jsonEditor", viewType="text", description='指定输出结果,默认为res')
    model: Literal['zte', 'deepseek'] = Field('deepseek', description='ai模型')
    expose: bool = Field(True, description="是否暴露结果,暴露后可被依赖", ifEmptyUseDefault=False)

    @validator('data', pre=True, always=False)
    def to_str(cls, v):
        return str(v)

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        expose_field = cls.__fields__["expose"]
        if not values.get("expose"):
            if not expose_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["expose"] = False
        return values

    @classmethod
    def schema(cls):
        dependency_dict = {
            "template": {
                "通用": [
                    {"prompt": Templates.COMMON},
                    {"output": Outputs.COMMON}
                ],
                "取值": [
                    {"prompt": Templates.GET_VALUE},
                    {"output": Outputs.GET_VALUE},
                ],
                "Python": [
                    {"prompt": Templates.PYTHON},
                    {"output": Outputs.PYTHON},
                ],
                "正则表达式": [
                    {"prompt": Templates.REGEX},
                    {"output": Outputs.REGEX},
                ],
            }
        }
        return SchemaGenerator().generate_basic_notime_schema(super().schema(), dependency_dict)


AIChat.schema()

HELLO_LOW_CODE = """LOGGER.debug('Hello LowCode!')
result = {'code':0,'data':{},'message':'Executive success! Deconstruct everything!'}

# 写入action结果
RESULT.set_result(result)

# 获取依赖数据
# refsData = SELF.refs[0]
# 
# 设置action的状态，可实现断言
# RESULT.set_status("失败")

# 设置参数，可被后续action调用
# 重新设置会覆盖已设置的
# SELF.set_paras({"paraName":"paraValue"})
# 添加参数，不会覆盖已设置的
# SELF.add_paras({"paraName":"paraValue"})

# 更多功能，请点击参数lowCodeDocs了解
"""


class LowCodePara(BaseModel):
    lowCodeDocs: DataT = Field(None, type="label", viewType="url", urlName="低代码action文档",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/"
                                   "8156fd581919413fb69adccf418e53f6/view")
    code: DataT | None = Field(HELLO_LOW_CODE, type="jsonEditor", viewType="text",
                               description='用户代码块, 默认为pass', )


class ForInLoop(BaseModel):
    variableAlias: str = Field("i", description="用户自定义的遍历元素的变量名，默认为i")
    iterable: DataT = Field(..., type="jsonEditor", description="待遍历的可迭代对象，如字典，列表，元组")


class ForInRangeLoop(BaseModel):
    variableAlias: str = Field("i", description="用户自定义的遍历元素的变量名，默认为 i")
    start: int = Field(0, description="循环范围起始值，非必填，默认为0")
    end: int = Field(..., description="循环范围终止值，必填")
    step: int = Field(1, description="步长，非必填，默认为1, 不可为0")

    @validator("step", pre=True)
    def check_step(cls, value):
        if value == 0:
            raise ValueError("步长(step) 不能为0。arg: step must not be zero")
        return value


class ForEnumLoop(BaseModel):
    indexAlias: str = Field("index", description="用户自定义遍历元素的索引名，默认为index")
    valueAlias: str = Field("value", description="用户自定义遍历元素的变量名，默认为value")
    iterable: DataT = Field(..., type="jsonEditor", description="待遍历的可迭代对象，如字典，列表，元组")
