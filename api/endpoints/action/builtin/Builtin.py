"""
@File: Builtin.py
@Author: 许王禾子10333721
@Time: 2023/9/19 下午4:21
@License: Copyright 2022-2030
@Desc: None
"""
import asyncio
import traceback

from fastapi import APIRouter, Request

from api.Response import success, fail
from api.endpoints.action.builtin.schemas import SleepInfo, CalculateInfo, LowCodePara, AIChat, ForInRangeLoop, \
    ForInLoop, ForEnumLoop
from api.route.BizRoute import bizRoute
from config.CommonCfg import HAS_NEXT
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from infrastructure.utils.DataDealer import str_to_float
from infrastructure.utils.Exceptions import ExceededMaximumLoopError
from infrastructure.utils.biz_core.ContextModifier import ContextModifier
from service.platform.ai_chat.AIChatService import AIChatService
from service.platform.for_loop.ForLoopService import ForLoopService
from service.platform.low_code.LowCodeActionService import LowCodeActionService

router = APIRouter(route_class=bizRoute, tags=["builtin"])


@router.post(path="/manual", summary="人工介入action",
             description=description(feature="人工介入action，测试用", employee_id="许王禾子 10333721"),
             openapi_extra={"category": "builtin", "function": "Manual"})
async def manual():
    return success()


@router.post(path="/sleep", summary="等待",
             description=description(feature="等待相应时间", employee_id="许王禾子 10333721"),
             openapi_extra={"category": "builtin", "function": "Sleep"})
async def sleep(para: SleepInfo):
    totalSecs = para.sec + 60 * para.min + 3600 * para.hour
    if totalSecs < 1:
        return success()
    Logger.info(f"start sleep {totalSecs}s")
    stepSec = int(totalSecs / 10) if totalSecs >= 10 else 1
    for i in range(totalSecs):
        if i % stepSec == 0:
            Logger.info(f"sleeping... {totalSecs - i}s left")
            await asyncio.sleep(stepSec)
    Logger.info(f"finish sleep")
    return success()


@router.post(path="/action_note", summary="注释",
             description=description(feature="注释用途，无实际逻辑", employee_id="文樟 10234064"),
             openapi_extra={"category": "builtin", "function": "action_note"})
async def action_note():
    return success()


@router.post(path="/calculate", summary="计算",
             description=description(feature="简单计算", employee_id="许王禾子 10333721"),
             openapi_extra={"refs": [{"actionName": "计算", "refPattern": "0-2"}],
                            "category": "builtin", "function": "Calculate"})
async def calculate(request: Request, para: CalculateInfo):
    paras = await Refs(request).get_output()
    if len(paras) == 2:
        para1 = paras[0].get("res")
        para2 = paras[1].get("res")
    elif len(paras) == 1:
        para1 = paras[0].get("res")
        para2 = str_to_float(para.para2)
    else:
        para1 = str_to_float(para.para1)
        para2 = str_to_float(para.para2)
    res = 0
    match para.opt:
        case "加":
            res = para1 + para2
        case "减":
            res = para1 - para2
        case "乘以":
            res = para1 * para2
        case "除以":
            if para.para2 != 0:
                res = para1 / para2
            else:
                return fail(msg="除数不能为零")
    data = {"res": res}
    return success(data=data)


@router.post(path="/ai/chat", summary="ai",
             description=description(feature="使用ai大模型", employee_id="许王禾子 10333721"),
             openapi_extra={"refs": [{"refPattern": "0-*"}], "category": "builtin", "function": "AI"})
async def ai_chat(request: Request, chat: AIChat):
    data = str(chat.data)
    res, err = AIChatService.chat(chat.model, chat.template, chat.prompt, chat.output, data)

    if err:
        return fail(msg=err)

    if chat.expose:
        if res and type(res) == dict:  # 返回值不为空，且为字典
            ContextModifier.set_paras(request=request, paras=res)

    return success(data=res)


@router.post(path="/lowCode", summary="低代码",
             description=description(
                 feature="""执行自定义代码块。\n可用方法：
                 调用'RESULT.set_result()'方法，写入处理结果；
                 调用'SELF.refs'获取依赖action的结果；
                 可调用日志方法：["LOGGER.debug()", "LOGGER.info()", "LOGGER.warn()", "LOGGER.error()"]。
                 更多使用方法见文档链接~""",
                 employee_id="10333573"),
             openapi_extra={"refs": [{"refPattern": "0-*"}], "category": "builtin", "function": "LowCode"})
async def lowCode(request: Request, para: LowCodePara):
    try:
        ans = await LowCodeActionService().run(para.code, request=request)
        if ans.code != 0:
            return fail(msg=ans.message, data=ans.data)
        return success(msg=ans.message, data=ans.data)
    except Exception as _:
        return fail(msg=traceback.format_exc(10))


@router.post(path="/forInLoop", summary=": FOR IN 遍历",
             description=description(
                 feature="""For遍历可迭代对象。
                 入参：variableAlias为用户自定义的遍历元素的变量名，默认为 i；
                 入参：iterable为待遍历的可迭代对象。
                 后续action可通过参数引用或者参数替换的方式使用出参作为局部变量，仅可被循环体内的action所依赖""",
                 employee_id="10288354"),
             openapi_extra={"category": "builtin", "function": "For"})
async def forInLoop(request: Request, para: ForInLoop):
    try:
        stageId = request.state.resource.get("pipelineStageId")
        recordId = request.state.resource.get("recordId")
        Logger.info(
            f"========开始For遍历执行，遍历元素：{para.iterable}，recordId：{recordId}========")
        iterIndex, iterValue, hasNext = ForLoopService.basic_loop(stageId, recordId, para.iterable)
        loopValue = {para.variableAlias: iterValue}
        ContextModifier.set_paras(request=request, paras=loopValue)
        outputData = {para.variableAlias: iterValue, HAS_NEXT: hasNext, "iterIndex": iterIndex}
        Logger.info(f"--------当前遍历信息：{outputData}--------")
        return success(data=outputData, msg="遍历中")
    except ValueError as _:
        ContextModifier.set_paras(request=request, paras={para.variableAlias: None})
        msg = f"遍历元素为空，循环条件未触发执行！遍历元素：{para.iterable}"
        return success(data={para.variableAlias: None, HAS_NEXT: False}, msg=msg)
    except StopIteration as _:
        Logger.info(f"--------本次循环遍历已终止--------")
        ContextModifier.set_paras(request=request, paras={para.variableAlias: None})
        return success(data={para.variableAlias: None, HAS_NEXT: False}, msg="遍历已结束")
    except Exception as e:
        Logger.error(f"--------本次循环遍历意外中止，错误信息：{e}--------")
        return fail(msg=traceback.format_exc(10))


@router.post(path="/forInRangeLoop", summary=": FOR RANGE 循环",
             description=description(
                 feature="""For范围循环。
                 入参：variableAlias为用户自定义的遍历元素的变量名，默认为 i；
                 入参：step/end/step为range遍历范围和步长。
                 后续action可通过参数引用或者参数替换的方式使用出参作为局部变量，仅可被循环体内的action所依赖""",
                 employee_id="10288354"),
             openapi_extra={"category": "builtin", "function": "For"})
async def forInRangeLoop(request: Request, para: ForInRangeLoop):
    try:
        stageId = request.state.resource.get("pipelineStageId")
        recordId = request.state.resource.get("recordId")
        Logger.info(
            f"========开始For循环执行，元素别名：{para.variableAlias}，起始值：{para.start}，终止值：{para.end}，"
            f"步长：{para.step}，recordId：{recordId}========")
        iterValue, hasNext = ForLoopService.range_loop(stageId, recordId, para.variableAlias,
                                                       para.start, para.end, para.step)
        loopValue = {para.variableAlias: iterValue}
        ContextModifier.set_paras(request=request, paras=loopValue)
        outputData = {para.variableAlias: iterValue, HAS_NEXT: hasNext}
        Logger.info(f"--------当前循环信息：{outputData}--------")
        return success(data=outputData, msg=get_msg(hasNext, _type="循环"))
    except StopIteration as e:
        msg = f"循环条件未触发执行，{e}！起始值：{para.start}，终止值：{para.end}，步长：{para.step}。"
        Logger.info(f"--------{msg}--------")
        ContextModifier.set_paras(request=request, paras={para.variableAlias: para.start})
        return success(data={para.variableAlias: para.start, HAS_NEXT: False}, msg=msg)
    except ExceededMaximumLoopError as e:
        Logger.error(f"--------本次循环意外中止，错误信息：{e}--------")
        return fail(msg=e)
    except Exception as e:
        Logger.error(f"--------本次循环意外中止，错误信息：{e}--------")
        return fail(msg=traceback.format_exc(10))


@router.post(path="/forInEnumLoop", summary=": FOR ENUM 枚举",
             description=description(
                 feature="""For枚举可迭代对象。
                 入参：indexAlias为用户自定义遍历元素的索引名，默认为 index；
                 入参：valueAlias为用户自定义遍历元素的变量名，默认为 value；
                 入参：iterable为待遍历的可迭代对象。
                 后续action可通过参数引用或者参数替换的方式使用出参作为局部变量，仅可被循环体内的action所依赖""",
                 employee_id="10288354"),
             openapi_extra={"category": "builtin", "function": "For"})
async def forInEnumLoop(request: Request, para: ForEnumLoop):
    try:
        stageId = request.state.resource.get("pipelineStageId")
        recordId = request.state.resource.get("recordId")
        Logger.info(
            f"========开始For枚举执行，枚举元素别名：{para.indexAlias}，枚举元素：{para.iterable}, recordId: {recordId}========")
        iterIndex, iterValue, hasNext = ForLoopService.enum_loop(stageId, recordId, para.iterable, para.indexAlias)
        loopValue = {para.indexAlias: iterIndex, para.valueAlias: iterValue}
        ContextModifier.set_paras(request=request, paras=loopValue)
        outputData = {para.indexAlias: iterIndex, para.valueAlias: iterValue, HAS_NEXT: hasNext}
        Logger.info(f"--------当前枚举信息：{outputData}--------")
        return success(data=outputData, msg="枚举中")
    except ValueError as _:
        ContextModifier.set_paras(request=request, paras={para.indexAlias: -1, para.valueAlias: None})
        msg = f"枚举列表为空，循环条件未触发！枚举元素：{para.iterable}"
        return success(data={para.indexAlias: -1, para.valueAlias: None, HAS_NEXT: False}, msg=msg)
    except StopIteration as curr_iter_index:
        Logger.info(f"--------本次枚举已结束--------")
        paras = {para.indexAlias: int(str(curr_iter_index)), para.valueAlias: None}
        ContextModifier.set_paras(request=request, paras=paras)
        return success(data={HAS_NEXT: False, **paras}, msg="枚举已结束")
    except Exception as e:
        Logger.error(f"--------本次枚举意外中止，错误信息：{e}--------")
        return fail(msg=traceback.format_exc(10))


def get_msg(hasNext: bool, _type: str):
    if hasNext:
        return f"{_type}中"
    return f"{_type}已结束"
