import logging
import traceback

from api.Response import fail, async_success
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from fastapi import APIRouter, Request
from service.action.utility.trigger_by_irun.IRunService import IRunService
from api.endpoints.action.utility.trigger_irun_by_case.Schemas import IRunConf

router = APIRouter(route_class=bizRoute)


@router.post("/trigger_irun", summary="irun执行用例",
             description=description('通过irun执行用例', '夏晨10242158'))
def trigger_irun_handler(request: Request, irun_conf: IRunConf):
    resource = request.state.resource
    callback_info = {
        "pipelineStageId": resource.get("pipelineStageId"),
        "recordId": resource.get("recordId"),
        "envId": resource.get("envId"),
        "taskId": resource.get("taskId"),
        "actionName": resource.get("actionName"),
        "user": resource.get("user"),
        "caseId": resource.get("caseId"),
        "auth_value": resource.get("token"),
        "execType": resource.get("execType"),
        "systemId": resource.get("systemId"),
        "taskName": resource.get("taskName"),
        "pipelineUuid": resource.get("pipelineUuid"),
        "isUpdateScripts": irun_conf.isUpdateScripts
    }
    try:
        result = IRunService().execute_text_case(callback_info)
    except Exception as err:
        logging.error(traceback.format_exc())
        return fail(msg=str(err))
    if result.get('code') != '0000':
        return fail(msg=str(result.get("msg")))
    return async_success("ok")
