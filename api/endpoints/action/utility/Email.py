#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/7/12 15:45
# <AUTHOR> 10263601
from fastapi import APIRouter, Request
from pydantic import BaseModel, Field

from api.Response import fail, success
from api.route.BizRoute import bizRoute
from service.action.utility.EmailService import EmailService

router = APIRouter(route_class=bizRoute)


class EmailDelivery(BaseModel):
    sender: str
    subject: str
    content: str
    receivers: str


@router.post("/send", summary="发送邮件", description="仅调试接口使用")
def send_email(emailDelivery: EmailDelivery, request: Request):
    emailDelivery.content = "111" + str(request.state.resource.get("config"))
    ret = EmailService.send_email(**emailDelivery.dict())
    if ret:
        return success(ret)
    return fail(ret)
