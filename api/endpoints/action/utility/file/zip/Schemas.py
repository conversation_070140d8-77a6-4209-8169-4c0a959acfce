# -*- encoding: utf-8 -*-
"""
@File    :   Schemas.py
@Time    :   2025/05/07 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import json

from pydantic import BaseModel, Field
from typing import TypeVar

DataT = TypeVar('DataT')



class ZipFiles(BaseModel):
    filePaths: DataT = Field(..., type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="300",
                           allowedFileType="*.*", params=[],
                           description="上传的压缩目标文件列表或者或者通过低代码引用传入压缩目录")


class UnZipFile(BaseModel):
    filePath: DataT = Field(..., type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="300",
                           allowedFileType="*.zip;*.ZIP", limit=1, params=[],
                           description="上传zip压缩文件或者参数引用产物链接")
    pat: str  = Field('*.*', description="通过入参pat传入过滤条件,找出dir_path 文件夹中所有复合pat条件的文件,比如 pat = '*.txt'")



