# -*- encoding: utf-8 -*-
"""
@File    :   Zip.py
@Time    :   2023/08/24 10:47:44
<AUTHOR>   侯小飞10270755,李双红10258068
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import traceback

from fastapi import APIRouter, Request
from api.Response import fail, success
from api.endpoints.action.utility.file.zip.Schemas import ZipFiles, UnZipFile
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from service.action.utility.file.zip.ZipService import ZipService
from infrastructure.resource.service.Refs import Refs
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/zip_files", summary="ZIP压缩文件",
             description=description("ZIP压缩文件,选择文件列表或者参数级联输入文件目录", "侯小飞10270755"))
async def zip_files(paras: ZipFiles):
    try:
        path, url = await ZipService.zip_files(paras.filePaths)
        return success(msg="ZIP压缩文件成功", data={"filePath": path, "fileUrl": url})
    except BaseException as err:
        Logger.error(f"ZIP压缩文件异常,详情:", traceback.format_exc())
        return fail(msg=f'ZIP压缩文件失败,{err}')


@router.post("/get_from_zip", summary="ZIP解压缩文件",
             description=description("ZIP解压缩文件,选择zip文件解压,并获取文件大小信息", "侯小飞10270755"))
async def get_from_zip(paras: UnZipFile):
    filePath = paras.filePath[0] if isinstance(paras.filePath, list) else paras.filePath
    try:
        dirPath, result = await ZipService.get_files_from_zip(filePath, paras.pat)
        return success(msg="ZIP解压缩文件成功", data={"filePath": dirPath, "data": result})
    except BaseException as err:
        Logger.error(f"ZIP解压缩文件异常,详情:", traceback.format_exc())
        return fail(msg=f'ZIP解压缩文件失败,{err}')


