# -*- encoding: utf-8 -*-
"""
@File    :   Excel.py
@Time    :   2023/08/24 10:47:44
<AUTHOR>   侯小飞10270755,李双红10258068
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import traceback

from fastapi import APIRouter, Request
from api.Response import fail, success
from api.endpoints.action.utility.file.excel.Schemas import ExcelReadBlock, ExcelDelRowCols, ExcelWriteBlock
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from service.action.utility.file.excel.ExcelService import ExcelService
from infrastructure.resource.service.Refs import Refs
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/read_excel_block", summary="Excel读取区块数据",
             description=description("Excel读区块数据,根据输入信息读取区块数据", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "Excel.*", "path": "/v1/api/action/utility/file/excel", "refPattern": "0-1"}]})
async def read_excel_block(request: Request, paras: ExcelReadBlock):
    filePath = await Refs(request).get_output("filePath") or paras.filePath
    if not filePath:return fail(msg=f'资源选择依赖为空或者界面filePath输入为空,请确认!!!')
    try:
        file, result = await ExcelService.read_block(filePath[0], paras.sheetName, paras.startRow, paras.endRow, paras.startCol, paras.endCol, paras.showMode)
        return success(msg="Excel读取区块数据成功", data={"filePath": file, "data": result})
    except BaseException as err:
        Logger.error(f"Excel读取区块数据异常,详情:", traceback.format_exc())
        return fail(msg=f'Excel读取区块数据失败,{err}')


@router.post("/del_excel_row_or_col", summary="Excel删除行列",
             description=description("Excel删除行列,可实现删除指定行或者指定列,不指定时不删除", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "Excel.*", "path": "/v1/api/action/utility/file/excel", "refPattern": "0-1"}]})
async def del_excel_row_or_col(request: Request, paras: ExcelDelRowCols):
    filePath = await Refs(request).get_output("filePath") or paras.filePath
    if not filePath: return fail(msg=f'资源选择依赖为空或者界面filePath输入为空,请确认!!!')
    try:
        file, result = await ExcelService.del_rows_or_cols(filePath[0], paras.sheetName, paras.row, paras.col, paras.amount, paras.isUpload)
        return success(msg="Excel删除行列成功", data={"filePath": file, "fileUrl": result})
    except BaseException as err:
        Logger.error(f"Excel删除行列异常,详情:", traceback.format_exc())
        return fail(msg=f'Excel删除行列失败,{err}')


@router.post("/write_excel_block", summary="Excel写入数据块",
             description=description("Excel写入数据块,支持覆盖写入、右侧插入、下方插入数据块", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "Excel.*", "path": "/v1/api/action/utility/file/excel", "refPattern": "0-1"}]})
async def write_excel_block(request: Request, paras: ExcelWriteBlock):
    filePath = await Refs(request).get_output("filePath") or paras.filePath
    if not filePath: return fail(msg=f'资源选择依赖为空或者界面filePath输入为空,请确认!!!')
    try:
        file, result = await ExcelService.write_block(filePath[0], paras.sheetName, paras.startRow, paras.startCol,
                                                      paras.dataList, paras.writeMode, paras.isUpload)
        return success(msg="Excel写入数据块成功", data={"filePath": file, "fileUrl": result})
    except BaseException as err:
        Logger.error(f"Excel写入数据块异常,详情:", traceback.format_exc())
        return fail(msg=f'Excel写入数据块失败,{err}')


