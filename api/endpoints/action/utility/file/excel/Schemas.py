# -*- encoding: utf-8 -*-
"""
@File    :   Schemas.py
@Time    :   2025/05/07 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""


from pydantic import BaseModel, Field
from typing import Literal, TypeVar

DataT = TypeVar('DataT')


TEMP_DATA = [["1", "2"],["3", None, "5"]]

class ExcelReadBlock(BaseModel):
    filePath: DataT | None = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="300",
                           allowedFileType="*.xlsx;*.xlsm", limit=1, params=[],
                           description="上传Excel文件或者通过参数引用或者依赖Excel读、写、删获取文件名,优先使用依赖的文件")
    sheetName: str | None = Field(None, description="读区块的行号sheet页名称,不输入时使用默认第一页")
    startRow: str = Field("1", description='读区块的首行号,默认1,表示第一行(从1开始算),支持过滤条件:\'1=="18044" and 2=="VBP_1_8"\','
                                         '表示第一列等于18044且第二列等于VBP_1_8,满足条件的第一行')
    endRow: str = Field(None, description='读区块的末尾行号,默认不输入,表示最后一行,支持过滤条件:\'1=="18044" and 2=="VSW_1_1"\','
                                         '表示第一列等于18044且第二列等于VSW_1_1,满足条件的第一行')
    startCol: str = Field("1", description='读区块的首列号,默认1,表示第一行(从1开始算),支持过滤条件:\'2=="functionMode"\','
                                         '表示第二行等于functionMode,满足条件的第一列')
    endCol: str = Field(None, description='读区块的末尾列号,默认不输入,表示最后一列,支持过滤条件:\'2=="vswPortInfo"\','
                                         '表示第二行等于vswPortInfo,满足条件的第一列')
    showMode: Literal["按行展示", "按列展示"] = Field("按行展示", description="数据展示模式,默认'按行展示',选择'按列展示'会转置数据")


class ExcelDelRowCols(BaseModel):
    filePath: DataT | None = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="300",
                           allowedFileType="*.xlsx;*.xlsm", limit=1, params=[],
                           description="上传Excel文件或者通过参数引用或者依赖Excel读、写、删获取文件名,优先使用依赖的文件")
    sheetName: str | None = Field(None, description="读区块的行号sheet页名称,不输入时使用默认第一页")
    row: str = Field(None, description='删除行号,默认不输入,表示不删除行,支持过滤条件:\'1=="18044" and 2=="VBP_1_8"\','
                                         '表示第一列等于18044且第二列等于VBP_1_8,满足条件的第一行')
    col: str = Field(None, description='删除列号,默认不输入,表示不删除列,支持过滤条件:\'2=="functionMode"\','
                                         '表示第二行等于functionMode,满足条件的第一列')
    amount: int = Field(1, description="连续删除行或者列数,默认删除1行/列")
    isUpload: bool = Field(False, description="是否上传产物,默认不上传,在最后一步修改时上传产物")


class ExcelWriteBlock(BaseModel):
    filePath: DataT | None = Field(None, type="uploadFile", returnType="array", viewType="uploadFile",
                                       fileMaxSize="300",
                                       allowedFileType="*.xlsx;*.xlsm", limit=1, params=[],
                                       description="上传Excel文件或者通过参数引用或者依赖Excel读、写、删获取文件名,优先使用依赖的文件")
    sheetName: str | None = Field(None, description="读区块的行号sheet页名称,不输入时使用默认第一页")
    startRow: str = Field(None, description='写入数据行号,用于选中单元格,默认不输入,表示在最后一行写入,支持过滤条件:\'1=="18044" and 2=="VBP_1_8"\','
                                         '表示第一列等于18044且第二列等于VBP_1_8,满足条件的第一行')
    startCol: str = Field(None, description='写入数据列号,默认不输入,表示不删除列,表示在最后一列写入,支持过滤条件:\'2=="functionMode"\','
                                         '表示第二行等于functionMode,满足条件的第一列')
    writeMode: Literal["覆盖写入", "右侧插入", "下方插入"] = Field("覆盖写入", description="数据块写入模式,默认'覆盖写入',表示从选中单元格开始写入数据块'"
                                                              "'右侧插入',表示在选中单元格右侧插入数据块,原数据右移;'下方插入',表示在选中单元格下方插入数据块,原数据下移")
    dataList: DataT = Field(TEMP_DATA, type="jsonEditor",
                         description='写入的数据块,2维列表,用null表示写入空,举例:[["1", "2"],["3", null, "5"]]')
    isUpload: bool = Field(False, description="是否上传产物,默认不上传,在最后一步修改时上传产物")
