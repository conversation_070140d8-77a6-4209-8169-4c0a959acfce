#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/5/23 14:21
# <AUTHOR> 10263601
from typing import TypeVar

from fastapi import APIRouter, UploadFile
from pydantic import BaseModel

from api.Response import success
from api.route.BizRoute import bizRoute

router = APIRouter()

# DataT = TypeVar('DataT')  # 创建一个 TypeVar 的实例
#
#
# class Response(BaseModel):  # 定义一个泛型模型
#     data: DataT = {"type": "url", "returnType": "array", "viewType": "tree",
#                    "params": [], "url": "https://zxmte.zte.com.cn:3303/tdl/other/sftp_dir_tree"}
#     b: str = "123"
#
#
# @router.post("/create", summary="多次交互场景schema", description="仅调试接口使用")
# def create_task(res: Response):
#     ret = "hello world"
#     return success(ret)


@router.post("/uploadFile", summary="文件上传测试 (待开发)", description="仅用于附件上传测试")
def upload_file(file: UploadFile):
    return success(data={"filename": file.filename, "filesize": file.size})
