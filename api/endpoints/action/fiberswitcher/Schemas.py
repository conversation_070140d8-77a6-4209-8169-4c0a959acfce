#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/3/25 14:02
# <AUTHOR> 10255283
from enum import Enum
from typing import Annotated, List
from pydantic import BaseModel, Field
from typing_extensions import Literal
from typing import Literal, TypeVar
DataT = TypeVar('DataT')

class Connect(BaseModel):
    fiberswitcherConfigUrl: DataT = Field(None, type="label", viewType="url", urlName="光交换机配置使用指南",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/525dabcc806b4055ad5988e67024fc25/view")
    inPort: str = Field(None, description="光衰端口入口，如'3.3.3',不填以config端口为默认参数，建议配置config")
    outPort: str = Field(None, description="光衰端口出口，如'2.8.3',不填以config端口为默认参数，建议配置config")

class delConnect(BaseModel):
    fiberswitcherConfigUrl: DataT = Field(None, type="label", viewType="url", urlName="光交换机配置使用指南",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/525dabcc806b4055ad5988e67024fc25/view")
    inPort: str = Field(None, description="光衰端口入口，如'3.3.3',不填以config端口为默认参数，建议配置config")
    outPort: str = Field(None, description="光衰端口出口，如'2.8.3',不填以config端口为默认参数，建议配置config")

class getCrossPower(BaseModel):
    fiberswitcherConfigUrl: DataT = Field(None, type="label", viewType="url", urlName="光交换机配置使用指南",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/525dabcc806b4055ad5988e67024fc25/view")
    inPort: str = Field(None, description="光衰端口，如'3.3.3',不填以config端口为默认参数，建议配置config")






