from fastapi import APIRouter, Request
from api.Response import fail, success
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from domain.factories.FibSFactory import FibSFactory
from infrastructure.device.fiberswitcher.FiberSwitcher import FiberSwitcher
from api.endpoints.action.fiberswitcher.Schemas import getCrossPower, Connect, delConnect

router = APIRouter(route_class=bizRoute)


@router.post("/connect", summary="光交换机连接",
             description=description(
                 "光交换机的连接，如果inPort不输入，需要在配置设备端口link，aau和vbp与fiberswitcher之间link，具体方法见fiberswitcherConfigUrl光交换机配置使用指南",
                 "杨先恒10255283"))
async def fiberswitcher_connect(request: Request, paras: Connect):
    fiberswitchers = FibSFactory.create(request)
    err = []
    info = []
    for fib in fiberswitchers:
        if not paras.inPort and not fib["port"]:
            return fail(data=err, msg="参数inPort和config里的与FIBERSWITCHER link都未配置，请至少选其中一个进行配置")
        inport = paras.inPort if paras.inPort else fib["port"][0]
        outport = paras.outPort if paras.outPort else fib["port"][1]
        msg = FiberSwitcher(fib).batch_add_connect(inport, outport)
        if msg:
            info.append([inport, outport])
        else:
            err.append([inport, outport])
    if err:
        return fail(data=err, msg="端口设置失败")
    return success(data=info, msg="所有交换机设置成功")


@router.post("/del_connect", summary="光交换机断开",
             description=description(
                 "光交换机断开，如果inPort不输入，需要在配置设备端口link，aau和vbp与fiberswitcher之间link，具体方法见fiberswitcherConfigUrl光交换机配置使用指南",
                 "杨先恒10255283"))
async def fiberswitcher_del_connect(request: Request, paras: delConnect):
    fiberswitchers = FibSFactory.create(request)
    err = []
    info = []
    for fib in fiberswitchers:
        if not paras.inPort and not fib["port"]:
            return fail(data=err, msg="参数inPort和config里的与FIBERSWITCHER link都未配置，请至少选其中一个进行配置")
        inport = paras.inPort if paras.inPort else fib["port"][0]
        outport = paras.outPort if paras.outPort else fib["port"][1]
        msg = FiberSwitcher(fib).del_connect(inport, outport)
        if msg:
            info.append([inport, outport])
        else:
            err.append([inport, outport])
    if err:
        return fail(data=err, msg="端口设置失败")
    return success(data=info, msg="所有交换机设置成功")


@router.post("/get_fiber_switch", summary="查询光衰信息",
             description=description(
                 "查询光衰信息，如果inPort不输入，需要在配置设备端口link，aau和vbp与fiberswitcher之间link，具体方法见fiberswitcherConfigUrl光交换机配置使用指南",
                 "杨先恒10255283"))
async def get_fiber_switch_info(request: Request, paras: getCrossPower):
    fiberswitchers = FibSFactory.create(request)
    err = []
    info = []
    for fib in fiberswitchers:
        if not paras.inPort and not fib["port"]:
            return fail(data=err, msg="参数inPort和config里的与FIBERSWITCHER link都未配置，请至少选其中一个进行配置")
        port = paras.inPort if paras.inPort else fib["port"][0]
        res = FiberSwitcher(fib).get_cross_power_detail(port)
        if res:
            info.append(res)
        else:
            err.append(port)
    if err:
        return fail(data=err, msg="查询光衰失败")
    return success(data=info, msg="查询光衰成功")
