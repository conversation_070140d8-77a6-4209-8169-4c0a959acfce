# -*- encoding: utf-8 -*-
'''
@File    :   Lmt.py
@Time    :   2024/05/24 15:57:44
<AUTHOR>   岳昊冉10263798
@Version :   1.0
@Contact :   <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import traceback

from infrastructure.logger import Logger
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.factories.UeFactory import UeFactory
from domain.platform.ActionInfo import description


router = APIRouter(route_class=bizRoute)


@router.post("/reboot_lmt", summary="重启lmt", description=description("重启lmt软件", "岳昊冉10263798"))
async def reboot_lmt(request: Request):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            if ue.ueType not in ['CPE', 'TUE']:
                ueRltDict.update({ue.id: '没有lmt'})
                continue
            rlt = await ue.reboot_lmt()
            ueRltDict.update({ue.id: 'lmt重启成功'})
            if not rlt:
                return fail(f'该UE: {repr(ue.id)}，执行失败', data=rlt)
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))