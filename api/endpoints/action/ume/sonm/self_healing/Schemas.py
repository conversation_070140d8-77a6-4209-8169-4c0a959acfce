from datetime import datetime
from enum import Enum
from typing import TypeVar, Optional, Literal

from pydantic import BaseModel, Field, validator

from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")


class ScenesEnum(str, Enum):
    sleepingCell = "睡眠小区"
    outOfServiceCell = "退服小区"
    degradedCell = "性能下降小区"
    selfInspectionOfSleepingCell = "睡眠小区自检"
    selfHealingLog = "平台自愈日志"
    selfHealingForm = "平台自愈报告"


class SelfHealingPara(BaseModel):
    radioMode: Literal["NR", "LTE ITRAN", "PLAT", "NB-IoT ITRAN", "LTE SDR", "NB-IoT SDR"] = Field("NR",
                                                                                                   description='选择制式，默认为NR')

    scenes: list[ScenesEnum] = Field([ScenesEnum.sleepingCell, ScenesEnum.outOfServiceCell, ScenesEnum.degradedCell,
                                      ScenesEnum.selfInspectionOfSleepingCell], description='选择场景,默认是小区自愈的4个场景，请根据制式实际对应的选项选择')
    reportTimeType: Literal["今天", "全部", "自定义"] = Field("今天", description='选择上报时间，默认为今天')
    startTime: Optional[datetime] | None
    endTime: Optional[datetime] | None

    @validator("radioMode")
    def validate_radioMode(cls, radioMode):
        l = ""
        if not radioMode:
            return l
        if radioMode == 'NR':
            return "shScenes"
        elif radioMode == 'LTE ITRAN':
            return "shScenes"
        elif radioMode == 'PLAT':
            return "schScenes"
        elif radioMode == 'NB-IoT ITRAN':
            return "shScenes"
        elif radioMode == 'LTE SDR':
            return "scenes"
        elif radioMode == 'NB-IoT SDR':
            return "scenes"

    @validator("scenes")
    def validate_scenes(cls, scenes):
        l = []
        if not scenes:
            return l
        for scene in scenes:
            if scene == '睡眠小区':
                s = "0"
            elif scene == '退服小区':
                s = "1"
            elif scene == '性能下降小区':
                s = "2"
            elif scene == '睡眠小区自检':
                s = "3"
            elif scene == '平台自愈日志':
                s = "0"
            elif scene == '平台自愈报告':
                s = "1"
            l.append(s)
        return l

    @validator("reportTimeType")
    def validate_reportTimeType(cls, reportTimeType):
        if reportTimeType == '今天':
            return "today"
        elif reportTimeType == '全部':
            return "all"
        elif reportTimeType == '自定义':
            return "define"
        return


class CheckNameItems(str, Enum):
    UMEReceivingTime = "上报时间"
    shAction = "自愈动作"
    sPlmn = "服务基站PLMN"
    sgNBId = "服务基站标识"
    sCellId = "服务小区标识"
    shFaultReason = "故障原因"


class CheckSelfHealingPara(BaseModel):
    checkName: Literal[
        "结果", "场景", "子网ID", "网元ID", "服务基站标识", "服务基站PLMN", "服务小区标识", "物理小区DU标识",
        "睡眠小区确认时间", "数据粒度", "睡眠连续粒度", "故障原因", "处理建议", "复位小区执行时间", "复位AAU执行时间",
        "复位VBP执行时间", "补偿执行时间", "补偿回退执行时间", "结束原因", "结束时间", "睡眠场景",
        "睡眠关键指标数据:msg1;msg3;rrcSetupReq;rrcSetupSucc;maxRrcUeNum;contextSetupReq;contextSetupSucc", "上报时间",
        "故障原因", "自愈动作", "补偿", "补偿回退", "性能下降小区确认时间", "性能下降场景", "Kpi根因", "自检启动时间",
        "自检执行时间", "自检结束时间", "睡眠小区自检场景", "睡眠小区自检结果"] = Field(description='选择校验列的列名')
    operator: Literal[">", "<", "=", ">=", "<=", "!="] = Field("=", description='选择运算符')
    expect: str | None

    # scenesName: "shScenes"

    @validator("checkName")
    def validate_checkName(cls, checkName):
        if checkName == '结果':
            return "result"
        elif checkName == '场景':
            return "shScenes"
        elif checkName == '子网ID':
            return "subNetwork"
        elif checkName == '网元ID':
            return "sgNBId"
        elif checkName == '服务基站标识':
            return "managedElement"
        elif checkName == '服务基站PLMN':
            return "sPlmn"
        elif checkName == '服务小区标识':
            return "sCellId"
        elif checkName == '物理小区DU标识':
            return "phyCellDuId"
        elif checkName == '睡眠小区确认时间':
            return "sleepingCellConfirmReportTime"
        elif checkName == '睡眠连续粒度':
            return "sleepContinuousGranularity"
        elif checkName == '睡眠关键指标数据:msg1;msg3;rrcSetupReq;rrcSetupSucc;maxRrcUeNum;contextSetupReq;contextSetupSucc':
            return "keyCountersValue"
        elif checkName == '故障原因':
            return "shFaultReason"
        elif checkName == '补偿':
            return "shCompensationState"
        elif checkName == '补偿回退':
            return "shCompensationRollbackState"
        elif checkName == '自愈动作':
            return "shAction"
        elif checkName == '上报时间':
            return "reportTime"
        elif checkName == '结束时间':
            return "reportEndTime"
        return


SELF_HEALING_SCENES_NAMES = ["睡眠小区", "性能下降小区", "NB-IoT睡眠小区", "睡眠小区自愈"]


class SelfHealingUserDecisionLogsPara(BaseModel):
    standardModel: Literal["NR", "NR-IoT ITRAN", "NR-IoT SDR", "LTE ITRAN", "LTE SDR"] = Field(default="NR",
                                                                                               description="选择制式")
    scenesName: DataT = Field(default=SELF_HEALING_SCENES_NAMES, type="jsonEditor",
                              description="按照标准格式填写任务参数")
    reportTimeType: Literal["今天", "全部", "自定义"] = Field("今天", description='选择上报时间，默认为今天')
    startTime: Optional[datetime] | None
    endTime: Optional[datetime] | None

    @validator("scenesName")
    def validate_scenesName(cls, scenesName):
        r = []
        for scenes in scenesName:
            if scenes == "睡眠小区":
                r.append("0")
            elif scenes == "性能下降小区":
                r.append("2")
            elif scenes == "NB-IoT睡眠小区":
                r.append("202")
            elif scenes == "睡眠小区自愈":
                r.append("0")
        return r

    @validator("reportTimeType")
    def validate_reportTimeType(cls, reportTimeType):
        if reportTimeType == '今天':
            return "today"
        elif reportTimeType == '全部':
            return "all"
        elif reportTimeType == '自定义':
            return "define"
        return

    @classmethod
    def schema(cls):
        dependency_dict = {
            "standardModel": {
                "NR": [
                    {"scenesName": [SELF_HEALING_SCENES_NAMES[0], SELF_HEALING_SCENES_NAMES[1]]}
                ],
                "NR-IoT ITRAN": [
                    {"scenesName": [SELF_HEALING_SCENES_NAMES[2]]}
                ],
                "NR-IoT SDR": [
                    {"scenesName": [SELF_HEALING_SCENES_NAMES[3]]}
                ],
                "LTE ITRAN": [
                    {"scenesName": [SELF_HEALING_SCENES_NAMES[0]]}
                ],
                "LTE SDR": [
                    {"scenesName": [SELF_HEALING_SCENES_NAMES[3]]}
                ]
            }
        }
        return MySchema().generate_basic_trace_schema(super().schema(), dependency_dict)


SelfHealingUserDecisionLogsPara.schema()

SelfHealingUserDecisionMap = {
    "运行状态": "runStatus",
    "决策时间": "controlConfirmTime",
    "场景": "shScenes",
    "步骤名称": "sonShRefPoint",
    "子网ID": "subNetwork",
    "网元ID": "managedElement",
    "服务基站标识": "sgNBId",
    "服务基站标识长度的bit位数": "sgNBIdLength",
    "服务基站PLMN": "sPlmn",
    "服务小区标识": "sCellId",
    "故障原因": "shFaultCause",
    "物理小区DU标识": "phyCellDuId",
    "睡眠场景": "sonSleepingScene",
    "上报时间": "reportTime",
    "详细信息": "sendDetail",
    "用户决策结果": "confirmResult",
    "实时检测可信度": "realDetectionCredibility",
    "历史检测可信度": "historicalDetectionCredibility",
    "综合可信度": "comprehensiveCredibility"
}


class SelectSelfHealingUserDecisionLogsPara(BaseModel):
    checkName: Literal[
        "运行状态", "决策时间", "场景", "步骤名称", "子网ID", "网元ID", "服务基站标识", "服务基站标识长度的bit位数",
        "服务基站PLMN", "服务小区标识", "故障原因", "物理小区DU标识", "睡眠场景", "上报时间", "详细信息", "用户决策结果", "实时检测可信度",
        "历史检测可信度", "综合可信度"] = Field(description='选择校验列的列名')
    operator: Literal[">", "<", "=", ">=", "<=", "!="] = Field("=", description='选择运算符')
    expect: str | None

    @validator("checkName")
    def validate_checkName(cls, checkName):
        return SelfHealingUserDecisionMap.get(checkName)


class ExecuteSelfHealingUserDecision(BaseModel):
    sonShRefPoint: Literal["继续", "取消"] = Field("继续", description='用户选择运行状态为：继续 or 取消')

    # refPoint: Literal["检测结果", "复位小区", "复位AAU", "复位VBP", "复位NF", "睡眠小区自检"] = Field(description = '选择步骤名称分别为：检测结果，复位小区，复位AAU，复位VBP,复位NF,睡眠小区自检')

    @validator("sonShRefPoint")
    def validate_check_name(cls, sonShRefPoint):
        if sonShRefPoint == '继续':
            return 2
        return 1
