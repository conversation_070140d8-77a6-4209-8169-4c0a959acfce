import re
import traceback
import pytz
import time
import sys
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.sonm.anr_optimization.Schemas import ExecuteAnrUserDecision, AnrUserDecisionLogsPara
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from domain.models.ume.sonm.SonM import SonM
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs

router = APIRouter(route_class=bizRoute)

@router.post("/query_anr_user_decision_logs", summary="获取SONM模块ANR用户决策日志",
             description=description("网管SONM模块下，获取SONM模块ANR的用户决策实时运行日志", "00256242"))
async def query_anr_user_decision_logs(request: Request, para: AnrUserDecisionLogsPara):
    try:
        mes = await MeFactory().create(request)
        Logger.debug(para)
        scenesName = "sonAnrScenes" if para.standardModel == "NR-->NR" else "sonAnrLteScenes"
        rlt, rltInfo = await SonM().query_user_decision_logs(mes, para, scenesName)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if int(sys.getsizeof(rltInfo)/(1024*1024)) > 10:
            return fail("ACTION 执行失败, 获取到的结果数据太多，请缩短查询时间")
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='获取结果失败')
        return success(data={"taskInfo": rltInfo}, msg='获取结果成功')
    except Exception as e:
        traceback.print_exc()
        Logger.error(traceback.format_exc())
        return fail("ACTION 执行失败", data=str(e))

@router.post("/execute_anr_user_decision", summary="SONM模块ANR受控的用户决策",
             description=description("网管SONM模块下，选择符合条件的SONM模块ANR用户决策日志,用户确认继续or取消,必须依赖action:获取SONM模块ANR用户决策日志，如果有多条日志，会同时执行确认or取消", "00256242"),
             openapi_extra={"refs": [{"actionName": ".*SONM模块ANR用户决策日志"}]})
async def execute_self_healing_user_decision(request: Request, para: ExecuteAnrUserDecision):
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    mes = await MeFactory().create(request)
    seenTransactionIds = set()
    results = []
    if not refTaskInfos:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')
    for refTaskInfo in refTaskInfos:
        sonAnrRefPoint = para.sonAnrRefPoint
        refPoint, transactionId = refTaskInfo.get("refPoint"), refTaskInfo.get("transactionId")
        uriName = "ExecuteAnrUserDecision" if refTaskInfo.get("function") == "ANR" else "ExecuteAnrLteUserDecision"
        if transactionId and transactionId not in seenTransactionIds:
            seenTransactionIds.add(transactionId)
            try:
                rlt, rltInfo = await SonM().execute_user_decision(mes, transactionId, int(refPoint), int(sonAnrRefPoint), uriName)
                if not rlt:
                    return fail(data={"errInfo": rltInfo}, msg='获取结果失败')
                results.append(rltInfo)
            except Exception as e:
                return fail(data={"errInfo": str(e)}, msg="执行过程中发生错误")
    return success(data={"taskInfo": results}, msg='获取结果成功')
