from datetime import datetime
from enum import Enum
from typing import TypeVar, Optional, Literal
from infrastructure.logger import Logger

from pydantic import BaseModel, Field, validator
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")

SELF_HEALING_SCENES_NAMES = ["邻区关系添加", "邻区关系删除", "邻区关系满配替换", "LTE邻区关系添加", "LTE邻区关系删除", "LTE邻区关系满配替换"]

class AnrUserDecisionLogsPara(BaseModel):
    standardModel: Literal["NR-->NR", "NR-->LTE"] = Field(default="NR-->NR", description="选择制式")
    scenesName: DataT = Field(default=SELF_HEALING_SCENES_NAMES, type="jsonEditor",
                              description="按照标准格式填写任务参数")
    reportTimeType: Literal["今天", "全部", "自定义"] = Field("今天", description='选择上报时间，默认为今天')
    startTime: Optional[datetime] | None
    endTime: Optional[datetime] | None

    @validator("scenesName")
    def validate_scenesName(cls, scenesName):
        r = []
        for scenes in scenesName:
            if scenes == "邻区关系添加" or scenes == "LTE邻区关系添加":
                r.append("101")
            elif scenes == "邻区关系删除" or scenes == "LTE邻区关系删除":
                r.append("201")
            elif scenes == "邻区关系满配替换" or scenes == "LTE邻区关系满配替换":
                r.append("401")
        return r

    @validator("reportTimeType")
    def validate_reportTimeType(cls, reportTimeType):
        if reportTimeType == '今天':
            return "today"
        elif reportTimeType == '全部':
            return "all"
        elif reportTimeType == '自定义':
            return "define"
        return
    @classmethod
    def schema(cls):
        dependency_dict = {
            "standardModel": {
                "NR-->NR": [
                    {"scenesName": [SELF_HEALING_SCENES_NAMES[0], SELF_HEALING_SCENES_NAMES[1], SELF_HEALING_SCENES_NAMES[2]]}
                ],
                "NR-->LTE": [
                    {"scenesName": [SELF_HEALING_SCENES_NAMES[3], SELF_HEALING_SCENES_NAMES[4], SELF_HEALING_SCENES_NAMES[5]]}
                ]
            }
        }
        return MySchema().generate_basic_trace_schema(super().schema(), dependency_dict)

AnrUserDecisionLogsPara.schema()

class ExecuteAnrUserDecision(BaseModel):
    sonAnrRefPoint: Literal["继续", "取消"] = Field("继续", description = '用户选择运行状态为：继续 or 取消')

    @validator("sonAnrRefPoint")
    def validate_check_name(cls, sonAnrRefPoint):
        if sonAnrRefPoint == '继续':
            return 2
        return 1
