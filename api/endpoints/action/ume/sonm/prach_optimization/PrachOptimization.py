import re
import traceback
import pytz
import time
import sys
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.sonm.prach_optimization.Schemas import PrachOptimizationLogsPara, CheckPrachOptimizationPara
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from domain.models.ume.sonm.SonM import SonM
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs

router = APIRouter(route_class=bizRoute)


@router.post("/query_prach_optimization_logs", summary="获取SONM模块Prach优化的运行日志",
             description=description("网管SONM模块下，获取SONM模块Prach优化的运行日志", "10263798"))
async def query_prach_optimization_logs(request: Request, para: PrachOptimizationLogsPara):
    try:
        mes = await MeFactory().create(request)
        Logger.debug(para)
        rlt, rltInfo = await SonM().query_optimization_logs(mes, para)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if int(sys.getsizeof(rltInfo)/(1024*1024)) > 10:
            return fail("ACTION 执行失败, 获取到的结果数据太多，请缩短查询时间")
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='The Query Results is got failed')
        return success(data={"taskInfo": rltInfo}, msg='The Query Results is got successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/check_prach_logs", summary="校验SONM模块Prach优化的运行日志",
             description=description("网管SONM模块下，校验SONM模块自愈的运行日志,必须依赖action'获取SONM模块Prach优化的运行日志", "10263798"),
             openapi_extra={"refs": [{"actionName": ".*SONM模块Prach优化的运行日志", "refPattern": "1-*"}]})
async def check_prach_optimization_logs(request: Request, para: CheckPrachOptimizationPara):
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    if not refTaskInfos:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')
    rltList = []
    for refTaskInfo in refTaskInfos:
        checkValue = refTaskInfo.get(para.checkName, "")
        if para.operator == "=":
            if str(checkValue) == para.expect:
                rltList.append(refTaskInfo)
        elif eval("checkValue" + para.operator + "expect", {"checkValue": str(checkValue), "expect": para.expect}):
            rltList.append(refTaskInfo)
    if not rltList:
        return fail(data={"errInfo": ''}, msg='未查询到符合条件的日志')
    return success(data={"taskInfo": rltList}, msg='查询成功')