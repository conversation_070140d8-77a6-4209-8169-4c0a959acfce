from datetime import datetime
from enum import Enum
from typing import Optional, Literal

from pydantic import BaseModel, Field, validator


class PrachScenesEnum(str, Enum):
    RachPerformanceEvaluation = "RACH性能评估"
    PRACHLogicalRootSequenceOptimization = "逻辑根序列优化"
    UplinkInterTimeslotInterferenceOptimization = "上行交叉时隙干扰优化"
    PRACHPowerControlParameterOptimization = "PRACH功控参数优化"


class PrachOptimizationLogsPara(BaseModel):
    radioMode: Literal["NR", "LTE ITRAN", "LTE SDR"] = Field("NR", description='选择制式，默认为NR')

    scenes: list[PrachScenesEnum] = Field([PrachScenesEnum.RachPerformanceEvaluation,
                                           PrachScenesEnum.PRACHLogicalRootSequenceOptimization],
                                          description='选择场景')
    reportTimeType: Literal["今天", "全部", "自定义"] = Field("今天", description='选择上报时间，默认为今天')
    startTime: Optional[datetime] | None
    endTime: Optional[datetime] | None

    @validator("radioMode")
    def validate_radioMode(cls, radioMode):
        l = ""
        if not radioMode:
            return l
        if radioMode == 'NR':
            return "roScenes"
        elif radioMode == 'LTE ITRAN':
            return "roScenes"
        elif radioMode == 'LTE SDR':
            return "scenes"

    @validator("scenes")
    def validate_scenes(cls, scenes):
        l = []
        if not scenes:
            return l
        for scene in scenes:
            if scene == '上行交叉时隙干扰优化':
                s = "1"
            elif scene == '逻辑根序列优化':
                s = "3"
            elif scene == 'PRACH功控参数优化':
                s = "0"
            elif scene == 'RACH性能评估':
                s = "10"
            l.append(s)
        return l

    @validator("reportTimeType")
    def validate_reportTimeType(cls, reportTimeType):
        if reportTimeType == '今天':
            return "today"
        elif reportTimeType == '全部':
            return "all"
        elif reportTimeType == '自定义':
            return "define"
        return


PrachOptimizationMapping = {"结果": "result", "场景": "roScenes", "子网ID": "subNetwork", "网元ID": "managedElement",
                            "服务基站标识": "sgNBId", "服务基站标识长度的bit位数": "sgNBIdLength",
                            "服务基站PLMN": "sPlmn", "服务小区标识": "sCellId",
                            "小区PCI": "pci", "小区SSB中心频点": "arfcnValueNr", "测量子载波间隔": "subcarrierSpacing",
                            "制式": "radioMode",
                            "UE Information数量": "ueInformationNum", "有效的RACH报告数量": "validRachReportNum",
                            "Msg1平均竞争冲突概率(%)": "Msg1CP",
                            "移行率(%)": "shiftRate", "Msg1第1次错检概率(%)": "msg1FirstDMP",
                            "Msg1第2次错检概率(%)": "msg1SecondDMP",
                            "Msg1第3次错检概率(%)": "msg1ThirdDMP", "Msg1第1次接入概率(%)": "msg1FirstAP",
                            "Msg1第2次接入概率(%)": "msg1SecondAP",
                            "Msg1第3次接入概率(%)": "msg1ThirdAP", "上报时间": "reportTime", "事务号": "transactionId",
                            "发现途径": "faultOrigin",
                            "优化前逻辑根序列集合": "rootSequenceSet", "优化后逻辑根序列集合": "optRootSequenceSet",
                            "邻基站标识": "ngNBId",
                            "邻基站PLMN": "nPlmn", "邻区标识": "nCellId", "SSB载频": "ssbFrequency",
                            "分配的详细信息": "assignDetails",
                            "激活的详细信息": "activedDetails", "RSI分配时间": "resolvedTime", "结束时间": "endTime"}


class CheckPrachOptimizationPara(BaseModel):
    checkName: Literal[
        "结果", "场景", "子网ID", "网元ID", "服务基站标识", "服务基站标识长度的bit位数", "服务基站PLMN", "服务小区标识",
        "小区PCI", "小区SSB中心频点", "测量子载波间隔", "制式", "UE Information数量", "有效的RACH报告数量", "Msg1平均竞争冲突概率(%)",
        "移行率(%)", "Msg1第1次错检概率(%)", "Msg1第2次错检概率(%)", "Msg1第3次错检概率(%)", "Msg1第1次接入概率(%)", "Msg1第2次接入概率(%)",
        "Msg1第3次接入概率(%)", "上报时间", "事务号", "发现途径", "优化前逻辑根序列集合", "优化后逻辑根序列集合", "邻基站标识", "邻基站PLMN",
        "邻区标识", "SSB载频", "分配的详细信息", "激活的详细信息", "RSI分配时间", "结束时间"] = Field(
        description='选择校验列的列名')
    operator: Literal[">", "<", "=", ">=", "<=", "!="] = Field("=", description='选择运算符')
    expect: str | None

    @validator("checkName")
    def validate_checkName(cls, checkName):
        return PrachOptimizationMapping.get(checkName)
