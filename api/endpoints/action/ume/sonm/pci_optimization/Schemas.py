from datetime import datetime
from enum import Enum
from typing import TypeVar, Optional, Literal
from infrastructure.logger import Logger
from pydantic import BaseModel, Field, validator

from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")

class PciScenesEnum(str, Enum):
    CentralizedPCIOptimization = "集中式PCI优化"

class PciOptimizationLogsPara(BaseModel):
    radioMode: Literal["NR", "LTE", "LTE SDR"] = Field("NR", description='选择制式，默认为NR')

    scenes: list[PciScenesEnum] = Field([PciScenesEnum.CentralizedPCIOptimization], description='选择场景')
    reportTimeType: Literal["今天", "全部", "自定义"] = Field("今天", description='选择上报时间，默认为今天')
    startTime: Optional[datetime] | None
    endTime: Optional[datetime] | None

    @validator("radioMode")
    def validate_radioMode(cls, radioMode):
        l = ""
        if not radioMode:
            return l
        if radioMode == 'NR':
            return "pciScenes"
        elif radioMode == 'LTE':
            return "pciScenes"
        elif radioMode == 'LTE SDR':
            return "scenes"


    @validator("scenes")
    def validate_scenes(cls, scenes):
        l = []
        if not scenes:
            return l
        for scene in scenes:
            if scene == '集中式PCI优化':
                s = "0"
            l.append(s)
        return l

    @validator("reportTimeType")
    def validate_reportTimeType(cls, reportTimeType):
        if reportTimeType == '今天':
            return "today"
        elif reportTimeType == '全部':
            return "all"
        elif reportTimeType == '自定义':
            return "define"
        return

PciOptimizationMapping = {"检测网元的子网ID": "subNetwork", "检测网元ID": "managedElement", "场景": "pciScenes", "运行模式": "runMode",
    "上报时间": "reportTime", "PCI故障类型": "pciType", "发现途径": "faultOrigin", "A小区基站标识": "aCellNeID", "A小区PLMN": "aCellNePlmn",
    "A小区标识": "aCellID", "B小区基站标识": "bCellNeID", "B小区PLMN": "bCellNePlmn", "B小区标识": "bCellID", "冲突/混淆小区制式": "faultCellRadioMode",
    "中间小区基站标识": "middleCellNeID", "中间小区PLMN": "middleCellNePlmn", "中间小区标识": "middleCellID", "中间小区制式": "middleCellRadioMode",
    "检测小区": "pciDetectCellType", "A小区是否是黑列表邻区": "aAnrBlackListNbrType", "中间小区到A的切换成功率": "middleCell2SourceHandoverOutSuccessRate",
    "B小区是否是黑列表邻区": "bAnrBlackListNbrType", "中间小区到B的切换成功率": "middleCell2TargetHandoverOutSuccessRate", "冲突/混淆小区PCI": "faultPci",
    "冲突/混淆小区频点": "faultFrequency", "分配的PCI（A小区|B小区）": "allocatePci", "分配结果": "allocateResult", "分配的详细信息": "allocateDetail",
    "激活结果": "activeResult", "激活的详细信息": "activeDetail", "是否解决故障": "isFaultSolved", "优化后的PCI（A小区|B小区）": "pciAfterOptimized",
    "开始时间": "startTime", "结束时间": "reportEndTime", "小区对当前的用户数": "currentUserNum", "小区对当前的邻区数": "neighBorCellNum",
    "小区对当前的PCI冲突/混淆数": "pciFaultNum", "小区对类型": "coverageType"}
class CheckPciOptimizationPara(BaseModel):
    checkName: Literal["检测网元的子网ID", "检测网元ID", "场景", "运行模式", "上报时间", "PCI故障类型", "发现途径", "A小区基站标识",
                       "A小区PLMN", "A小区标识", "B小区基站标识", "B小区PLMN", "B小区标识", "冲突/混淆小区制式", "中间小区基站标识",
                       "中间小区PLMN", "中间小区标识", "中间小区制式", "检测小区", "A小区是否是黑列表邻区", "中间小区到A的切换成功率",
                       "B小区是否是黑列表邻区", "中间小区到B的切换成功率", "冲突/混淆小区PCI", "冲突/混淆小区频点", "分配的PCI（A小区|B小区）",
                       "分配结果", "分配的详细信息", "激活结果", "激活的详细信息", "是否解决故障", "优化后的PCI（A小区|B小区）", "开始时间",
                       "结束时间", "小区对当前的用户数", "小区对当前的邻区数", "小区对当前的PCI冲突/混淆数", "小区对类型"] = Field(description='选择校验列的列名')
    operator: Literal[">", "<", "=", ">=", "<=", "!="] = Field("=", description='选择运算符')
    expect: str | None

    @validator("checkName")
    def validate_checkName(cls, checkName):
        return PciOptimizationMapping.get(checkName)

pciSectionsTemplate = [{
            "sectionID": "0",
            "section": "0-799"
        },
        {
            "sectionID": "1",
            "section": "800-1007"
        },
        {
            "sectionID": "2",
            "section": ""
        },
        {
            "sectionID": "3",
            "section": ""
        },
        {
            "sectionID": "4",
            "section": ""
        },
        {
            "sectionID": "5",
            "section": ""
        },
        {
            "sectionID": "6",
            "section": ""
        },
        {
            "sectionID": "7",
            "section": ""
        },
        {
            "sectionID": "8",
            "section": ""
        },
        {
            "sectionID": "9",
            "section": ""
        }
    ]

prachSectionsTemplate = [{
            "sectionID": "0",
            "section": "0-837"
        },
        {
            "sectionID": "1",
            "section": "0-137"
        }]

PCI_PARAMETERS_TEMPLATE = {
    "PCI分配开关": "打开",
    "定时处理开关": "关闭",
    "定时处理时间 (本地时间)": "07:00",
    "集中式PCI运行模式": "自由",
    "断点超时时长(分钟)_PCI": "1",
    "断点超时策略_PCI": "继续",
    "满足最小复用层数": "否",
    "PCI最小复用层数": "4",
    "PCI模3约束": "PCI模3保持不变",
    "PCI模30约束": "PCI模30保持不变",
    "PCI模30选取的最大邻接NR载波个数": "5",
    "PCI相关性门限": "0.10",
    "PCI泄露比门限": "-10",
    "PCI相关性选取的最大邻接NR载波个数": "3",
    "集中式检测周期(小时)": "24"
}

Prach_PARAMETERS_TEMPLATE = {
    "RSI自优化开关": "打开",
    "RSI定时处理开关": "关闭",
    "RSI冲突收集时长(分钟)": "5",
    "RSI自优化定时处理时间": "08:00",
    "集中式RSI运行模式": "自由",
    "断点超时时长(分钟)_Prach": "5",
    "断点超时策略_Prach": "继续",
    "相邻小区范围": "过滤邻区",
    "满足RSI峰均比限制": "否",
    "满足最小复用距离": "否"
}

PARAMETERS_KEY_MAP = {
    "PCI分配开关": "allocatePciSwitch",
    "定时处理开关": "timingProcessPciSwitch",
    "定时处理时间 (本地时间)": "optimizationTime",
    "集中式PCI运行模式": "runMode",
    "断点超时时长(分钟)_PCI": "breakTimeoutLength",
    "断点超时策略_PCI": "breakPointTimeoutPolicy",
    "满足最小复用层数": "satisfyMinReuseLayer",
    "PCI最小复用层数": "pciReuseLayers",
    "PCI模3约束": "mod3Constraint",
    "PCI模30约束": "mod30",
    "PCI模30选取的最大邻接NR载波个数": "neighborNumM30",
    "PCI相关性门限": "correlationThreshold",
    "PCI泄露比门限": "leakageThreshold",
    "PCI相关性选取的最大邻接NR载波个数": "neighborlNumCorrelation",
    "PCI混淆重分配小区间距离门限(米)": "distanceThresholdForConfusion",
    "集中式检测开关": "centralizedDetectionSwitch",
    "集中式检测周期(小时)": "cenPciPeriod",
    "RSI自优化开关": "rsiSwitch",
    "RSI定时处理开关": "rsiResTimeSwitch",
    "RSI冲突收集时长(分钟)": "collectionTime",
    "RSI自优化定时处理时间": "rsiResTime",
    "集中式RSI运行模式": "rsiRunMode",
    "断点超时时长(分钟)_Prach": "sonTimeOutPeriod",
    "断点超时策略_Prach": "sonTimeOutPolicy",
    "相邻小区范围": "nbrCellRange",
    "满足RSI峰均比限制": "meetCM",
    "满足最小复用距离": "meetMinReuseDist"
}

PARAMETERS_VALUE_MAP = {
    "打开": "1", "关闭": "0", "自由": "0", "受控": "1", "取消": "0", "继续": "1", "否": "0", "是": "1", "PCI模3保持不变": "0", "邻区PCI模3不等": "1",
    "PCI模30保持不变": "0", "邻区PCI模30不等": "1", "过滤邻区": "0", "所有邻区": "1"
}

class NRCentralizedOptimizationParameters(BaseModel):
    taskType: Literal["PCI", "Prach"] = Field(default="Prach")
    parameters: DataT = Field(default=Prach_PARAMETERS_TEMPLATE, type="jsonEditor",
                              description="按照标准格式填写任务参数")
    sections: DataT = Field(default=prachSectionsTemplate, type="jsonEditor", description="按照标准格式填写任务参数")

    @validator('parameters')
    def parameters_analysis(cls, parameters):
        ret = {}
        for key, value in parameters.items():
            ret.update({PARAMETERS_KEY_MAP.get(key, key): PARAMETERS_VALUE_MAP.get(value, value)})
        return ret

    @classmethod
    def schema(cls):
        dependency_dict = {
            "taskType": {
                "PCI": [
                    {"parameters": PCI_PARAMETERS_TEMPLATE},
                    {"sections": pciSectionsTemplate}
                ],
                "Prach": [
                    {"parameters": Prach_PARAMETERS_TEMPLATE},
                    {"sections": prachSectionsTemplate}
                ]
            }
        }
        return MySchema().generate_basic_notime_schema(super().schema(), dependency_dict)

NRCentralizedOptimizationParameters.schema()

class reportTimeTypeEnum(str, Enum):
    c1 = "all"
    c2 = "today"
    c3 = "define"

class hourSlotEnum(str, Enum):
    c1 = 24
    c2 = 1
    c3 = 4
    c4 = 8
    c5 = 12

SONM_PARAMETERS_TEMPLATE = {
    "functionalModule": "ANR Optimization",
    "childFunctionalModule": "Optimization Logs",
    "userDecisionType": "realTime",
    "product": "NR NR",
    "scenesName": "anrScenes",
    "scenesValue": "0"
}

class AnrOptimizationLogsFilters(BaseModel):
    parameters: DataT = Field(default=SONM_PARAMETERS_TEMPLATE, type="jsonEditor", description="按照标准格式填写 任务参数")
    reportTimeType: reportTimeTypeEnum
    startTime: Optional[datetime] | None
    endTime: Optional[datetime] | None
    hourSlot: hourSlotEnum

PCI_OPTIMIZATION_SCENES_NAMES = ["集中式PCI优化", "分布式PCI优化"]

class PCIOptimizationUserDecisionLogsPara(BaseModel):
    standardModel: Literal["NR", "LTE", "LTE SDR"] = Field(default="NR", description="选择制式")
    scenesName: DataT = Field(default=PCI_OPTIMIZATION_SCENES_NAMES, type="jsonEditor", description="按照标准格式填写任务参数")
    reportTimeType: Literal["今天", "全部", "自定义"] = Field("今天", description='选择上报时间，默认为今天')
    startTime: Optional[datetime] | None
    endTime: Optional[datetime] | None

    @validator("scenesName")
    def validate_scenesName(cls, scenesName):
        r = []
        for scenes in scenesName:
            if scenes == "集中式PCI优化":
                r.append("0")
            elif scenes == "分布式PCI优化":
                r.append("1")
        return r

    @validator("reportTimeType")
    def validate_reportTimeType(cls, reportTimeType):
        if reportTimeType == '今天':
            return "today"
        elif reportTimeType == '全部':
            return "all"
        elif reportTimeType == '自定义':
            return "define"
        return

    @classmethod
    def schema(cls):
        dependency_dict = {
            "standardModel": {
                "NR": [
                    {"scenesName": [PCI_OPTIMIZATION_SCENES_NAMES[0]]}
                ],
                "LTE": [
                    {"scenesName": [PCI_OPTIMIZATION_SCENES_NAMES[0], PCI_OPTIMIZATION_SCENES_NAMES[1]]}
                ],
                "LTE SDR": [
                    {"scenesName": [PCI_OPTIMIZATION_SCENES_NAMES[1]]}
                ]
            }
        }
        return MySchema().generate_basic_notime_schema(super().schema(), dependency_dict)

PCIOptimizationUserDecisionLogsPara.schema()

PCIOptimizationUserDecisionMap = {
    "检测网元的子网ID": "subNetwork",
    "检测网元ID": "managedElement",
    "场景": "pciScenes",
    "运行状态": "runStatus",
    "用户决策结果": "confirmResult",
    "详细信息": "sendDetail",
    "优化小区基站标识": "optimizationCellNeID",
    "优化小区PLMN": "cellPLMN",
    "优化小区小区标识": "optimizationCellID",
    "冲突混淆次数": "faultTimes",
    "冲突混淆事件": "faultEvent",
    "冲突/混淆小区PCI": "faultPci",
    "分配的PCI": "allocatedPci",
    "分配详细信息": "allocatedDetail",
    "上报时间": "reportTime",
    "决策时间": "controlConfirmTime"
}

class SelectPCIOptimizationUserDecisionLogsPara(BaseModel):
    checkName: Literal["检测网元的子网ID", "检测网元ID", "场景", "运行状态", "用户决策结果", "详细信息", "优化小区基站标识", "上报时间",
        "优化小区PLMN", "优化小区小区标识", "冲突混淆次数", "冲突混淆事件", "冲突/混淆小区PCI", "分配的PCI", "分配详细信息", "决策时间"] \
        = Field(description='选择校验列的列名')
    operator: Literal[">", "<", "=", ">=", "<=", "!="] = Field("=", description='选择运算符')
    expect: str | None

    @validator("checkName")
    def validate_checkName(cls, checkName):
        return PCIOptimizationUserDecisionMap.get(checkName)

class ExecutePciUserDecision(BaseModel):
    sonPciRefPoint: Literal["继续", "取消"] = Field("继续", description = '用户选择运行状态为：继续 or 取消')

    @validator("sonPciRefPoint")
    def validate_check_name(cls, sonPciRefPoint):
        if sonPciRefPoint == '继续':
            return 2
        return 1