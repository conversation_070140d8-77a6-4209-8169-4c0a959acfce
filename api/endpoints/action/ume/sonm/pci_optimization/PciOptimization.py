import re
import traceback
import pytz
import time
import sys
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.sonm.pci_optimization.Schemas import (PciOptimizationLogsPara, CheckPciOptimizationPara,
                                                                    NRCentralizedOptimizationParameters,
                                                                    AnrOptimizationLogsFilters,
                                                                    PCIOptimizationUserDecisionLogsPara,
                                                                    SelectPCIOptimizationUserDecisionLogsPara,
                                                                    ExecutePciUserDecision)
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from domain.models.ume.sonm.SonM import SonM
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs

router = APIRouter(route_class=bizRoute)


@router.post("/query_pci_optimization_logs", summary="获取SONM模块PCI优化的运行日志",
             description=description("网管SONM模块下，获取SONM模块PCI优化的运行日志", "10263798"))
async def query_pci_optimization_logs(request: Request, para: PciOptimizationLogsPara):
    try:
        mes = await MeFactory().create(request)
        Logger.debug(para)
        rlt, rltInfo = await SonM().query_optimization_logs(mes, para)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if int(sys.getsizeof(rltInfo)/(1024*1024)) > 10:
            return fail("ACTION 执行失败, 获取到的结果数据太多，请缩短查询时间")
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='The Query Results is got failed')
        return success(data={"taskInfo": rltInfo}, msg='The Query Results is got successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/check_pci_optimization_logs", summary="校验SONM模块PCI优化的运行日志",
             description=description("网管SONM模块下，校验SONM模块PCI优化的运行日志,必须依赖action'获取SONM模块PCI优化的运行日志", "10263798"),
             openapi_extra={"refs": [{"actionName": ".*SONM模块PCI优化的运行日志", "refPattern": "1-*"}]})
async def check_pci_optimization_logs(request: Request, para: CheckPciOptimizationPara):
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    if not refTaskInfos:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')
    rltList = []
    for refTaskInfo in refTaskInfos:
        checkValue = refTaskInfo.get(para.checkName, "")
        if para.operator == "=":
            if str(checkValue) == para.expect:
                rltList.append(refTaskInfo)
        elif eval("checkValue" + para.operator + "expect", {"checkValue": str(checkValue), "expect": para.expect}):
            rltList.append(refTaskInfo)
    if not rltList:
        return fail(data={"errInfo": ''}, msg='未查询到符合条件的日志')
    return success(data={"taskInfo": rltList}, msg='查询成功')

@router.post("/modify_nrcentralized_optimization_parameters", summary="修改SONM模块NR集中式优化参数",
             description=description("网管SONM模块下，修改SONM模块NR集中式优化参数", "10263798"))
async def modify_nrcentralized_optimization_parameters(request: Request, para: NRCentralizedOptimizationParameters):
    try:
        mes = await MeFactory().create(request)
        Logger.debug(para)
        rlt = await SonM().modify_nrcentralized_optimization_parameters(mes, para)
        Logger.debug(rlt)
        if not rlt:
            return fail(msg='修改失败')
        return success(msg='修改成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/query_optimization_logs", summary="获取SONM日志",
             description=description("网管SONM模块下，获取SONM日志", "10263798"))
async def query_optimization_logs(request: Request,  para: AnrOptimizationLogsFilters):
    try:
        mes = await MeFactory().create(request)
        Logger.debug(para)
        rlt, rltInfo = await SonM().get_optimization_logs(mes, para)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='The Query Results is got failed')
        return success(data={"taskInfo": rltInfo}, msg='The Query Results is got successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/query_pci_user_decision_logs", summary="获取SONM模块PCI优化用户决策日志",
             description=description("网管SONM模块下，获取SONM模块用户决策的运行日志", "10263798"))
async def query_pci_user_decision_logs(request: Request, para: PCIOptimizationUserDecisionLogsPara):
    try:
        mes = await MeFactory().create(request)
        Logger.debug(para)
        rlt, rltInfo = await SonM().query_user_decision_logs(mes, para, "pciScenes")
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if int(sys.getsizeof(rltInfo)/(1024*1024)) > 10:
            return fail("ACTION 执行失败, 获取到的结果数据太多，请缩短查询时间")
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='The Query Results is got failed')
        return success(data={"taskInfo": rltInfo}, msg='The Query Results is got successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/select_pci_optimization_uerDecision_log", summary="选择SONM模块PCI优化用户决策日志",
             description=description("网管SONM模块下，选择符合条件的SONM模块PCI优化用户决策日志,必须依赖action:获取SONM模块PCI优化用户决策日志", "10263798"),
             openapi_extra={"refs": [{"actionName": ".*SONM模块PCI优化用户决策日志", "refPattern": "1-*"}]})
async def select_pci_optimization_uerDecision_log(request: Request, para: SelectPCIOptimizationUserDecisionLogsPara):
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    if not refTaskInfos:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')
    rltList = []
    for refTaskInfo in refTaskInfos:
        checkValue = refTaskInfo.get(para.checkName, "")
        if para.operator == "=":
            if str(checkValue) == para.expect:
                rltList.append(refTaskInfo)
        elif eval("checkValue" + para.operator + "expect", {"checkValue": str(checkValue), "expect": para.expect}):
            rltList.append(refTaskInfo)
    if not rltList:
        return fail(data={"errInfo": ''}, msg='未查询到符合条件的日志')
    return success(data={"taskInfo": rltList}, msg='查询成功')

@router.post("/select_pci_optimization_user_decision_log", summary="选择SONM模块PCI优化用户决策日志",
             description=description("网管SONM模块下，选择符合条件的SONM模块PCI优化用户决策日志,必须依赖action:获取SONM模块PCI优化用户决策日志", "10263798"),
             openapi_extra={"refs": [{"actionName": ".*SONM模块PCI优化用户决策日志", "refPattern": "1-*"}]})
async def select_pci_optimization_user_decision_log(request: Request, para: SelectPCIOptimizationUserDecisionLogsPara):
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    if not refTaskInfos:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')
    rltList = []
    for refTaskInfo in refTaskInfos:
        checkValue = refTaskInfo.get(para.checkName, "")
        if para.operator == "=":
            if str(checkValue) == para.expect:
                rltList.append(refTaskInfo)
        elif eval("checkValue" + para.operator + "expect", {"checkValue": str(checkValue), "expect": para.expect}):
            rltList.append(refTaskInfo)
    if not rltList:
        return fail(data={"errInfo": ''}, msg='未查询到符合条件的日志')
    return success(data={"taskInfo": rltList}, msg='查询成功')

@router.post("/execute_pci_user_decision", summary="SONM模块PCI检测受控的用户决策",
             description=description("网管SONM模块下，选择符合条件的SONM模块PCI优化用户决策日志,用户确认继续or取消,必须依赖action:获取SONM模块PCI优化用户决策日志，如果有多条日志，会同时执行确认or取消", "00256242"),
             openapi_extra={"refs": [{"actionName": ".*SONM模块PCI优化用户决策日志"}]})
async def execute_pci_user_decision(request: Request, para: ExecutePciUserDecision):
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    mes = await MeFactory().create(request)
    seenTransactionIds = set()
    results = []

    if not refTaskInfos:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')

    for refTaskInfo in refTaskInfos:
        sonPciRefPoint = para.sonPciRefPoint
        refPoint = refTaskInfo.get("refPoint")
        transactionId = refTaskInfo.get("transactionId")

        if transactionId and transactionId not in seenTransactionIds:
            seenTransactionIds.add(transactionId)  # 添加到已处理的ID集合
            try:
                # 确保 refPoint 和 sonPciRefPoint 能够转换为整数
                rlt, rltInfo = await SonM().execute_user_decision(mes, transactionId, int(refPoint), int(sonPciRefPoint), "ExecutePciUserDecision")
                if not rlt:  # 如果执行失败
                    return fail(data={"errInfo": rltInfo}, msg='The Query Results is got failed')
                results.append(rltInfo)  # 保存成功的结果
            except Exception as e:
                # 捕获其他异常
                return fail(data={"errInfo": str(e)}, msg="执行过程中发生错误")
    # 如果循环中没有失败，则返回成功
    return success(data={"taskInfo": results}, msg='The Query Results is got successful')