import copy
import traceback
import time
import pytz
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.sta.Schemas import ModifyDttraceTask, AtutraceTask, NssaitraceTask, CreateStationTask, ModifyStationTask
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.factories.PdnFactory import PdnFactory
from domain.models.ume.sta.Sta import Sta
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.utils.TimeHandler import format_to_zero_time, format_startEndTime
from infrastructure.resource.service.Refs import Refs


router = APIRouter(route_class=bizRoute)

@router.post("/modify_dttrace", summary="修改STA基站跟踪DTTRACE任务(建议使用:修改STA基站跟踪任务)",
             description=description("【功能】网管STA模块下，修改基站跟踪DTTRACE任务,修改前需挂起任务，修改后自动激活", "岳昊冉10263798"),
             openapi_extra={"refs": [{"actionName": "创建STA基站跟踪DTTRACE任务", "refPattern": "1-*"}]})
async def modify_dttrace(request: Request, para: ModifyDttraceTask):
    mes = await MeFactory.create(request)
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    try:
        taskInfo = {}
        if para.startTime and para.endTime:
            startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                     para.endTime.astimezone(pytz.utc))
        else:
            startTime, endTime = format_startEndTime()
        parameters = {"Ping包长度尾数": para.pingPacketLengthRemainder, "最大UE数": para.ueNum}
        for me in mes:
            refTaskInfoDic = refTaskInfos.get(me.meId)
            eventList = para.eventList if para.eventList else refTaskInfoDic.get("eventList")
            if para.ipAddress:
                parameters.update({"IP地址": para.ipAddress})
            else:
                parameters.update({"IP地址": refTaskInfoDic.get("ipAddress")})
            info = {"taskName": refTaskInfoDic.get("taskName"), "menuId": "ranume-sta-ne-trace", "eventList": eventList,
                    "taskType": "DTTrace", "parameters": parameters, "startTime": startTime, "endTime": endTime}
            paras = {"dataViewMode": "2", "serviceType": "5gnr", "subscriptionId": refTaskInfoDic.get("subscriptionId"),
                     "menuId": "ranume-sta-ne-trace"}
            paras.update({"eventList": para.eventList}) if para.eventList else paras
            taskRlt, errMsg = await Sta(me).modify_task(paras, info)
            if errMsg:
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                taskRlt.update({"ipAddress": parameters.get("IP地址")})
                taskInfo.update({me.meId: taskRlt})
        if taskInfo:
             return success(data={"taskInfo": taskInfo}, msg="Base station dttrace task is modified successful.")
        return fail(data={"taskInfo": taskInfo}, msg="Base station dttrace task is modified failed.")
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/create_atutrace", summary="创建STA基站跟踪ATUTRACE任务(建议使用:创建STA基站跟踪任务)",
             description=description("【功能】网管STA模块下，创建基站跟踪ATUTRACE任务", "岳昊冉10263798"))
async def create_atutrace(request: Request, para: AtutraceTask):
    mes = await MeFactory.create(request)
    try:
        taskInfo = {}
        if para.startTime and para.endTime:
            startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                     para.endTime.astimezone(pytz.utc))
        else:
            startTime, endTime = format_startEndTime()
        for me in mes:
            paras = {"taskName": "Hyper_Automation_" + me.meId + "_" + str(int(time.time())), "dataViewMode": "2",
                     "serviceType": "5gnr", "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                     "menuId": "neTrace", "taskType": "ATUTrace"}
            taskRlt, errMsg = await Sta(me).create_task(paras)
            if errMsg:
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                taskInfo.update({me.meId: taskRlt})
        if taskInfo:
            return success(data={"taskInfo": taskInfo}, msg="Base station atutrace task is created successful.")
        return fail(data={"taskInfo": taskInfo}, msg="Base station atutrace task is created failed.")
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/modify_atutrace", summary="修改STA基站跟踪ATUTRACE任务(建议使用:修改STA基站跟踪任务)",
             description=description("【功能】网管STA模块下，修改基站跟踪ATUTRACE任务，修改前需挂起任务，修改后自动激活", "岳昊冉10263798"),
             openapi_extra={"refs": [{"actionName": "创建STA基站跟踪ATUTRACE任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def modify_atutrace(request: Request, para: AtutraceTask):
    mes = await MeFactory.create(request)
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    try:
        taskInfo = {}
        if para.startTime and para.endTime:
            startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                     para.endTime.astimezone(pytz.utc))
        else:
            startTime, endTime = format_startEndTime()
        for me in mes:
            refTaskInfoDic = refTaskInfos.get(me.meId)
            eventList = para.eventList if para.eventList else refTaskInfoDic.get("eventList")
            info = {"taskName": refTaskInfoDic.get("taskName"), "menuId": "ranume-sta-ne-trace", "eventList": eventList,
                    "taskType": "ATUTrace", "startTime": startTime, "endTime": endTime}
            paras = {"dataViewMode": "2", "serviceType": "5gnr", "subscriptionId": refTaskInfoDic.get("subscriptionId"),
                     "menuId": "ranume-sta-ne-trace"}
            paras.update({"eventList": para.eventList}) if para.eventList else paras
            taskRlt, errMsg = await Sta(me).modify_task(paras, info)
            if errMsg:
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                taskInfo.update({me.meId: taskRlt})
        if taskInfo:
            return success(data={"taskInfo": taskInfo}, msg="Base station atutrace task is modified successful.")
        return fail(data={"taskInfo": taskInfo}, msg="Base station atutrace task is modified failed.")
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/create_nssaitrace", summary="创建STA切片跟踪任务",
             description=description("【功能】网管STA模块下，区域跟踪功能中，创建切片跟踪任务", "岳昊冉10263798"),
             openapi_extra = {"refs": [{"actionName": "UE接入", "refPattern": "0-*"}]})
async def create_nssaitrace(request: Request, para: NssaitraceTask):
    mes = await MeFactory.create(request)
    refs = await Refs(request).get_output()
    ueIpList = [ref.get("ipAddress") for ref in refs] if refs else []
    ueIpList = ueIpList + para.ueIpAddressList
    ueIpList = list(dict.fromkeys([ip for ip in ueIpList if ip is not None and ip != '']))
    try:
        taskInfo = {}
        if para.startTime and para.endTime:
            startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                     para.endTime.astimezone(pytz.utc))
        else:
            startTime, endTime = format_startEndTime()
        if ueIpList and para.sliceSDList and para.sliceSSTList and para.fiveQIList:
            parameters = {"NSSAI_UeIP地址": repr(ueIpList).replace("'", '"'), "5QI": para.fiveQIList,
                          "切片sd": para.sliceSDList, "切片sst": para.sliceSSTList}
        else:
            return fail(msg="必填参数不能为空!")
        for me in mes:
            paras = {"taskName": "Hyper_Automation_" + me.meId + "_" + str(int(time.time())), "dataViewMode": "2",
                     "serviceType": "5gnr", "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                     "menuId": "ranume-sta-nssai-trace", "taskType": "MTS", "parameters": parameters}
            taskRlt, errMsg = await Sta(me).create_task(paras)
            if errMsg:
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                taskInfo.update({me.meId: taskRlt})
        if taskInfo:
             return success(data={"taskInfo": taskInfo}, msg="Base station nssaitrace task is created successful.")
        return fail(data={"taskInfo": taskInfo}, msg="Base station nssaitrace task is created failed.")
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/create_station_trace", summary="创建STA基站跟踪任务",
             description=description("【功能】网管STA模块下，创建基站跟踪任务(IperfData请使用:创建STA基站灌包任务)", "侯小飞10270755"))
async def create_station_trace(request: Request, para:CreateStationTask):
    mes_nr = await MeFactory.create_by_nr(request)
    mes_lte = await MeFactory.create_by_lte(request)
    mes_mec = await MeFactory.create_by_mec(request)
    mes = mes_nr + mes_lte + mes_mec
    if sum(map(lambda x:1 if x else 0, [mes_nr, mes_lte, mes_mec])) > 1:
        return fail(msg="本action不支持同时选择NR、LTE、MEC资源，请通过资源过滤出单一制式资源!")
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    parameters = copy.deepcopy(para.parameters)
    try:
        taskInfo = {}
        serviceType = "5gnr" if mes_nr else "lte" if mes_lte else "mec"
        if para.startTime and para.endTime:
            startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc), para.endTime.astimezone(pytz.utc))
        else: startTime, endTime = format_startEndTime()
        for me in mes:
            paras = {"taskName": "Hyper_Automation_" + me.meId + "_" + str(int(time.time())), "dataViewMode": para.dataViewMode,
                     "serviceType": serviceType, "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                     "menuId": "neTrace", "taskType": para.taskType}
            if para.taskType == "DTTrace":
                if "IP地址" not in parameters:
                    pdns = PdnFactory.create(request)
                    if not pdns: return fail(msg=f"必须在parameters输入'IP地址'或者环境资源中有一个pdn")
                    parameters.update({"IP地址": pdns[0].cnIp})
                paras.update({"parameters": parameters})
            if para.taskType == "Signal":
                paras.update({"parameters": parameters})
            taskRlt, errMsg = await Sta(me).create_task(paras)
            if errMsg: return fail(data={"taskInfo": taskInfo}, msg=f"网元{me.meId}创建基站跟踪任务失败,原因:{errMsg}")
            else:
                taskRlt.update({"dataViewMode": para.dataViewMode})
                taskInfo.update({me.meId: taskRlt})
        if taskInfo:
            return success(data={"taskInfo": taskInfo}, msg="基站跟踪任务创建成功")
    except Exception as e:
        traceback.print_exc()
        Logger.error(f"创建基站跟踪任务过程中程序运行错误，详情:",traceback.format_exc())
        return fail(f"创建基站跟踪任务过程中程序运行错误，详情:{str(e)}")


@router.post("/modify_station_trace", summary="修改STA基站跟踪任务",
             description=description("【功能】网管STA模块下，修改基站跟踪任务", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "创建STA基站跟踪任务"}]})
async def modify_station_trace(request: Request, para:ModifyStationTask):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo: return fail(msg="!!没有要修改的任务，请先创建相关任务!!")
    parameters = copy.deepcopy(para.parameters)
    ret, newTaskInfo = False, {}
    for meTask in taskInfo:
        for meId, info in meTask.items():
            meTaskinfo = copy.deepcopy(info)
            me = await MeFactory.create_by_meid(request, meId)
            if me is None: return fail(msg=f"当前无满足条件网元，请检查：环境资源信息中是否存在{meId}网元!")
            try:
                eventList = para.eventList if para.eventList else info.get("eventList", [])
                paras = {"dataViewMode": info.get("dataViewMode"), "serviceType": info.get("serviceType"),
                         "startTime": info.get("startTime"), "endTime": None, "eventList": eventList,
                         "menuId": "neTrace", "taskType": para.taskType, "subscriptionId": info.get("subscriptionId")}
                if para.taskType == "DTTrace":
                    if "IP地址" not in parameters:
                        pdns = PdnFactory.create(request)
                        if not pdns: return fail(msg=f"必须在parameters输入'IP地址'或者环境资源中有一个pdn")
                        parameters.update({"IP地址": pdns[0].cnIp})
                    paras.update({"parameters": parameters})
                if para.taskType == "Signal":
                    paras.update({"parameters": parameters})
                taskRlt, errMsg = await Sta(me).modify_task(meTaskinfo, paras)
                if errMsg: return fail(data={"taskInfo": newTaskInfo}, msg=f"网元{me.meId}修改基站跟踪任务失败,原因:{errMsg}")
                else:
                    meTaskinfo.update(taskRlt)
                    newTaskInfo.update({me.meId: meTaskinfo})
            except Exception as e:
                traceback.print_exc()
                Logger.error(f"修改基站跟踪任务过程中程序运行错误，详情:",traceback.format_exc())
                return fail(f"修改基站跟踪任务过程中程序运行错误，详情:{str(e)}")
    return success(data={"taskInfo": newTaskInfo}, msg="基站跟踪任务修改成功")