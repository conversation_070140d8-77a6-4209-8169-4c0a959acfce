#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:25
# <AUTHOR> 10263601

from fastapi import APIRouter

from api.endpoints.action.ume.sta.RegionTrace import router as regionTraceRouter
from api.endpoints.action.ume.sta.Sta import router as staRouter
from api.endpoints.action.ume.sta.UserTrace import router as userTraceRouter
from api.endpoints.action.ume.sta.ScSta import router as scStaRouter

router = APIRouter()
router.include_router(staRouter)
router.include_router(regionTraceRouter)
router.include_router(userTraceRouter)
router.include_router(scStaRouter)
