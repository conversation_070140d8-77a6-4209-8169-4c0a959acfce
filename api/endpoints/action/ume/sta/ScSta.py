import traceback
import pytz
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.sta.ScSchemas import CreateScStaCellInfo
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from service.action.ume.sta.CellSignalTask import CellSignalTask
from domain.factories.CellFactory import CellFactory
from infrastructure.utils.ResultFormatter import format_query_mo_result

router = APIRouter(route_class=bizRoute)


@router.post("/create_sc_sta", summary="创建通感小区STA跟踪任务",
             description=description("网管STA模块下，创建通感小区STA跟踪任务;\r\n支持通过'查询MO节点属性'查询'NRSensingCell'节点传入感知小区;\r\n"
                                     "如果通过资源的方式，则选择感知小区对应通信小区即可", "10270755"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_cell_signal_task(request: Request, para: CreateScStaCellInfo) -> dict:
    mes, meIds, cells, newQueryRlts = [], [], [], []
    queryRlts = await Refs(request).get_output()

    Logger.debug(f'依赖的action输出:{queryRlts}')
    needKeys = ["ManagedElement", "SubNetwork", "ldn", "nrSensingCellId"]
    if queryRlts:
        meTemps = await MeFactory.create_by_nr(request)
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            Logger.debug(queryRltDict)

            for meId in queryRltDict:
                for cellInfo in queryRltDict[meId]:
                    if not all(key in cellInfo for key in needKeys):
                        return fail(
                            msg=f"{cellInfo.get('mocName')}的 ManagedElement;SubNetwork;ldn;nrSensingCellId 必须包含在所依赖action'查询MO节点属性'查询结果中")
                if meId not in meIds:
                    meIds.append(meId)
            newQueryRlts.append(queryRltDict)
        mes = list(filter(lambda me: me.meId in meIds, meTemps))
    else:
        mes = await MeFactory.create_by_nr(request)
        cells = await CellFactory.create(request)
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds, mes))
        if not mes: return fail(msg=f"当前无满足条件网元,请检查资源选择出的GNB是否和资源选择的CELL存在link关系!")
    ret, taskInfo = False, {}
    startTime = para.startTime.astimezone(pytz.utc) if para.startTime else None
    endTime = para.endTime.astimezone(pytz.utc) if para.endTime else None
    for me in mes:
        try:
            retTemp = para.dict()
            ret, errMsg = await CellSignalTask.create_sc(me, startTime, endTime, para.taskType, para.eventList, cells, newQueryRlts)
            if not ret:
                Logger.error(f"网元：{me.meId} 创建通感小区STA跟踪任务，错误信息: ", errMsg)
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                ret.pop("events")
                retTemp.update(ret)
                retTemp.update({"meId": me.meId})
                taskInfo.update({me.meId: retTemp})
        except BaseException as err:
            Logger.error(f"网元：{me.meId} 创建通感小区STA跟踪任务过程中程序运行错误，详情:", traceback.format_exc())
            return fail(msg=f"网元：{me.meId} 创建通感小区STA跟踪任务过程中程序运行错误，可查看log分析原因: {err}")
    return success(data={"taskInfo": taskInfo}, msg="创建通感小区STA跟踪任务成功")


