import copy
import logging
import traceback
import time
import pytz
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.sta.Schemas import FileListWithTimeRange, CreateStaCellInfo, ModifyStaCellInfo, \
    CreateMtsTask, CountSignal, GetSignalDetail, CheckSignalDetail, ClearSignal, GetMtsData, StartMtsMonitor, \
    QueryParams, CreateDttraceTask, CreateSopTask, CreateNeIperfTask, CreatUeTraceTask, CreatUeTraceTaskSchema, \
    CreatUeTraceTaskDynamicSchema
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.factories.PdnFactory import PdnFactory
from domain.models.ume.sta.Sta import Sta
from domain.models.ume.sta.TaskManager import TaskManager
from domain.platform.ActionInfo import description
from domain.platform.artifact import Artifact
from infrastructure.logger import Logger
from infrastructure.utils.Ids import IdsApi
from infrastructure.utils.TimeHandler import format_to_zero_time, format_startEndTime
from infrastructure.resource.service.Refs import Refs
from service.action.ume.sta.AutoMtsTask import AutoMtsTask
from service.action.ume.sta.CellSignalTask import CellSignalTask
from service.action.utility.performance_index_service.PerformanceIndexService import PerformanceIndexService
from service.action.ume.sta.SignalDecode import SignalDecode
from domain.factories.CellFactory import CellFactory
from domain.factories.UeFactory import UeFactory
from infrastructure.utils.ResultFormatter import format_query_mo_result
# 2025.6.12 调试界面
# STA_KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/684a2d44794842dcaa574024"
# 2025.7.1 恢复真实界面
STA_KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/6537e0f4f3b21d69d436d1ff"

router = APIRouter(route_class=bizRoute)


@router.post("/active", summary="激活STA订阅任务", description=description("网管STA模块下，激活STA订阅任务", "10270755"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def active_sta_task(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有要激活的任务，请先创建相关任务!!")
    flag, errList = True, []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            flag, err = await TaskManager(await MeFactory.create_by_meid(request, meId)).start_task(info)
            errList.append(err)
    if not flag:
        return fail(msg=errList)
    return success(data="Active task success")


@router.post("/suspend", summary="暂停STA订阅任务", description=description("网管STA模块下，暂停STA订阅任务", "10270755"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def suspend_sta_task(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有要暂停的任务，请先创建相关任务!!")
    flag, errList = True, []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            flag, err = await TaskManager(await MeFactory.create_by_meid(request, meId)).stop_task(info)
            errList.append(err)
    if not flag:
        return fail(msg=errList)
    return success(data="Stop task success")


@router.post("/delete", summary="删除STA订阅任务", description=description("网管STA模块下，删除STA订阅任务", "10270755"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def delete_sta_task(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有要删除的任务，请先创建相关任务!!")
    flag, errList = True, []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            flag, err = await TaskManager(await MeFactory.create_by_meid(request, meId)).delete_task(info)
            errList.append(err)
    if not flag:
        return fail(msg=errList)
    return success(data="Delete task success")


@router.post("/createMts", summary="创建MTS采集任务", description=description("网管STA模块下，创建输入指标的MTS采集任务", "10270755"),
             openapi_extra={
                 "refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_mts_task(request: Request, createMtsTask: CreateMtsTask):
    mes, meIds = [], []
    cells = []
    newQueryRlts = []
    queryRlts = await Refs(request).get_output()
    Logger.debug(f'依赖的action输出:{queryRlts}')
    needKeys = ["ManagedElement", "SubNetwork", "ldn", "cellLocalId"]
    if queryRlts:
        meTemps = await MeFactory.create(request)
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            Logger.debug(queryRltDict)
            for meId in queryRltDict:
                for cellInfo in queryRltDict[meId]:
                    if not all(key in cellInfo for key in needKeys):
                        return fail(msg="NRCellDU的 ManagedElement;SubNetwork;ldn;cellLocalId 必须包含在查询结果中")
                    if cellInfo.get('mocName') in ["NRCellCU", "NRCellDU"]:
                        meIds.append((meId, "5gnr"))
                    else:
                        meIds.append((meId, "lte"))
            newQueryRlts.append(queryRltDict)
        mes = list(filter(lambda me: (me.meId, me.serviceType.lower()) in meIds, meTemps))
    else:
        mes = await MeFactory.create(request)
        cells = await CellFactory.create(request)
        cellmeIds = set((cell.me.meId, hasattr(cell, 'gnb')) for cell in cells)
        mes = list(filter(lambda me: (me.meId, me.serviceType.lower() == "5gnr") in cellmeIds, mes))
    taskInfo, errList = {}, []
    for me in mes:
        ret, errmsg = await AutoMtsTask().create_task(me, cells, newQueryRlts, createMtsTask)
        if not ret:
            Logger.error("创建网元：{0} 的MTS采集任务失败，error msg: {1}".format(me.meId, errmsg))
            errList.append(errmsg)
        else:
            taskInfo.update({me.meId: ret})
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    if not taskInfo:
        return fail(data={"taskInfo": taskInfo}, msg="未创建任务，请检查资源过滤条件")
    return success(data={"taskInfo": taskInfo})


@router.post("/autoMts", summary="自动创建MTS采集任务", description=description("网管STA模块下，创建固定指标的MTS采集任务", "10270755"))
async def create_mts_task_automatic(request: Request):
    mes = await MeFactory.create(request)
    taskInfo, errList = {}, []
    for me in mes:
        ret, errmsg = await AutoMtsTask().create_mts_task_automatic(me)
        if not ret:
            errList.append(errmsg)
        else:
            taskInfo.update({me.meId: ret})
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    if not taskInfo:
        return fail(data={"taskInfo": taskInfo}, msg="未创建任务，请检查资源过滤条件")
    return success(data={"taskInfo": taskInfo})


@router.post("/exportMts", summary="导出MTS采集任务",
             description=description("网管STA模块下,导出MTS采集任务", "10263798, 10255283"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def export_mts_data(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有要导出的任务，请先创建相关任务!!")
    filePathList, errmsgList = [], []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            filePath, errmsg = await AutoMtsTask().export_mts_task_automatic(me, info)
            if errmsg:
                err = "创建网元：{0} 的MTS采集任务失败，error msg: {1}".format(meId, errmsg)
                Logger.error(err)
                errmsgList.append(err)
            else:
                filePathList.append(filePath)
    if errmsgList:
        return fail(data={"filePathList": filePathList}, msg=errmsgList)
    return success(data={"filePathList": filePathList})


@router.post("/create_signal", summary="自动创建STA小区信令跟踪任务",
             description=description("网管STA模块下，创建小区信令跟踪任务", "10227494,10296591"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_cell_signal_task(request: Request, para: CreateStaCellInfo) -> dict:
    mes, meIds, cells, newQueryRlts = [], [], [], []
    queryRlts = await Refs(request).get_output()

    Logger.debug(f'依赖的action输出:{queryRlts}')
    needKeys = ["ManagedElement", "SubNetwork", "ldn", "cellLocalId"]
    if queryRlts:
        meTemps = await MeFactory.create(request)
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            Logger.debug(queryRltDict)
            for meId in queryRltDict:
                for cellInfo in queryRltDict[meId]:
                    if not all(key in cellInfo for key in needKeys):
                        return fail(
                            msg=f"{cellInfo.get('mocName')}的 ManagedElement;SubNetwork;ldn;cellLocalId 必须包含在所依赖action查询结果中")
                    if cellInfo.get('mocName') in ["NRCellCU", "NRCellDU"]:
                        meIds.append((meId, "5gnr"))
                    else: meIds.append((meId, "lte"))
            newQueryRlts.append(queryRltDict)
        mes = list(filter(lambda me: (me.meId, me.serviceType.lower()) in meIds, meTemps))
    else:
        mes = [await MeFactory.create_by_meid(request, str(para.meId))] if para.meId else await MeFactory.create(
            request)
        cells = await CellFactory.create(request)
        if not mes or not mes[0]:
            return fail(
                msg=f"当前无满足条件网元，请检查：1.输入meId:{para.meId}是否与环境资源信息匹配；2.环境信息中是否存在网元!")
        if para.cellIds and len(mes) != 1:
            return fail(msg="当通过入参方式指定小区cellId时，必须通过输入meId或者资源选择指定目标网元!")
        cellmeIds = set((cell.me.meId, hasattr(cell, 'gnb')) for cell in cells)
        mes = list(filter(lambda me: (me.meId, me.serviceType.lower() == "5gnr") in cellmeIds, mes))
        if not mes:
            return fail(
                msg=f"当前无满足条件网元，请检查：输入meId:{para.meId}或者资源选择出的GNB是否和资源选择的CELL存在link关系!")
    startTime = para.startTime.astimezone(pytz.utc) if para.startTime else None
    endTime = para.endTime.astimezone(pytz.utc) if para.endTime else None
    ret, taskInfo = False, {}
    for me in mes:
        try:
            retTemp = para.dict()
            ret, errMsg = await CellSignalTask.create(me, startTime, endTime, para.cellIds, para.signalType, cells,
                                                      newQueryRlts, para.dataViewMode)
            if not ret:
                Logger.error(f"网元：{me.meId} 创建小区信令跟踪任务失败，错误信息: ", errMsg)
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                ret.pop("events")
                retTemp.update(ret)
                retTemp.update({"meId": me.meId})
                taskInfo.update({me.meId: retTemp})
        except BaseException as err:
            Logger.error(f"网元：{me.meId} 创建小区信令跟踪任务过程中程序运行错误，详情:", traceback.format_exc())
            return fail(msg=f"网元：{me.meId} 创建小区信令跟踪任务过程中程序运行错误，可查看log分析原因: {err}")
    return success(data={"taskInfo": taskInfo}, msg="小区信令跟踪任务创建成功")


@router.post("/modify_signal", summary="修改STA小区信令跟踪任务",
             description="【功能】网管STA模块下，修改小区信令跟踪任务【作者】10227494,10296591",
             openapi_extra={"refs": [{"actionName": "自动创建STA小区信令跟踪任务"}]})
async def modify_cell_signal_task(request: Request, para: ModifyStaCellInfo) -> dict:
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有要修改的任务，请先创建相关任务!!")
    ret, newTaskInfo = False, {}
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            if me is None:
                return fail(msg=f"当前无满足条件网元，请检查：环境资源信息中是否存在{meId}网元!")
            try:
                meTaskinfo = copy.deepcopy(info)
                ret, errMsg = await CellSignalTask.modify(me, info, para.signalType)
                if not ret:
                    Logger.error(f"网元：{me.meId} 修改小区信令跟踪任务失败，错误信息: ", errMsg)
                    return fail(data={"taskInfo": newTaskInfo}, msg=errMsg)
                else:
                    meTaskinfo.update(ret)
                    newTaskInfo.update({me.meId: meTaskinfo})
            except BaseException as err:
                Logger.error(f"网元：{me.meId} 修改小区信令跟踪任务过程中程序运行错误，详情:", traceback.format_exc())
                return fail(msg=f"网元：{me.meId} 修改小区信令跟踪任务过程中程序运行错误，可查看log分析原因: {err}")
    return success(data={"taskInfo": newTaskInfo}, msg="修改小区信令跟踪任务成功.")


@router.post("/export_sta_task_data", summary="导出STA小区信令跟踪任务数据到sftp服务器", description=description(
    "网管STA模块下，导出小区信令跟踪任务数据到sftp服务器", "10227494,10296591"), openapi_extra={
    "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def export_sta_task_data(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有任务，请先创建相关任务!!")
    ret, errList, sftpUrls = True, [], {}
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            ret, retMsg = await CellSignalTask.export_sta_task_data(me, info)
            if not ret:
                errMsg = "导出网元：{0} 的STA任务数据失败，error msg: {1}".format(meId, retMsg)
                Logger.error(errMsg)
                errList.append(errMsg)
            else:
                sftpUrls.update({meId: retMsg})
    if errList:
        return fail(data={"sftpUrls": sftpUrls}, msg=f"导出STA任务数据失败：{errList}")
    return success(data={"sftpUrls": sftpUrls}, msg=f"导出STA任务数据成功")


@router.post("/saveStaMtsData", summary="MTS采集数据解析入库",
             description=description("网管STA模块下，MTS采集数据解析入库", '夏晨10242158'),
             openapi_extra={"refs": [{"actionName": "导出MTS采集任务"}],
                            "urls": [{"name": "STA-MTS-表格-曲线", "url": STA_KPI_TABLE_URL}]
                            })
async def save_sta_task_perf_data(request: Request, fileListWithTime: FileListWithTimeRange):
    refs_path = await Refs(request).get_output("filePathList")
    Logger.info("MTS采集数据解析入库filePathList:{0}".format(refs_path))
    if not refs_path:
        return fail(msg="!!获取不到依赖action路径文件，filePathList is empty!!")
    attachment = dict(fileListWithTime)
    attachment.update({"filePathList": refs_path[0]})
    Logger.info("MTS采集数据解析入库request.state.resource:{0}".format(request.state.resource))

    def transform_perf_para():
        resource = request.state.resource
        taskId = resource.get("taskId")
        taskName = resource.get("taskName")
        caseId = resource.get("systemId")
        if caseId:
            return {"id": taskId, "name": taskName, "taskAttachment": [],
                    "cases": [{"id": caseId, "system_id": caseId, "caseAttachment": [attachment]}]}
        return {"id": taskId, "name": taskName, "cases": [], "taskAttachment": [attachment]}

    try:
        processor = PerformanceIndexService().handle_perf_request(transform_perf_para())
        files = Artifact.get_file_urls(refs_path[0])
        links = IdsApi().ids_get_hyperlink_and_send_email(request, files, [STA_KPI_TABLE_URL],
                                                          processor.ids_links, ["MTS数据分析"])
        return success(msg=f"MTS采集数据解析入库成功, 点击 {links} 跳转IDS")
    except Exception as err:
        Logger.error(traceback.format_exc())
        return fail(msg=f"MTS采集数据解析入库失败,{err}")


@router.post("/countSignal", summary="查询信令数量",
             description=description("查询满足过滤条件的信令数量,支持SOP信令查询", '侯小飞10270755'),
             openapi_extra={"refs": [{"actionName": ".*创建.*跟踪任务"}]})
async def count_signal_num(request: Request, paras: CountSignal):
    taskInfo = await Refs(request).get_output("taskInfo")
    mes = [await MeFactory.create_by_meid(request, paras.meId)] if paras.meId else await MeFactory.create(request)
    if not mes[0]:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    mesIdlist = [str(me.meId) for me in mes]
    retList, succflag = [], True
    for meTask in taskInfo:
        for meId, info in meTask.items():
            if meId not in mesIdlist:
                continue
            me = mes[mesIdlist.index(meId)]
            try:
                info.update({"queryNum": 1000})
                count = await SignalDecode.count_monitor_msg(
                    me, info, paras.msgName, paras.headDict, paras.filterStr, paras.eventList)
                retList.append({meId: count})
            except BaseException as err:
                succflag = False
                Logger.error(f"{meId}查询运行异常:", traceback.format_exc())
                retList.append({meId: f"{err}"})
    if succflag and retList:
        return success(msg=f"信令{paras.msgName}数量查询成功", data=retList)
    return fail(
        msg=f"信令{paras.msgName}数量查询异常,请确认：1.action引用关系是否正确；2.输入或资源过滤出的网元和索引任务中网元是否一致 "
            f"3.是否部分网元查询存在异常", data=retList)


@router.post("/getSignalDetail", summary="获取信令中的信元数据",
             description=description(
                 "根据参数路径获取信令中的信元数据，如果提供的路径能匹配多条，则返回所有匹配路径的值,支持SOP信令查询",
                 '侯小飞10270755'),
             openapi_extra={"refs": [{"actionName": ".*创建.*跟踪任务"}]})
async def get_signal_datas(request: Request, paras: GetSignalDetail):
    taskInfo = await Refs(request).get_output("taskInfo")
    mes = [await MeFactory.create_by_meid(request, paras.meId)] if paras.meId else await MeFactory.create(request)
    if not mes[0]:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    mesIdlist = [str(me.meId) for me in mes]
    retList, succflag = [], True
    for meTask in taskInfo:
        for meId, info in meTask.items():
            if meId not in mesIdlist:
                continue
            me = mes[mesIdlist.index(meId)]
            try:
                info.update({"queryNum": 1000})
                datas = await SignalDecode.detail_decode_signal(
                    me, info, paras.msgType, paras.msgName, paras.keyPathList, paras.msgNo, paras.filterDict,
                    paras.headDict)
                retList.append({meId: datas})
            except BaseException as err:
                succflag = False
                Logger.error(f"{meId}信令解析查询异常:", traceback.format_exc())
                retList.append({meId: f"{err}"})
    if succflag and retList:
        return success(msg=f"信令{paras.msgName}解析查询成功", data=retList)
    return fail(
        msg=f"信令{paras.msgName}解析查询异常，请确认：1.action引用关系是否正确；2.输入或资源过滤出的网元和索引任务中网元是否一致 "
            f"3.是否部分网元查询存在异常", data=retList)


@router.post("/checkSignalDetail", summary="遍历信令校验信令中信元数据",
             description=description("遍历满足条件的每条信令,校验其中指定信元路径信元值与参考值是否一致（如果提供路径能匹配多条信元路径，则会逐条判断，"
                                     "当存在一条信元路径与参考值一致则返回当前信令编号）,遍历所有信令都没有与参考值一致则返回失败",
                                     '侯小飞10270755'),
             openapi_extra={"refs": [{"actionName": "自动创建STA小区信令跟踪任务"}]})
async def ergodic_check_signal_detail(request: Request, paras: CheckSignalDetail):
    taskInfo = await Refs(request).get_output("taskInfo")
    mes = [await MeFactory.create_by_meid(request, paras.meId)] if paras.meId else await MeFactory.create(request)
    if not mes[0]:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    mesIdlist = [str(me.meId) for me in mes]
    retList, succflag = [], True
    for meTask in taskInfo:
        for meId, info in meTask.items():
            if meId not in mesIdlist:
                continue
            me = mes[mesIdlist.index(meId)]
            try:
                info.update({"queryNum": 1000})
                msgnum = await SignalDecode.ergodic_check_signal_detail_info(
                    me, info, paras.msgName, paras.keyPath, paras.expectValue, paras.msgType, paras.msgNo,
                    paras.filterDict, paras.headDict)
                if int(msgnum) == -1:
                    succflag = False
                    retList.append({meId: f"遍历信令,没有与预期信元{paras.keyPath}={paras.expectValue}一致信令"})
                else:
                    retList.append({meId: msgnum})
            except BaseException as err:
                succflag = False
                Logger.error(f"{meId}信令解析校验异常:", traceback.format_exc())
                retList.append({meId: f"{err}"})
    if succflag and retList:
        return success(msg=f"信令{paras.msgName}信令解析校验成功", data=retList)
    return fail(
        msg=f"信令{paras.msgName}信令解析校验失败，请确认：1.action引用关系是否正确；2.输入或资源过滤出的网元和索引任务中网元是否一致 "
            f"3.是否部分网元查询存在异常", data=retList)


@router.post("/clearHistorySignal", summary="清空历史信令",
             description=description("使用该关键字后，信令跟踪数据获取、校验等action只会获取到当前时间点以后的信令数据",
                                     '侯小飞10270755'),
             openapi_extra={"refs": [{"actionName": "自动创建STA小区信令跟踪任务", "refPattern": "1-*"}]})
async def clear_signal_detail(request: Request, paras: ClearSignal):
    taskInfo = await Refs(request).get_output("taskInfo")
    mes = [await MeFactory.create_by_meid(request, paras.meId)] if paras.meId else await MeFactory.create(request)
    if not mes[0]:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    mesIdlist = [str(me.meId) for me in mes]
    retList, succflag = [], True
    for meTask in taskInfo:
        for meId, info in meTask.items():
            if meId not in mesIdlist:
                continue
            me = mes[mesIdlist.index(meId)]
            try:
                if paras.operate == "清除":
                    await SignalDecode.clear_history_signal(me, info)
                else:
                    await SignalDecode.cancel_clear_history_signal(me, info)
                retList.append({meId: f"{info['taskName']}{paras.operate}历史信令成功"})
            except BaseException as err:
                succflag = False
                Logger.error(f"{meId} {info['taskName']}{paras.operate}历史信令异常:", traceback.format_exc())
                retList.append({meId: f"{err}"})
    if succflag and retList:
        return success(msg=f"{paras.operate}历史信令成功", data=retList)
    return fail(
        msg=f"{paras.operate}历史信令成功失败，请确认：1.action引用关系是否正确；2.输入或资源过滤出的网元和索引任务中网元是否一致 "
            f"3.是否部分网元查询存在异常", data=retList)


@router.post("/getMtsData", summary="获取实时MTS数据",
             description=description("网管STA模块下,获取实时MTS数据", "10263798"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def get_mts_data(request: Request, getMtsData: GetMtsData):
    taskInfo = await Refs(request).get_output("taskInfo")
    filterResList, errmsgList, flag = [], [], True
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            flag, msg = await AutoMtsTask().get_mts_task_data(me, info, getMtsData.msgNUm)
            if flag is None:
                Logger.error("获取网元：{0} 的MTS数据失败，error msg: {1}".format(meId, msg))
                errmsgList.append(msg)
            elif not flag:
                Logger.error("获取网元：{0} 的MTS数据失败，error msg: {1}".format(meId, msg))
            else:
                filterResList.append(msg)
                Artifact.submit_data_as_json(filterResList, name="mtsData",
                                             desc=f"当前{me.meId}任务{info.get('taskName')}实时MTS信息",
                                             fileType="实时MTS")
    if errmsgList:
        return fail(data={"filterResList": filterResList}, msg=errmsgList)
    return success(data={"filterResList": filterResList})


@router.post("/startMtsMonitor", summary="TDL开始MTS监控",
             description=description("网管STA模块下,开始监控实时的MTS数据，从当前action开始获取实时数据", "10263798"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def start_mts_monitor(request: Request, paras: StartMtsMonitor):
    taskInfo = await Refs(request).get_output("taskInfo")
    threads, respmsgList, queryParaslist = [], [], []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            resp, threadId, queryParas = await AutoMtsTask().start_mts_task_monitor(me, info, paras.msgNUm)
            threads.append(str(threadId))
            queryParaslist.append(queryParas)
    if respmsgList:
        return fail(data={"threads": threads}, msg=respmsgList)
    return success(data={"threads": threads, "queryParaslist": queryParaslist})


@router.post("/checkMtsData", summary="校验实时MTS数据",
             description=description("网管STA模查询实时MTS监控数据，可获取指定指标的最新值/最大值/最小值/平均值/所有值",
                                     "10263798"),
             openapi_extra={"refs": [{"actionName": "获取实时MTS数据", "refPattern": "1-*"}]})
async def query_realtime_mts_data(request: Request, queryParams: QueryParams):
    filterResList = await Refs(request).get_output("filterResList")
    successResult, failResult = [], []
    for mtsCounter, checkIndex in queryParams.mtsCounters.items():
        Flag, Result = await AutoMtsTask().get_real_mts_specified_values(filterResList, queryParams.queryType,
                                                                         mtsCounter, checkIndex, queryParams.cellId,
                                                                         queryParams.CpfUeId)
        if not Flag:
            Logger.error("校验MTS数据失败，error msg: {0}".format(Result))
            failResult.append(Result)
        else:
            successResult.append(Result)
    if not failResult:
        return success(data=successResult, msg=f"查询{queryParams.queryType}成功")
    return fail(data=failResult, msg=f"查询{queryParams.queryType}存在失败，请检查入参")


@router.post("/create_dttrace", summary="创建STA基站跟踪DTTRACE任务(建议使用:创建STA基站跟踪任务)",
             description="【功能】网管STA模块下，创建基站跟踪DTTRACE任务【作者】10263798")
async def create_dttrace(request: Request, para: CreateDttraceTask):
    mes = await MeFactory.create(request)
    pdns = PdnFactory.create(request)
    try:
        taskInfo = {}
        for pdn in pdns:
            if para.startTime and para.endTime:
                startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                         para.endTime.astimezone(pytz.utc))
            else:
                startTime, endTime = format_startEndTime()
            parameters = {"Ping包长度尾数": para.pingPacketLengthRemainder, "最大UE数": para.ueNum}
            if not para.ipAddress:
                parameters.update({"IP地址": pdn.cnIp})
            else:
                parameters.update({"IP地址": para.ipAddress})
            for me in mes:
                paras = {"taskName": "Hyper_Automation_" + me.meId + "_" + str(int(time.time())), "dataViewMode": "2",
                         "serviceType": "5gnr",
                         "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                         "menuId": "neTrace", "taskType": "DTTrace", "parameters": parameters}
                taskRlt, errMsg = await Sta(me).create_task(paras)
                if errMsg:
                    return fail(data={"taskInfo": taskInfo}, msg=errMsg)
                else:
                    taskRlt.update({"PDN IP": pdn.ip, "ipAddress": para.ipAddress})
                    taskInfo.update({me.meId: taskRlt})
        if taskInfo:
            return success(data={"taskInfo": taskInfo}, msg="Base station dttrace task is created successful.")
        return fail(data={"taskInfo": taskInfo}, msg="Base station dttrace task is created failed.")
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/createSop", summary="创建SOP跟踪任务", description=description("网管STA模块下，创建Sop跟踪任务", "10255283"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_Sop_task(request: Request, createSopTask: CreateSopTask):
    mes, meIds = [], []
    cells = []
    newQueryRlts = []
    queryRlts = await Refs(request).get_output()

    Logger.debug(f'依赖的action输出:{queryRlts}')
    needKeys = ["ManagedElement", "SubNetwork", "ldn", "cellLocalId"]
    if queryRlts:
        meTemps = await MeFactory.create(request)
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            Logger.debug(queryRltDict)
            for meId in queryRltDict:
                for cellInfo in queryRltDict[meId]:
                    if not all(key in cellInfo for key in needKeys):
                        return fail(msg="NRCellDU的 ManagedElement;SubNetwork;ldn;cellLocalId 必须包含在查询结果中")
                    if cellInfo.get('mocName') in ["NRCellCU", "NRCellDU"]:
                        meIds.append((meId, "5gnr"))
                    else:
                        meIds.append((meId, "lte"))
            newQueryRlts.append(queryRltDict)
        mes = list(filter(lambda me: (me.meId, me.serviceType.lower()) in meIds, meTemps))
    else:
        mes = await MeFactory.create(request)
        cells = await CellFactory.create(request)
        cellmeIds = set((cell.me.meId, hasattr(cell, 'gnb')) for cell in cells)
        mes = list(filter(lambda me: (me.meId, me.serviceType.lower() == "5gnr") in cellmeIds, mes))
    taskInfo, errList = {}, []
    for me in mes:
        ret, errmsg = await AutoMtsTask().create_task(me, cells, newQueryRlts, createSopTask, "SOP")
        if not ret:
            Logger.error("创建网元：{0} 的SOP跟踪任务失败，error msg: {1}".format(me.meId, errmsg))
            errList.append(errmsg)
        else:
            taskInfo.update({me.meId: ret})
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo})


@router.post("/createIperf", summary="创建STA基站灌包任务",
             description="【功能】网管STA模块下，创建基站跟踪DTTRACE任务【作者】10255283")
async def create_ne_iperf(request: Request, para: CreateNeIperfTask):
    mes = await MeFactory.create(request)
    ues = UeFactory.create(request)
    taskInfo, errList = {}, []
    for me in mes:
        taskRlt, errMsg = await AutoMtsTask().create_ne_iperf_task(me, ues, para)
        if not taskRlt:
            Logger.error("创建网元：{0} 创建基站跟踪DTTRACE任务失败，error msg: {1}".format(me.meId, errMsg))
            errList.append(errMsg)
        else:
            taskInfo.update({me.meId: taskRlt})
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo})


@router.post("/createUeTrace", summary="创建STA用户跟踪任务",
             description="【功能】网管STA模块下，创建用户跟踪任务【作者】10263798")
async def create_ue_trace_task(request: Request, para: CreatUeTraceTask):
    mes = await MeFactory.create(request)
    taskInfo, errList = {}, []
    startTime = para.startTime.astimezone(pytz.utc) if para.startTime else None
    endTime = para.endTime.astimezone(pytz.utc) if para.endTime else None
    for me in mes:
        ret, errmsg = await AutoMtsTask().create_ue_trace_task(me, startTime, endTime, para)
        if not ret:
            Logger.error("创建网元：{0} 的UE TRACE采集任务失败，error msg: {1}".format(me.meId, errmsg))
            errList.append(errmsg)
        else:
            taskInfo.update({me.meId: ret})
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo})


# schema联动实例，不要删除
@router.post("/createSchemaTest", summary="创建schema联动任务",
             description="【功能】网管STA模块下，创建用户跟踪任务【作者】10263798")
async def create_ue_trace_task(request: Request, para: CreatUeTraceTaskSchema):
    mes = await MeFactory.create(request)
    taskInfo, errList = {}, []
    startTime = para.startTime.astimezone(pytz.utc) if para.startTime else None
    endTime = para.endTime.astimezone(pytz.utc) if para.endTime else None
    for me in mes:
        ret, errmsg = await AutoMtsTask().create_ue_trace_task(me, startTime, endTime, para)
        if not ret:
            Logger.error("创建网元：{0} 的UE TRACE采集任务失败，error msg: {1}".format(me.meId, errmsg))
            errList.append(errmsg)
        else:
            taskInfo.update({me.meId: ret})
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo})


# schema动态联动实例，不要删除
@router.post("/createSchemaDynamicTest", summary="创建schema动态联动任务",
             description="【功能】网管STA模块下，创建用户跟踪任务【作者】10263798")
async def create_ue_trace_task(request: Request, para: CreatUeTraceTaskDynamicSchema):
    mes = await MeFactory.create(request)
    taskInfo, errList = {}, []
    startTime = para.startTime.astimezone(pytz.utc) if para.startTime else None
    endTime = para.endTime.astimezone(pytz.utc) if para.endTime else None
    for me in mes:
        ret, errmsg = await AutoMtsTask().create_ue_trace_task(me, startTime, endTime, para)
        if not ret:
            Logger.error("创建网元：{0} 的UE TRACE采集任务失败，error msg: {1}".format(me.meId, errmsg))
            errList.append(errmsg)
        else:
            taskInfo.update({me.meId: ret})
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo})
