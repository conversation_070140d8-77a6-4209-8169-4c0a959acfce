import re
from datetime import datetime
from enum import Enum
from typing import TypeVar, Literal, Optional

from pydantic import BaseModel, Field, validator

from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")


class FileListWithTimeRange(BaseModel):
    before_upgrade_beg_time: datetime | None = None
    before_upgrade_end_time: datetime | None = None
    after_upgrade_beg_time: datetime | None = None
    after_upgrade_end_time: datetime | None = None


class SignalTypeEnum(str, Enum):
    f1 = "F1"
    mecs = "MECS"
    ng = "NG"
    nrppa = "NRPPA"
    x2 = "X2"
    xn = "XN"
    uu = "UU"
    f1Paging = "F1_PAGING"
    ngPaging = "NG_PAGING"
    e2ap = "E2AP"
    S1Paging = "S1_PAGING"
    s1 = "S1"


class CreateStaCellInfo(BaseModel):
    startTime: datetime | None = None
    endTime: datetime | None = None
    meId: int = Field(None,
                      description="网元ID，默认不填，表示默认在环境中每个网元都创建信令跟踪任务，对于多网元环境，可以通过资源选择或者该参数指定网元信息")
    cellIds: str = Field(default=None, description="小区cellId，默认不填，表示跟踪当前环境资源中每个网元下所有小区；如果跟踪指定小区，可以通过资源选择过滤出指定小区，"
                                                   "注意：如果通过该参数进行过滤小区，必须通过资源选择或meId属性指定小区所在唯一网元，格式："
                                                   "cellId1,cellId2 举例：31,32", regex=r"^$|\d{1,5},?")
    signalType: list[SignalTypeEnum] = [SignalTypeEnum.uu, SignalTypeEnum.ng, SignalTypeEnum.xn, SignalTypeEnum.x2,
                                        SignalTypeEnum.s1]
    dataViewMode: Literal["1", "2"] = Field("2", description='1:表示跟踪所有内部信令；2:默认，表示跟踪标准信令')

    @validator("cellIds", pre=True)
    def format_cellids(cls, v):
        return v.replace(" ", "") if v else ""


class ModifyStaCellInfo(BaseModel):
    signalType: list[SignalTypeEnum] = [SignalTypeEnum.uu, SignalTypeEnum.ng, SignalTypeEnum.xn, SignalTypeEnum.x2]


MTS_TEMPLATE = ["UE_MAC_THROUGHPUT", "UE_SRS_SINR", "CELL_BLER", "CELL_MAC_THROUGHPUT"]

MTS_CHECK_VALUE = {"UE_AVG_MCS": ["UeDlAvgMCS", "UeDlAvgMCS"],
                   "UE_MAC_THROUGHPUT": ["ulMACThroughput(Mbps)", "dlMACThroughput(Mbps)"],
                   "UE_PDSCH_INFO": ["cqiValid", "pmiCqiValid", "ri", "srsSinr"],
                   "CELL_PHY_THROUGHPUT": ["phyDlTBSize(Mbps)", "phyulTBSize(Mbps)"]
                   }


class CreateMtsTask(BaseModel):
    Counters: DataT = Field(default=MTS_TEMPLATE, type="jsonEditor",
                            description="按照标准格式填写MtsCounters,例：['UE_AVG_MCS','UE_MAC_THROUGHPUT']")


class CountSignal(BaseModel):
    msgName: str = Field(..., description="必填，需要查询的信令名,举例: RRCRelease")
    headDict: str | None = Field(None, description="选填,对信令过头部过滤的字典,例: RNTI=28,Cell ID=1")
    filterStr: str | None = Field(None, description="选填,筛选字段,对信元过滤字符串,例: rrc_TransactionIdentifier=3")
    eventList: str | None = Field(None, description="选填,当创建STA任务有多种事件类型时，可以指定事件类型,例: UU,NG")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元信令操作")

    @validator("headDict")
    def attrs_to_dict(cls, v):
        if not v:
            return None
        d = dict()
        for item in v.split(','):
            k, v = item.split('=')
            d[k.strip()] = v.strip()
        return d

    @validator("msgName", "meId")
    def del_rl_space(cls, v):
        return v.strip() if v else v

    @validator("filterStr")
    def attrs_add_space(cls, v):
        return re.sub(r"\s*=\s*", " = ", v.strip()).strip() if v else v

    @validator("eventList")
    def attrs_to_list(cls, v):
        return [_.upper().strip() for _ in v.split(',')] if v else v


class GetSignalDetail(BaseModel):
    msgName: str = Field(..., description="必填，需要查询的信令名,举例: RRCReconfiguration")
    keyPathList: str | None = Field(..., description="必填,需要查看的信元路径,以点\'.\'隔开参数路径(支持正则),多个路径中间以逗号\',\'分割"
                                                     "例: drb_ToAddModList.elem\[\d\].drb_Identity,criticalExtensions.t")
    msgNo: str | None = Field('0',
                              description="选填,同一个任务中若有多个同名msgName信令上报，使用本参数区分，默认查看最新一个")
    msgType: str | None = Field(None,
                                description="选填,目标信令对应信令类型，若存在信令名称相同、信令类型不同时，可用本参数区分")
    filterDict: str | None = Field(None,
                                   description="选填,筛选字段(路径支持正则),对信元过滤字典,例: rrc_TransactionIdentifier=0")
    headDict: str | None = Field(None, description="选填,对信令过头部过滤的字典,例: RNTI=28,Cell ID=1")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元信令操作")

    @validator("headDict", "filterDict")
    def attrs_to_dict(cls, v):
        if not v:
            return None
        d = dict()
        for item in v.split(','):
            k, v = item.split('=')
            d[k.strip()] = v.strip()
        return d

    @validator("msgName", "msgNo", "msgType", "meId")
    def del_rl_space(cls, v):
        return v.strip() if v else v

    @validator("keyPathList")
    def attrs_to_list(cls, v):
        return [_.strip() for _ in v.split(',')] if v else v


class CheckSignalDetail(BaseModel):
    msgName: str = Field(..., description="必填，需要查询的信令名,举例: RRCReconfiguration")
    keyPath: str | None = Field(..., description="必填,需要校验信元的路径,以点隔开参数路径(路径支持正则),"
                                                 "例: drb_ToAddModList.elem\[\d\].drb_Identity")
    expectValue: str | None = Field(None,
                                    description="选填,信元预期值,默认不填,表示不校验信元,只要keyPath路径存在值就返回该信令的编号,例: 1")
    msgNo: str | None = Field(None,
                              description="选填,需要遍历校验的信令序号,默认不填,表示遍历所有msgName信令,支持0-1,3,5-6简写模式")
    msgType: str | None = Field(None,
                                description="选填,目标信令对应信令类型,若存在信令名称相同、信令类型不同时,可用本参数区分,例: UU")
    filterDict: str | None = Field(None,
                                   description="选填,筛选字段(路径支持正则),对信元过滤字典,例: rrc_TransactionIdentifier=0")
    headDict: str | None = Field(None, description="选填,对信令过头部过滤的字典,例: RNTI=28,Cell ID=1")
    meId: str | None = Field(None, description="网元Id,用于区分多网元环境中的网元,不填会对环境中所有网元信令操作")

    @validator("headDict", "filterDict")
    def attrs_to_dict(cls, v):
        if not v:
            return None
        d = dict()
        for item in v.split(','):
            k, v = item.split('=')
            d[k.strip()] = v.strip()
        return d

    @validator("msgName", "keyPath", "expectValue", "msgNo", "msgType", "meId")
    def del_rl_space(cls, v):
        return v.strip() if v else v


class ClearSignal(BaseModel):
    meId: str | None = Field(None, description="网元Id,用于区分多网元环境中的网元,不填会对环境中所有网元信令操作")
    operate: Literal["清除", "取消清除"] = Field("清除", description='操作类型,清除:表示该动作为清除索引任务历史信令，'
                                                                     '取消清除:表示索引任务中已下发的清空信令动作都失效')


class StartMtsMonitor(BaseModel):
    msgNUm: int | None = Field(600, description="选填,从MTS任务中监控数据量,默认值600")


class GetMtsData(BaseModel):
    msgNUm: int | None = Field(20, description="选填,从开始MTS监控任务中获取的数据量,默认值20；"
                                               "注意该数据是在开始监控的数据内获取的，请确保获取数据量小于监控数据量")


class QueryType(str, Enum):
    new = "最新值"
    average = "平均值"
    max = "最大值"
    min = "最小值"
    all = "所有值"


DEFAULT_MTS_Template = {"CELL_MAC_THROUGHPUT": "ulMACThroughput(Mbps)", "CELL_BLER": "ulbler(%)",
                        "UE_SRS_SINR": "srsSinrPort[0](dB)", "UE_MAC_THROUGHPUT": "ulMACThroughput(Mbps)"}


class QueryParams(BaseModel):
    queryType: QueryType
    cellId: str = Field(None, description='MTS校验的小区数据,不传表示不过滤,取所有数据,支持多小区,小区间用空格分隔,举例:1,2')
    CpfUeId: str = Field(None, description='MTS校验的指定CpfUeId数据,不传表示不过滤,取所有数据,支持多个,用空格分隔,举例:115,118')
    mtsCounters: DataT = Field(DEFAULT_MTS_Template, type="jsonEditor",
                               description='按照标准格式填写MtsCounters,例：{"UE_AVG_MCS": "UeDlAvgMCS"},'
                                           '格式解释："UE_AVG_MCS"为跟踪指标, "UeDlAvgMCS"为跟踪指标小校验的子校验目标,'
                                           '如果一个指标下有多个子校验指标,可用列表方式，格式如：{"UE_AVG_MCS": ["UeDlAvgMCS", "UeUlAvgMCS"]}')


DTTRACE_TEMPLATE = ["GNB_DTTRACE_UE_INFO"]
SIGNAL_TEMPLATE = ["E2AP", "NG"]


class CreateDttraceTask(BaseModel):
    ipAddress: str = Field(..., description='ip地址, 格式：ipv4地址,举例：*******')
    eventList: DataT = Field(default=DTTRACE_TEMPLATE, type="jsonEditor",
                             description="按照标准格式填写DTTrace Events,例：['UE_AVG_MCS','UE_MAC_THROUGHPUT']")
    pingPacketLengthRemainder: str = Field("3", description="Ping包长度尾数")
    ueNum: str = Field("5", description="最大UE数")
    startTime: Optional[datetime] | None = None
    endTime: Optional[datetime] | None = None


SOP_TEMPLATE = ["UE_CONTEXT_SETUP_STAT", "PDU_SESSION_RESOURCE_SETUP_INFO", "UE_CAPABILITY_STAT", "UE_RRC_SETUP_STAT"]
ASIC_TEMPLATE = ["AUTOLOG_SUMMARY_INFO"]
ATUTRACE_TEMPLATE = ['UE_PUSCH_SINR', 'UE_PDCP_THROUGHPUT_PKT']


class CreateSopTask(BaseModel):
    Counters: DataT = Field(default=SOP_TEMPLATE, type="jsonEditor",
                            description="按照标准格式填写SopCounters,例：['PDU_SESSION_RESOURCE_REL_INFO_NEW','UE_HO_SRC_INFO']")


class CreateNeIperfTask(BaseModel):
    ipAddress: str | None= Field(None, description='ip地址, 格式：ipv4地址,举例：*******;*******;不填时,将会从UE中获取全部的IPV4地址')
    IperfType: Literal["UDP灌包", "TCP灌包"] = Field("UDP灌包", description="灌包协议种类")
    IperfDirection: Literal["下行", "上行"] = Field("下行", description="灌包方向")
    UdpBufferSize: int = Field(1472, description='UDP灌包缓冲区长度，单位Byte')
    UdpBandWidth: int = Field(100, description='UDP灌包灌包带宽，单位Mbps')
    IperfDuration: int = Field(9999, description='灌包时长，单位s')
    port: int = Field(12345, description='端口号')
    TcpWindowSize: int = Field(512, description='TCP窗口大小，单位KByte')
    DlIperfNumber: int = Field(1, description='下行灌包UE的iperf个数')
    InterSiteSwitch: Literal["Close", "Open"] = Field("Close", description=" 跨站任务开关，仅在普通下行灌包任务中生效")


DEFAULT_Trace_Template = [
    "E2AP",
    "F1",
    "MECS",
    "NG",
    "UU",
    "X2",
    "XN"
]

Signal_Parameters_Template = {
    "跟踪深度": "1",
    "小区业务跟踪开关": "0"
}

UeLog_Parameters_Template = {
    "UeNAS层接入类型": "0,1,2",
    "TraceIDs": "None",
    "Spa日志自定义ID": "0",
    "抓包时长（分钟）": "1",
    "UPA抓包协议层上下行": "2",
    "GTPU层抓包长度（字节）": "40",
    "GTPU层抓包开关": "0",
    "PDCP层抓包长度（字节）": "32",
    "PDCP层抓包开关": "0",
    "RLC层抓包长度（字节）": "7",
    "RLC层抓包开关": "0",
    "MAC层抓包长度（字节）": "10",
    "MAC层抓包开关": "0",
    "PPSH层1日志采样类型": "2,3,4,11,12",
    "PPSL层1日志采样类型": "1,5,6,7,8,9,10,13,14",
    "UDS层1日志采样类型": "0,2,4,5,11,12,13,14,23,24",
    "UeLog_5QI": "1,2,5"
}

SpecialUeMetrics_Parameters_Template = {
    "UeNAS层接入类型": "0,1,2",
    "上报周期": "5s",
    "WNG终端识别开关": "0",
    "TraceID跟踪模式": "0",
    "TraceIDs": "a0a1f2",
    "DtIds": "None",
    "高速业务识别开关": "0",
    "业务识别最大UE数": "3",
    "语音识别开关": "0",
    "用户面识别业务的AppIdList": "None",
    "高频地铁地址匹配列表": "None",
    "SPA强制1s周期上报开关": "0"
}


class DataViewMode(str, Enum):
    show = '展示'
    saveFile = '保存文件'


class CreatUeTraceTask(BaseModel):
    dataViewMode: list[DataViewMode] = Field([DataViewMode.show, DataViewMode.saveFile], description='数据查看方式,\
                                             默认为展示和保存文件,当选择的事件包含UeLog时此处只能选择保存文件,且此处不可为空')
    traceType: Literal["TraceID 跟踪", "UEID 跟踪"] = Field(default="TraceID 跟踪")
    taskType: Literal["Signal", "SpecialUeMetrics", "UeLog", "MTS"] = Field(default="Signal", description="参数设置, \
                                                                     如选取信令类型则此处需要选择Signal")
    idList: str = Field(None,
                        description='多个用逗号分割,例如traceId时：123456,234567 ueId时:460112101045929,460112101045930')
    traceCounters: DataT = Field(default=DEFAULT_Trace_Template, type="jsonEditor",
                                 description='按照示例填写。TraceID 跟踪才可以同时跟踪多种类型；UEID 跟踪 只能跟踪单一类型')
    parameters: DataT = Field(default=Signal_Parameters_Template, type="jsonEditor",
                              description='跟踪Signal,UeLog或SpecialUeMetrics时此处需要修改，否则不需关注，此处各参数为网管默认值')
    startTime: datetime | None = None
    endTime: datetime | None = None

    @validator("idList")
    def validate_idList(cls, idList):
        if idList is None:
            return True
        d = list()
        for Id in idList.split(','):
            id = Id.strip()
            if not re.match(r"^[0-9A-Fa-f]{6}$", id) and not re.match(r"^\d{1,15}$", id):
                raise ValueError(
                    "Invalid Id: {},输入为traceId时：六位十六进制数，支持单个输入或多个以逗号分隔输入。示例：a0a1f2 或 a0a1f2,a0a1f3, \
                    输入ueId时：支持单个输入，多个以逗号分隔输入，区间输入，且最大支持15位。示例：12 或 11,12 或 [12,23]".format(
                        id))
            else:
                d.append(id)
        return d

    @validator("dataViewMode")
    def validate_dataViewMode(cls, dataViewMode):
        if dataViewMode is None:
            raise ValueError("Invalid dataViewMode,此处不可为空")
        if dataViewMode == ["展示"]:
            return "0"
        elif dataViewMode == ["保存文件"]:
            return "1"
        else:
            return "2"

    @classmethod
    def schema(cls):
        schema_map = [
            ({"taskType": ["Signal"]}, {"parameters": Signal_Parameters_Template}),
            ({"taskType": ["SpecialUeMetrics"]}, {"parameters": SpecialUeMetrics_Parameters_Template}),
            ({"taskType": ["UeLog"]}, {"parameters": UeLog_Parameters_Template})
        ]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map)


CreatUeTraceTask.schema()


# schema动态联动实例，不要删除
class CreatUeTraceTaskSchema(BaseModel):
    '''
    schema 联动 根据不同的依赖关系 开发 支持不同的模版，以 支持 规范的生成对应的jsonschema ,供 前后端 规范通信！
    目前已支持的模版及使用说明文档地址如下：若模版有相似的依赖关系 用户可直接复用对应的类方法 去封装模版；若没有，需要继续开发 增添对应模版；
     https://i.zte.com.cn/#/space/67aa26ca3aef43f4b734f3f627989ab7/wiki/page/49b621f246304e41a3f6a114194dd615/view
    '''
    traceType: Literal["TraceID 跟踪", "UEID 跟踪"] = Field(default="TraceID 跟踪")
    traceIdList: str = Field(None, description='TraceID如果多个用逗号分割,例如：123456,234567')
    ueIdList: str = Field(None, description='UEID如果多个用逗号分割,例如：460112101045929,460112101045930')
    startTime: datetime | None = None
    endTime: datetime | None = None
    traceCounters: DataT = Field(default=DEFAULT_Trace_Template, type="jsonEditor",
                                 description='按照示例填写。TraceID 跟踪才可以同时跟踪多种类型；UEID 跟踪 只能跟踪单一类型')
    dataViewMode: Literal["1", "2"] = Field("2", description='1:表示跟踪所有内部信令；2:默认，表示跟踪标准信令')

    @classmethod
    def schema(cls):
        dependency_dict = {
            "traceType": {
                "TraceID 跟踪": [
                    {"traceIdList": "123456"},
                    {"traceCounters":
                        [
                            {
                                "taskType": "Signal",
                                "eventList": ["F1", "UU", "UU_v190630"]
                            },
                            {
                                "taskType": "MTS",
                                "eventList": ["RB_PHS_RTT_STAT", "RB_PLS_RTT_STAT"]
                            },
                            {
                                "taskType": "SOP",
                                "eventList": ["CDT_MOBILITY_PDU_SESSION_INFO"]
                            }
                        ]
                    }
                ],
                "UEID 跟踪": [
                    {"ueIdList": "46001"},
                    {"traceCounters":
                        [
                            {
                                "taskType": "Signal",
                                "eventList": ["F1", "UU", "UU_v190630"]
                            }
                        ]
                    }
                ]
            }
        }
        return MySchema().generate_complex_trace_schema(super().schema(), dependency_dict)

    @validator("traceIdList")
    def validate_traceIdList(cls, traceIdList):
        if traceIdList is None:
            return True
        d = list()
        for traceId in traceIdList.split(','):
            traceId = traceId.strip()
            if not re.match(r"^[0-9A-Fa-f]{6}$", traceId):
                raise ValueError(
                    "Invalid traceId: {},输入格式：六位十六进制数，支持单个输入或多个以逗号分隔输入。示例：a0a1f2 或 a0a1f2,a0a1f3".format(
                        traceId))
            else:
                d.append(traceId)
        return d

    @validator("ueIdList")
    def validate_ueIdList(cls, ueIdList):
        if ueIdList is None:
            return True
        d = list()
        for ueId in ueIdList.split(','):
            ueId = ueId.strip()
            if not re.match(r"^\d{1,15}$", ueId):
                raise ValueError(
                    "Invalid ueId: {},输入格式：支持单个输入，多个以逗号分隔输入，区间输入，且最大支持15位。示例：12 或 11,12 或 [12,23]".format(
                        ueId))
            else:
                d.append(ueId)
        return d


# CreatUeTraceTaskSchema.schema()


# schema动态联动实例，不要删除
class CreatUeTraceTaskDynamicSchema(BaseModel):
    '''
    schema 联动 根据不同的依赖关系 开发 支持不同的模版，以 支持 规范的生成对应的jsonschema ,供 前后端 规范通信！
    目前已支持的模版及使用说明文档地址如下：若模版有相似的依赖关系 用户可直接复用对应的类方法 去封装模版；若没有，需要继续开发 增添对应模版；
     https://i.zte.com.cn/#/space/67aa26ca3aef43f4b734f3f627989ab7/wiki/page/49b621f246304e41a3f6a114194dd615/view
    '''
    traceType: Literal["TraceID 跟踪", "UEID 跟踪"] = Field(default="TraceID 跟踪")
    traceIdList: str = Field(None, description='TraceID如果多个用逗号分割,例如：123456,234567')
    ueIdList: str = Field(None, description='UEID如果多个用逗号分割,例如：460112101045929,460112101045930')
    startTime: datetime | None = None
    endTime: datetime | None = None
    traceCounters: DataT = Field(default=DEFAULT_Trace_Template, type="jsonEditor",
                                 description='按照示例填写。TraceID 跟踪才可以同时跟踪多种类型；UEID 跟踪 只能跟踪单一类型')
    dataViewMode: Literal["1", "2"] = Field("2", description='1:表示跟踪所有内部信令；2:默认，表示跟踪标准信令')

    @classmethod
    def schema(cls):
        dependency_dict = {
            "traceType": {
                "TraceID 跟踪": [
                    {"traceIdList": "123456"},
                    {"traceCounters":
                        [
                            {
                                "taskType": "Signal",
                                "eventList": ["F1", "UU", "UU_v190630"]
                            },
                            {
                                "taskType": "MTS",
                                "eventList": ["RB_PHS_RTT_STAT", "RB_PLS_RTT_STAT"]
                            },
                            {
                                "taskType": "SOP",
                                "eventList": ["CDT_MOBILITY_PDU_SESSION_INFO"]
                            }
                        ]
                    }
                ],
                "UEID 跟踪": [
                    {"ueIdList": "46001"},
                    {"traceCounters":
                        [
                            {
                                "taskType": "Signal",
                                "eventList": ["F1", "UU", "UU_v190630"]
                            }
                        ]
                    }
                ]
            }
        }
        dynamic_hide_field = ["ueIdList", "traceIdList"]  # 注意列表顺序不要写反了,前面是需要先被隐藏的
        return MySchema().generate_complex_dynamic_schema(super().schema(), dependency_dict, dynamic_hide_field)

    @validator("traceIdList")
    def validate_traceIdList(cls, traceIdList):
        if traceIdList is None:
            return True
        d = list()
        for traceId in traceIdList.split(','):
            traceId = traceId.strip()
            if not re.match(r"^[0-9A-Fa-f]{6}$", traceId):
                raise ValueError(
                    "Invalid traceId: {},输入格式：六位十六进制数，支持单个输入或多个以逗号分隔输入。示例：a0a1f2 或 a0a1f2,a0a1f3".format(
                        traceId))
            else:
                d.append(traceId)
        return d

    @validator("ueIdList")
    def validate_ueIdList(cls, ueIdList):
        if ueIdList is None:
            return True
        d = list()
        for ueId in ueIdList.split(','):
            ueId = ueId.strip()
            if not re.match(r"^\d{1,15}$", ueId):
                raise ValueError(
                    "Invalid ueId: {},输入格式：支持单个输入，多个以逗号分隔输入，区间输入，且最大支持15位。示例：12 或 11,12 或 [12,23]".format(
                        ueId))
            else:
                d.append(ueId)
        return d


CreatUeTraceTaskDynamicSchema.schema()


class ModifyDttraceTask(BaseModel):
    ipAddress: str = Field(..., description='ip地址, 格式：ipv4地址,举例：*******')
    eventList: DataT = Field(default=[], type="jsonEditor",
                             description="按照标准格式填写DTTrace Events,例：['UE_AVG_MCS','UE_MAC_THROUGHPUT']")
    pingPacketLengthRemainder: str = Field("3", description="Ping包长度尾数")
    ueNum: str = Field("5", description="最大UE数")
    startTime: datetime | None
    endTime: datetime | None


class AtutraceTask(BaseModel):
    eventList: DataT = Field(default=[], type="jsonEditor",
                             description="按照标准格式填写DTTrace Events,例：['UE_PUSCH_SINR','UE_PDCP_THROUGHPUT_PKT']")
    startTime: datetime | None
    endTime: datetime | None


class NssaitraceTask(BaseModel):
    eventList: DataT = Field(default=[], type="jsonEditor",
                             description="按照标准格式填写DTTrace Events,例：['SCH_DL_SLA_CLOSE_LOOP_TIME_DELAY_STAT','UE_CSI_CQI_PMI_INFO']")
    ueIpAddressList: str = Field(description="ue的ip地址，多个ip用逗号分开，例如*******,*******")
    fiveQIList: str = Field(description="跟踪的5QI，多个5QI用逗号分开，例如8,9")
    sliceSDList: str = Field(description="跟踪切片的sd，多个sd用逗号分开，例如1118481,1118486")
    sliceSSTList: str = Field(description="跟踪切片的sst，多个sst用逗号分开，例如1,2")
    startTime: datetime | None
    endTime: datetime | None

    @validator("ueIpAddressList")
    def validate_ueIpAddressList(cls, ueIpAddressList):
        if ueIpAddressList is None:
            return []
        d = list()
        for ueIpAddress in ueIpAddressList.split(','):
            ueIpAddress = ueIpAddress.strip()
            d.append(ueIpAddress)
        return d


class TraceIDTraceTask(BaseModel):
    traceIdList: str = Field(description="6位16进制数，输入多个则用逗号分割，如a0a1f2或a0a1f2,a0a1f3")
    eventList: DataT = Field(default=[], type="jsonEditor",
                             description='按照标准格式填写的Events,如不涉及可不填.例：["SCH_DL_SLA_CLOSE_LOOP_TIME_DELAY_STAT","UE_CSI_CQI_PMI_INFO"]')
    signalType: list[SignalTypeEnum] = [SignalTypeEnum.uu, SignalTypeEnum.ng, SignalTypeEnum.xn, SignalTypeEnum.x2,
                                        SignalTypeEnum.s1, SignalTypeEnum.f1]
    traceDepthFilter: Literal["最小", "中等", "最大"] = Field("最大",
                                                              description='信令跟踪的跟踪深度，默认为最大，仅在跟踪类型选择了Signal时生效')
    cellTrafficTraceSwitchFilter: Literal["关闭", "打开"] = Field("关闭",
                                                                  description='小区业务跟踪开关,默认为关闭，仅在跟踪类型选择了Signal时生效')
    startTime: datetime | None
    endTime: datetime | None

    @validator("traceIdList")
    def validate_traceIdList(cls, traceIdList):
        if traceIdList is None:
            return True
        d = list()
        for traceId in traceIdList.split(','):
            traceId = traceId.strip()
            d.append(traceId)
        return d

    @validator("traceDepthFilter")
    def validate_traceDepthFilter(cls, traceDepthFilter):
        if traceDepthFilter is None:
            return True
        if traceDepthFilter == "最小":
            return 0
        elif traceDepthFilter == "中等":
            return 1
        elif traceDepthFilter == "最大":
            return 2
        else:
            return False

    @validator("cellTrafficTraceSwitchFilter")
    def validate_cellTrafficTraceSwitchFilter(cls, cellTrafficTraceSwitchFilter):
        if cellTrafficTraceSwitchFilter is None:
            return True
        if cellTrafficTraceSwitchFilter == "关闭":
            return 0
        elif cellTrafficTraceSwitchFilter == "打开":
            return 1
        else:
            return False


class MiaDecode(BaseModel):
    taskId: str = ""
    filename: str = ""
    user: str = "10288354"
    fileSize: str = ""


DTTRACE_PARAMETERS_TEMPLATE = {"Ping包长度尾数": 3, "最大UE数": 5}
SIGNAL_PARAMETERS_TEMPLATE = {"Node ID跟踪": "null", "信令跟踪方向": "全上报"}


class CreateStationTask(BaseModel):
    dataViewMode: list[DataViewMode] = Field([DataViewMode.show, DataViewMode.saveFile],
                                             description='数据查看方式,默认为展示和保存文件,且此处不可为空')
    taskType: Literal["SOP", "DTTrace", "ASIC", "ATUTrace", "Signal"] = Field(default="DTTrace", description="基站跟踪任务类型")
    eventList: DataT = Field(default=DTTRACE_TEMPLATE, type="jsonEditor",
                             description=f"按照标准格式填写的Events,如不涉及可不填.例:{DTTRACE_TEMPLATE}")
    parameters: DataT = Field(default=DTTRACE_PARAMETERS_TEMPLATE, type="jsonEditor",
                              description="按照标准格式填写任务参数,仅跟踪类型是DTTrace时生效,如果未输入'IP地址',将使用环境信息中第一个pdn cnip信息")
    startTime: datetime | None = Field(default=None, description="开始时间,不输入表示当前系统时间为开始时间")
    endTime: datetime | None = Field(default=None, description="结束时间,不输入表示当前系统时间延后8小时为结束时间")

    @validator("dataViewMode")
    def validate_dataViewMode(cls, dataViewMode):
        if dataViewMode is None:
            raise ValueError("dataViewMode必须选择,不可为空")
        match dataViewMode:
            case ["展示"]:
                return "0"
            case ["保存文件"]:
                return "1"
            case _:
                return "2"

    @classmethod
    def schema(cls):
        schema_map = [
            ({"taskType": ["SOP"]}, {"eventList": SOP_TEMPLATE}),
            ({"taskType": ["DTTrace"]}, {"eventList": DTTRACE_TEMPLATE, "parameters": DTTRACE_PARAMETERS_TEMPLATE}),
            ({"taskType": ["ASIC"]}, {"eventList": ASIC_TEMPLATE}),
            ({"taskType": ["ATUTrace"]}, {"eventList": ATUTRACE_TEMPLATE}),
            ({"taskType": ["Signal"]}, {"eventList": SIGNAL_TEMPLATE, "parameters": SIGNAL_PARAMETERS_TEMPLATE})]
        dynamic_paras = ["parameters"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)


CreateStationTask.schema()


class ModifyStationTask(BaseModel):
    taskType: Literal["SOP", "DTTrace", "ASIC", "ATUTrace", "Signal"] = Field(default="DTTrace", description="基站跟踪任务类型")
    eventList: DataT = Field(default=DTTRACE_TEMPLATE, type="jsonEditor",
                             description=f"按照标准格式填写的Events,如不涉及可不填.例:{DTTRACE_TEMPLATE}")
    parameters: DataT = Field(default=DTTRACE_PARAMETERS_TEMPLATE, type="jsonEditor",
                              description="按照标准格式填写任务参数,仅跟踪类型是DTTrace时生效,如果未输入'IP地址',将使用环境信息中第一个pdn cnip信息")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"taskType": ["SOP"]}, {"eventList": SOP_TEMPLATE}),
            ({"taskType": ["DTTrace"]}, {"eventList": DTTRACE_TEMPLATE, "parameters": DTTRACE_PARAMETERS_TEMPLATE}),
            ({"taskType": ["ASIC"]}, {"eventList": ASIC_TEMPLATE}),
            ({"taskType": ["ATUTrace"]}, {"eventList": ATUTRACE_TEMPLATE}),
            ({"taskType": ["Signal"]}, {"eventList": SIGNAL_TEMPLATE, "parameters": SIGNAL_PARAMETERS_TEMPLATE})]
        dynamic_paras = ["parameters"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)


ModifyStationTask.schema()

MDT_PARAMETERS_TEMPLATE = {
    "M1 M6上报间隔": "5120ms",
    "M1 定位方法": "None",
    "M1 触发机制": "周期",
    "M1 门限类型": "RSRP",
    "M1 RSRP/RSRQ/SINR 阈值": "127",
    "M1 上报次数": "Infinity",
    "M4采集周期": "5120ms",
    "M5采集周期": "5120ms",
    "M7采集周期(min)": "1",
    "本地蓝牙名": "null",
    "无线局域网 SSID": "null",
    "Logged采集持续时间": "10min",
    "Logged采集间隔": "5120ms",
    "Logged 触发机制": "outOfCoverage",
    "L1 门限类型": "RSRP",
    "L1 RSRP/RSRQ/SINR阈值": "51",
    "L1 事件测量判决迟滞": "3",
    "L1 事件发生到上报的时间差": "320ms",
    "区域范围类型": "无",
    "小区CGI": "null",
    "跟踪区编码": "null",
    "跟踪区标识": "null",
    "MDT PLMN列表": "null",
    "TCE IP 地址": "*******"
}
class DockTraceTask(BaseModel):
    imsiList: str = Field("123456789123456",description="15位10进制数，输入多个则用逗号分割，如123456789123456或123456789123456,987654321123456")
    serviceType: Literal["5gnr"] = Field("5gnr")
    cnEmsNames: str = Field(description="填写配置界面的核心网名称,多个则用逗号隔开")
    # cnemsInfos: DataT = Field(default=cnemsInfos, type="jsonEditor",
    #                           description="按照标准格式填写 任务参数,仅在跟踪类型选择了MDT时生效")
    eventList: DataT = Field(default=['UE_MDT_LOGGED'], type="jsonEditor",
                             description='按照标准格式填写的Events,如不涉及可不填.例：["RB_PHS_RTT_STAT","UE_MDT_LOGGED"]')
    signalType: list[SignalTypeEnum] = [SignalTypeEnum.uu, SignalTypeEnum.ng, SignalTypeEnum.xn, SignalTypeEnum.x2,
                                        SignalTypeEnum.s1, SignalTypeEnum.f1, SignalTypeEnum.e2ap, SignalTypeEnum.mecs, SignalTypeEnum.nrppa]

    MDTParameters: DataT = Field(default=MDT_PARAMETERS_TEMPLATE, type="jsonEditor",
                              description="跟踪MDT时此处需要修改，否则不需关注，此处各参数为网管默认值")
    SignalParameters: DataT = Field(default=Signal_Parameters_Template, type="jsonEditor",
                              description='跟踪Signal时此处需要修改，否则不需关注，此处各参数为网管默认值')
    startTime: datetime | None
    endTime: datetime | None

    @validator("imsiList")
    def validate_imsiList(cls, imsiList):
        if imsiList is None:
            return True
        d = list()
        for imsi in imsiList.split(','):
            imsi = imsi.strip()
            if not re.match(r"^[0-9]{15}$", imsi):
                raise ValueError(
                    f"Invalid traceId: {imsi},输入格式：15位十进制数，支持单个输入或多个以逗号分隔输入。示例：123456789123456 或 123456789123456,987654321123456")
            else:
                d.append(imsi)
        return d

    @validator("cnEmsNames")
    def validate_cnEmsNames(cls, cnEmsNames):
        if cnEmsNames is None:
            return True
        d = list()
        for cnEmsName in cnEmsNames.split(','):
            cnEmsName = cnEmsName.strip()
            d.append(cnEmsName)
        return d
