import traceback
import time
import pytz
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.sta.Schemas import TraceIDTraceTask, DockTraceTask
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.models.ume.sta.Sta import Sta
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.utils.TimeHandler import format_to_zero_time, format_startEndTime
from infrastructure.resource.service.Refs import Refs


router = APIRouter(route_class=bizRoute)


@router.post("/create_traceidtrace", summary="创建STA的TraceID跟踪任务",
             description=description("【功能】网管STA模块下，用户跟踪功能中，创建TraceID跟踪任务", "岳昊冉10263798"))
async def create_traceidtrace(request: Request, para: TraceIDTraceTask):
    mes = await MeFactory.create(request)
    if para.startTime and para.endTime:
        startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                 para.endTime.astimezone(pytz.utc))
    else:
        startTime, endTime = format_startEndTime()
    try:
        parameters = {"小区业务跟踪开关": para.cellTrafficTraceSwitchFilter, "跟踪深度": para.traceDepthFilter}
        taskInfo = {}
        paras = {"dataViewMode": "2", "serviceType": "5gnr", "startTime": startTime, "endTime": endTime,
                 "traceIdList": para.traceIdList, "menuId": "ranume-sta-traceId-trace", "parameters": parameters}
        eventList = para.signalType + para.eventList if para.signalType else para.signalType
        paras.update({"eventList": eventList, "taskName": "Hyper_Automation_TraceId_" + str(int(time.time()))})
        taskRlt, errMsg = await Sta(mes[0]).create_task(paras)
        if errMsg:
            return fail(data={"taskInfo": taskInfo}, msg=errMsg)
        else:
            taskRlt.update({"traceIds": para.traceIdList})
            return success(data={"taskInfo": {mes[0].meId: taskRlt}}, msg="Base station traceidtrace task is created successful.")
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/modify_traceidtrace", summary="修改STA的TraceID跟踪任务",
             description=description("【功能】网管STA模块下，修改基站跟踪TraceIDTRACE任务，修改前需挂起任务，修改后自动激活", "岳昊冉10263798"),
             openapi_extra={"refs": [{"actionName": "创建STA的TraceID跟踪任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def modify_traceidtrace(request: Request, para: TraceIDTraceTask):
    mes = await MeFactory.create(request)
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    try:
        taskInfo = {}
        if para.startTime and para.endTime:
            startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                     para.endTime.astimezone(pytz.utc))
        else:
            startTime, endTime = format_startEndTime()
        traceIdList = para.traceIdList if para.traceIdList else refTaskInfos.get("traceIdList")
        parameters = {"小区业务跟踪开关": para.cellTrafficTraceSwitchFilter, "跟踪深度": para.traceDepthFilter}
        for meId, refTaskInfoDic in refTaskInfos.items():
            me = await MeFactory.create_by_meid(request, meId)
            eventList = para.eventList if para.eventList else []
            eventList = eventList + para.signalType if para.signalType else eventList
            info = {"taskName": refTaskInfoDic.get("taskName"), "menuId": "ranume-sta-traceId-trace", "eventList": eventList,
                    "parameters": parameters, "startTime": startTime, "endTime": endTime, "traceIdList": traceIdList}
            paras = {"dataViewMode": "2", "serviceType": "5gnr", "subscriptionId": refTaskInfoDic.get("subscriptionId"),
                     "menuId": "ranume-sta-traceId-trace", "traceIdList": traceIdList, "taskName": refTaskInfoDic.get("taskName")}
            taskRlt, errMsg = await Sta(me).modify_task(paras, info)
            if errMsg:
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                taskRlt.update({"traceIds": para.traceIdList})
                taskInfo.update({me.meId: taskRlt})
        if taskInfo:
            return success(data={"taskInfo": taskInfo}, msg="Base station traceidtrace task is modified successful.")
        return fail(data={"taskInfo": taskInfo}, msg="Base station traceidtrace task is modified failed.")
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/create_docktrace", summary="创建STA的对接跟踪任务",
             description=description("【功能】网管STA模块下，用户跟踪功能中，创建对接跟踪任务", "00256242"))
async def create_docktrace(request: Request, para: DockTraceTask):
    mes = await MeFactory.create(request)
    if para.startTime and para.endTime:
        startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                 para.endTime.astimezone(pytz.utc))
    else:
        startTime, endTime = format_startEndTime()
    try:
        parameters = para.SignalParameters if para.signalType else {}
        parameters.update(para.MDTParameters) if para.eventList else parameters
        taskInfo = {}
        paras = {"dataViewMode": "2", "serviceType": "5gnr", "startTime": startTime, "endTime": endTime,
                 "usIds": para.imsiList, "menuId": "ranume-sta-docking-trace","cnEmsNames": para.cnEmsNames,
                 "parameters": parameters}
        eventList = para.signalType + para.eventList if para.eventList else para.signalType
        paras.update({"eventList": eventList, "taskName": "Hyper_Automation_DockTrace_" + str(int(time.time()))})
        taskRlt, errMsg = await Sta(mes[0]).create_task(paras)
        if errMsg:
            return fail(data={"taskInfo": taskInfo}, msg=errMsg)
        else:
            taskRlt.update({"usIds": para.imsiList, "cnEmsNames": para.cnEmsNames})
            return success(data={"taskInfo": {mes[0].meId: taskRlt}}, msg="对接跟踪任务创建成功!")
    except Exception as e:
        traceback.print_exc()
        Logger.error(f"创建STA的对接跟踪任务过程中程序运行错误，详情:", traceback.format_exc())
        return fail(f"创建STA的对接跟踪任务过程中程序运行错误，详情:{str(e)}")

@router.post("/modify_docktrace", summary="修改STA的对接跟踪任务",
             description=description("【功能】网管STA模块下，修改用户跟踪下的对接跟踪任务，修改前需挂起任务(使用action：暂停STA订阅任务)，修改后自动激活", "00256242"),
             openapi_extra={"refs": [{"actionName": "创建STA的对接跟踪任务", "path": "/v1/api/action/ume/sta", "refPattern": "1-*"}]})
async def modify_cocktrace(request: Request, para: DockTraceTask):
    refTaskInfos = (await Refs(request).get_output("taskInfo"))[0]
    try:
        taskInfo = {}
        if para.startTime and para.endTime:
            startTime, endTime = format_to_zero_time(para.startTime.astimezone(pytz.utc),
                                                     para.endTime.astimezone(pytz.utc))
        else:
            startTime, endTime = format_startEndTime()
        imsiList = para.imsiList if para.imsiList else refTaskInfos.get("usIds")
        cnEmsNames = para.cnEmsNames if para.cnEmsNames else refTaskInfos.get("cnEmsNames")
        parameters = para.SignalParameters if para.signalType else {}
        parameters.update(para.MDTParameters) if para.eventList else parameters
        for meId, refTaskInfoDic in refTaskInfos.items():
            me = await MeFactory.create_by_meid(request, meId)
            eventList = para.signalType + para.eventList if para.eventList else para.signalType
            paras = {"dataViewMode": "2", "serviceType": "5gnr","taskName": refTaskInfoDic.get("taskName"), "menuId": "ranume-sta-docking-trace",
                     "eventList": eventList, "parameters": parameters, "startTime": startTime, "endTime": endTime, "usIds": imsiList, "cnEmsNames": cnEmsNames}
            info = {"subscriptionId": refTaskInfoDic.get("subscriptionId"), "menuId": "ranume-sta-docking-trace", "taskName": refTaskInfoDic.get("taskName")}
            taskRlt, errMsg = await Sta(me).modify_task(info, paras)
            if errMsg:
                return fail(data={"taskInfo": taskInfo}, msg=errMsg)
            else:
                taskRlt.update({"usIds": imsiList, "cnEmsNames": cnEmsNames})
                taskInfo.update({me.meId: taskRlt})
        if taskInfo:
            return success(data={"taskInfo": taskInfo}, msg="STA的对接跟踪任务修改成功!")
        return fail(data={"taskInfo": taskInfo}, msg="STA的对接跟踪任务修改失败!")
    except Exception as e:
        traceback.print_exc()
        Logger.error(f"修改基站跟踪任务过程中程序运行错误，详情:", traceback.format_exc())
        return fail(f"修改基站跟踪任务过程中程序运行错误，详情:{str(e)}")
