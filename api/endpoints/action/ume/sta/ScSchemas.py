from datetime import datetime
from typing import TypeVar, Literal
from pydantic import BaseModel, Field

from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")


SignalSC_eventList_Template = ["BRS_SENSE", "SDP_BRS", "SENSE_INNER_SIGNAL_SPF"]
MTSSC_eventList_Template = ["TARGET_TRACK_POINT"]


class CreateScStaCellInfo(BaseModel):
    startTime: datetime | None = None
    endTime: datetime | None = None

    taskType: Literal["SignalSC", "MTSSC"] = Field("MTSSC", description='任务类型，默认 MTSSC')
    eventList: DataT = Field(default=MTSSC_eventList_Template, type="jsonEditor",
                            description="按照标准格式填写MTSSC events,例：['TARGET_TRACK_POINT']")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"taskType": ["SignalSC"]}, {"eventList": SignalSC_eventList_Template}),
            ({"taskType": ["MTSSC"]}, {"eventList": MTSSC_eventList_Template})
        ]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map)


CreateScStaCellInfo.schema()



