from datetime import datetime
from enum import Enum
from typing import TypeVar, Optional
from infrastructure.logger import Logger

from pydantic import BaseModel, Field, validator

DataT = TypeVar("DataT")


class serviceTypeEnum(str, Enum):
    c1 = "5gnr"

class taskTypeNameEnum(str, Enum):
    c1 = "FreScan"
    c2 = "MutiFreScan"

class eventNameEnum(str, Enum):
    c1 = "SCH_SLOT_NI"
    c2 = "SCH_MULTI_SYMBOL_NI"


FRESCAN_PARAMETERS_TEMPLATE = {
    "超级小区CPID": "无效（普通小区）",
    "AAU(RRU)LNA关闭时长": 'LNA保持打开',
    "待扫描符号ID": "UL DMRS 轮询扫描"
}

MUTIFRESCAN_PARAMETERS_TEMPLATE = {
    "Slot号": "0",
    "待扫描多符号ID": '全部符号',
    "NI门限": "100"
}

class CreateFreScanTask(BaseModel):
    serviceType: serviceTypeEnum
    taskTypeName: taskTypeNameEnum
    eventName: eventNameEnum
    parameters: DataT = Field(default=FRESCAN_PARAMETERS_TEMPLATE, type="jsonEditor",
                             description="按照标准格式填写 任务参数")
    startTime: Optional[datetime]
    endTime: Optional[datetime]

SIMULATIONLOAD_PARAMETERS_TEMPLATE = {
    "PDSCH加载开关": "true",
    "PDCCH加载开关": "true",
    "FDD子帧随机化比例": "100",
    "BF权值生效开关": "false",
    "BF循环周期": "1",
    "指定下行MCS": "27"
}

SIMULATIONLOAD_PARAMETERS_MAP = {
    "PDSCH加载开关": "pdschSimSwitch",
    "PDCCH加载开关": "pdcchSimSwitch",
    "FDD子帧随机化比例": "dlSFSimRateFdd",
    "BF权值生效开关": "bfLoadFlag",
    "BF循环周期": "bfPeriod",
    "指定下行MCS": "expectDlMcs"
}

class productNum(str, Enum):
    c1 = "NR"

class startSimulationLoadTask(BaseModel):
    simLoadHelp: DataT = Field(None, type="label", viewType="url", urlName="模拟加载操作指南",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/c88a78c4e0df457c8cbcb18546e28728/view")
    product: productNum
    # nrPhysicalCellDUId: str = Field(None, description="需要开启模拟加载小区的物理小区Id")
    parameters: DataT = Field(default=SIMULATIONLOAD_PARAMETERS_TEMPLATE, type="jsonEditor",
                             description="按照标准格式填写 任务参数")
    path: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                        allowedFileType=".DAT", limit=1, params=[])

    @validator("parameters")
    def validate_parameters(cls, parameters):
        l = {}
        for key, value in parameters.items():
            l.update({SIMULATIONLOAD_PARAMETERS_MAP.get(key, key): value})
        return l