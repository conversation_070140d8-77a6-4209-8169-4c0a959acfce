import re
import traceback
import pytz
import time
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.eim.Schemas import CreateFreScanTask, startSimulationLoadTask
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from domain.models.ume.eim.Eim import Eim
from infrastructure.utils.ResultFormatter import format_query_mo_result
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs

router = APIRouter(route_class=bizRoute)


@router.post("/create_frescan_task", summary="创建基带频谱分析任务",
             description=description("网管Eim模块下，创建基带频谱分析任务", "10263798"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "path": "/v1/api/action/ume/rancm/mo/get"}]})
async def create_frescan_task(request: Request,  para: CreateFreScanTask):
    try:
        mes = []
        newQueryRlts = []
        queryRlts = await Refs(request).get_output()

        Logger.debug(queryRlts)
        needKeys = ["ManagedElement", "SubNetwork", "ldn", "cellLocalId"]
        if queryRlts:
            for queryRlt in queryRlts:
                queryRltDict = format_query_mo_result(queryRlt)
                Logger.debug(queryRltDict)
                for meId in queryRltDict:
                    for cellInfo in queryRltDict[meId]:
                        if not all(key in cellInfo for key in needKeys):
                            return fail(msg="NRCellDU的 ManagedElement;SubNetwork;ldn;cellLocalId 必须包含在查询结果中")

                    me = await MeFactory().create_by_meid(request, meId)
                    newQueryRlts.append(queryRltDict)
                    mes.append(me)
        Logger.debug(para)
        rlt, rltInfo = await Eim().create_frescan_task(mes, newQueryRlts, para)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is created failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is created successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/modify_frescan_task", summary="修改基带频谱分析任务",
             description=description("网管Eim模块下，修改基带频谱分析任务", "10263798"),
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/eim"},
                 {"actionName": "查询MO节点属性", "path": "/v1/api/action/ume/rancm/mo/get"}]})
async def modify_frescan_task(request: Request,  para: CreateFreScanTask):
    try:
        mes = []
        newQueryRlts = []
        queryRlts = await Refs(request).get_output()
        Logger.debug(queryRlts)
        needKeys = ["ManagedElement", "SubNetwork", "ldn", "cellLocalId"]
        taskId = 0
        taskName = ''
        if queryRlts:
            for queryRlt in queryRlts:
                if 'taskInfo' in queryRlt:
                    taskId = queryRlt.get('taskInfo').get('subscriptionId')
                    taskName = queryRlt.get('taskInfo').get('subscriptionName')
                    Logger.debug(taskId)
                    Logger.debug(taskName)
                    if taskId != 0 and taskName != '':
                        continue
                    return fail(msg="获取已建任务subscriptionId：{0}, subscriptionName: {1}异常".format(str(taskId), taskName))
                queryRltDict = format_query_mo_result(queryRlt)
                for meId in queryRltDict:
                    Logger.debug(queryRltDict)
                    Logger.debug("meId === {0}".format(meId))
                    for cellInfo in queryRltDict[meId]:
                        if not all(key in cellInfo for key in needKeys):
                            return fail(
                                msg="NRCellDU的 ManagedElement, SubNetwork, ldn, cellLocalId 必须包含在查询结果中")
                    me = await MeFactory().create_by_meid(request, meId)
                    mes.append(me)
                    newQueryRlts.append(queryRltDict)
        Logger.debug(para)
        rlt, rltInfo = await Eim().modify_frescan_task(str(taskId), taskName, mes, newQueryRlts, para)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is modified failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is modified successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/cancel_frescan_task", summary="取消基带频谱分析任务",
             description="【功能】取消基带频谱分析任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/eim"}]})
async def cancel_frescan_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        Logger.debug(taskInfo)
        mes = await MeFactory.create(request)
        Logger.debug(mes)
        subscriptionId = taskInfo[0].get('subscriptionId')
        rlt, rltInfo = await Eim().cancel_frescan_task(str(subscriptionId), mes)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is stopped failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is stopped successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/delete_frescan_task", summary="删除基带频谱分析任务",
             description="【功能】删除基带频谱分析任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/eim"}]})
async def delete_frescan_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        Logger.debug(taskInfo)
        mes = await MeFactory.create(request)
        Logger.debug(mes)
        subscriptionId = taskInfo[0].get('subscriptionId')
        rlt, rltInfo = await Eim().delete_frescan_task(str(subscriptionId), mes)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is deleted failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is deleted successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/start_simulation_load_task", summary="启动模拟加载任务",
             description=description("网管Eim模块下，启动模拟加载任务。查询MO节点时需要查询的attrNames为ManagedElement,\
                                     SubNetwork, ldn, nrPhysicalCellDUId", "10263798"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "path": "/v1/api/action/ume/rancm/mo/get"}]})
async def start_simulation_load_task(request: Request,  para: startSimulationLoadTask):
    try:
        mes = []
        newQueryRlts = []
        queryRlts = await Refs(request).get_output()
        Logger.debug(para)
        Logger.debug(queryRlts)
        bfPath = ''
        if para.parameters.get("BF权值生效开关") == "true":
            if not para.path:
                return fail("当BF权值生效开关为true时, 需上传bf权值文件")
            bfPath = para.path[0]
            Logger.debug(bfPath)
        needKeys = ["ManagedElement", "SubNetwork", "ldn", "nrPhysicalCellDUId"]
        minVersionList = []
        if queryRlts:
            for queryRlt in queryRlts:
                queryRltDict = format_query_mo_result(queryRlt)
                Logger.debug(queryRltDict)
                for meId in queryRltDict:
                    for cellInfo in queryRltDict[meId]:
                        if not all(key in cellInfo for key in needKeys):
                            return fail(msg="NRPhysicalCellDU的 ManagedElement;SubNetwork;ldn;nrPhysicalCellDUId, 必须包含在查询结果中")
                    me = await MeFactory().create_by_meid(request, meId)
                    meInfo = await me.get_me_info()
                    Logger.debug(meInfo)
                    mimVersion = meInfo.get('mimVersion')
                    minVersionList.append(mimVersion)
                    newQueryRlts.append(queryRltDict)
                    mes.append(me)
        Logger.debug(minVersionList)
        Logger.debug(set(minVersionList))
        if len(set(minVersionList)) > 1:
            return False, "网管要求网元的版本需一致， 查询结果中的网元版本清单：{0}".format(minVersionList)
        Logger.debug(para)
        rlt, rltInfo = await Eim().start_simulationLoad_task(mes, newQueryRlts, para, bfPath)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='任务创建失败')
        return success(data={"taskInfo": rltInfo}, msg='任务创建成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/stop_simulation_load_task", summary="停止模拟加载任务",
             description=description("网管Eim模块下，停止模拟加载任务", "10263798"),
             openapi_extra={"refs": [{"actionName": "启动模拟加载任务", "path": "/v1/api/action/ume/eim"}]})
async def stop_simulation_load_task(request: Request):
    try:
        taskInfos = await Refs(request).get_output("taskInfo")
        Logger.debug(taskInfos)
        mes = []
        allSucRltList = []
        allFailRltList = []
        for taskInfo in taskInfos[0]:
            phyDuCellInfos = taskInfo.get('simLoadOperationResponseList')
            Logger.debug(phyDuCellInfos)
            for phyDuCellInfo in phyDuCellInfos:
                Logger.debug(phyDuCellInfo)
                meId = re.search("ManagedElement=(\d+),", phyDuCellInfo).group(1)
                me = await MeFactory().create_by_meid(request, meId)
                mes.append(me)
                rlt, rltInfo = await Eim().stop_simulationLoad_task(me, phyDuCellInfo)
                Logger.debug(rlt)
                Logger.debug(rltInfo)
                if not rlt:
                    allFailRltList.append(rltInfo)
                allSucRltList.append(rltInfo)
        if allFailRltList:
            return fail(data={"errInfo": allFailRltList}, msg='Task is stopped failed')
        return success(data={"taskInfo": allSucRltList}, msg='Task is stopped successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))