from typing import TypeVar, Literal

from pydantic import BaseModel, Field, validator

DataT = TypeVar('DataT')

EXPOT_TEMPLATE_PARA = {"scenes": [],
                       "radioTypes": ["5G"]}

SCENSE = ["1588","IPSec","GMIPsec","MAU","RRU_SHARE","DryContactCable","Cabinet","TMA","BFD","slice5QI","FMM","UDTTC",
          "RruExpansionBox","specialNetworkSharing","Sensing","Computing"]

RADIOTYPES =  ["5G", "LTE-TDD", "LTE-FDD", "NB-IoT", "GSM", "UMTS"]

SETUP_PARA = {"tarName": "",
              "patchPkg": "",
              "verAndData": True}

UPGRADE_PARA = {"patchPkg": "",
                "stepInfo": ["下载版本包", "预激活版本包", "激活版本包"]}


class VersionManagement(BaseModel):
    opType: Literal["基站升级", "基站开站", "回退"] = Field("基站升级", description="基站版本操作类型")
    tarPath: DataT = Field(..., type="func", source="tdl", returnType="string", viewType="input", params=[],
                           func="service.platform.version_get.VersionService.VersionService.get_version",
                           description="基站版本全路径")

    filePath: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                            allowedFileType=".xlsm", limit=1, params=[],
                            description="本地开站规划数据xlsm文件，该参数只有op类型是'基站开站'时才生效")

    @validator("filePath")
    def update_op_type(cls, v, values):
        if values['opType'] == "基站开站" and v:
            values['opType'] = "本地规划数据开站"
        return v


class ExportDataMakeTemplate(BaseModel):
    tarName: str = Field(..., description="升级目标版本包,必须以.tar结尾", regex=r".*\.tar")
    exportParas: DataT = Field(EXPOT_TEMPLATE_PARA, type="jsonEditor",
                               description=f'SPU导出模板制作数据时相关属性,scenes:特殊场景,默认为[],可选择:{SCENSE};'
                                           f'radioTypes:无线制式,默认["5G"]，可选择:{RADIOTYPES}')


class CreateDataMake(BaseModel):
    filePath: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                            allowedFileType=".xlsm", limit=1, params=[],
                            description="本地开站规划数据xlsm文件，当输入该参数时，表明以本地导入数据制作数据，"
                                        "如果action有依赖'SPU导出数据制作模板',将以导出的模板制作数据")


class ManageDataMake(BaseModel):
    func: Literal["删除任务", "导出报告", "查询详情"] = Field("删除任务", description="数据制作任务管理操作类型")


class CreateSetup(BaseModel):
    opType: Literal["带数据开站", "PNP开站"] = Field("带数据开站", description="开站任务类型")
    localDataSource: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile",
                                   fileMaxSize="100",
                                   allowedFileType=".zip;*.xml", limit=1, params=[],
                                   description="仅带数据开站任务，数据源是（按文件）时生效;当action依赖了数据制作任务时，该参数不生效;"
                                               "本地数据文件（仅支持xml、zip类型文件）")
    paras: DataT = Field(SETUP_PARA, type="jsonEditor",
                         description="创建开站任务时其他属性，tarName: 大包名称（仅数据源为按文件方式时生效）;"
                                     "patchPkg:补丁包名（根据需要填写）;verAndData:版本和数据替换标识（默认true，false表示'整站数据替换'）")


class ManageSetup(BaseModel):
    func: Literal["启动", "重试", "停止", "取消", "删除任务", "等待完成",
    "查询状态", "查询详情", "导出报告"] = Field("删除任务", description="开站任务管理操作类型")


class CreateUpgrade(BaseModel):
    opType: Literal["全自动升级", "确认式升级", "版本预下载", "热补丁加载", "版本回退", "确认式升级(种子基站)"] = \
        Field("全自动升级", description="升级任务类型")
    tarName: str = Field(..., description="升级目标版本包,必须以.tar结尾", regex=r".*\.tar")
    paras: DataT = Field(UPGRADE_PARA, type="jsonEditor",
                         description="创建升级任务时其他属性，patchPkg:补丁包名（根据需要填写）;"
                                     "stepInfo:确认式升级步骤（用户需自行确保步骤合理性）")


class ManageUpgrade(BaseModel):
    func: Literal["启动", "继续", "重试", "停止", "取消", "回退", "卸载", "解锁", "加锁", "删除任务", "等待完成",
    "查询状态", "查询详情"] = Field("启动", description="升级任务管理操作类型")


class ManageMeTask(BaseModel):
    func: Literal["开站前处理任务", "升级前处理任务"] = Field("开站前处理任务", description="网元任务管理操作类型")


class getPKGInfo(BaseModel):
    tarName: str = Field(..., description="版本tar包名,必须以.tar结尾,示例：UNI_V5.75.10.10F20R25.tar", regex=r".*\.tar")


class ParaBeforeUpgrade(BaseModel):
    paraFilePath: DataT = Field(..., type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                                allowedFileType="*.xlsx;*.xlsm", limit=1, params=[],
                                description="开发提供的用于校验升级参数规则的文档,文档格式参考：https://zxmte.zte.com.cn:7776/sftpuser/upgrade_para/excel/demo/")
