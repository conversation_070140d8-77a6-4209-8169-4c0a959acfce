import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.spu.Schemas import VersionManagement, ExportDataMakeTemplate, CreateDataMake, \
    ManageDataMake, CreateSetup, ManageSetup, CreateUpgrade, ManageUpgrade, ManageMeTask, getPKGInfo, ParaBeforeUpgrade
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.models.ume.spu.QueryVersion import QueryVersion
from domain.models.ume.spu.SpuPublic import Spu
from domain.models.ume.spu.UpgradePara.UpgradePara import UpgradePara
from domain.platform.ActionInfo import description
from domain.platform.artifact.Artifact import submit_file_to_sftp, download_file_form_url
from infrastructure.logger import Logger
from infrastructure.resource.service.DeviceManager import DeviceManager
from infrastructure.resource.service.Refs import Refs
from service.action.ume.spu.DataPlanerService import DataPlanerService
from service.action.ume.spu.ErmsSpuService import ErmsSpuService
from service.action.ume.spu.SetUpJobService import SetUpJobService
from service.action.ume.spu.UpgradeService import UpgradeService

router = APIRouter(route_class=bizRoute)


@router.post("/management", summary="基站版本管理",
             description=description(
                 "通过ERMS，实现网管SPU模块下，升级、回退，开站、指定版本开站功能（注意：该action执行时间较久，请合理安排）",
                 "侯小飞10270755"))
async def management_version(request: Request, para: VersionManagement):
    mes = await MeFactory.create(request)
    if not mes:
        return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    if not para.tarPath.lower().endswith('.tar'):
        return fail(f"版本路径必须是全路径（以.tar结尾）")
    task_id = request.state.resource.get("taskId")
    task_name = request.state.resource.get("taskName")
    env_id = request.state.resource.get("envId")
    user = request.state.resource.get("user")
    meIds = [me.meId for me in mes]
    try:
        ret, msg = await ErmsSpuService.management_version(task_id, task_name, env_id, user,
                                                           para.opType, para.tarPath, para.filePath, meIds)
        if not ret:
            return fail(f"环境：{env_id} 网元{meIds} {para.opType}失败：{ErmsSpuService.analysis_result(msg)}")
    except BaseException as err:
        Logger.error(f"环境：{env_id} 网元{meIds} {para.opType}过程中程序运行错误，详情:", traceback.format_exc())
        return fail(msg=f"环境：{env_id} 网元{meIds} {para.opType}过程中程序运行错误，可查看log分析原因: {err}")
    return success(msg=f"环境：{env_id} 网元{meIds} {para.opType}成功")


@router.post("/export_datamake_template", summary="SPU导出数据制作模板",
             description=description("在网管SPU模块，导出数据制作模板", "侯小飞10270755"))
async def export_datamake_template(request: Request, para: ExportDataMakeTemplate):
    mes = await MeFactory.create(request)
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    try:
        file = await DataPlanerService.export_datamake_template(mes, para.tarName, para.exportParas)
    except BaseException as err:
        Logger.error(f"SPU导出数据制作模板过程中程序运行错误，详情:", traceback.format_exc())
        return fail(msg=f"SPU导出数据制作模板过程中程序运行错误，详情: {err}")
    return success(msg=f"SPU导出数据制作模板成功", data=file)


@router.post("/create_datamake_task", summary="SPU数据制作",
             description=description("在网管SPU模块，创建数据制作任务", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "SPU导出数据制作模板", "refPattern": "0-1"}]})
async def create_datamake_task(request: Request, para: CreateDataMake):
    mes = await MeFactory.create(request)
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    file = await Refs(request).get_output() or para.filePath
    try:
        jobName, msg = await DataPlanerService.create_datamake_task(mes, file)
        if msg: return fail(msg=f"SPU数据制作失败，原因:{msg}", data=jobName)
    except BaseException as err:
        Logger.error(f"SPU数据制作过程中程序运行错误，详情:", traceback.format_exc())
        return fail(msg=f"SPU数据制作过程中程序运行错误，详情: {err}")
    return success(msg=f"SPU数据制作成功", data=jobName)


@router.post("/manage_datamake_task", summary="SPU数据制作任务管理",
             description=description("在网管SPU模块，管理创建的数据制作任务", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "SPU数据制作", "path": "/v1/api/action/ume/spu/create_datamake_task",
                           "refPattern": "1-*"}]})
async def manage_datamake(request: Request, para: ManageDataMake):
    mes = await MeFactory.create(request)
    jobNames = await Refs(request).get_output()
    errFlag, result = False, {}
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    for jobName in jobNames:
        try:
            jobId = await Spu(mes[0]).get_job_id_by_job_name(jobName, "DataMake")
            ret, msg = await DataPlanerService.management_datamake_task(mes[0], para.func, jobId)
            if not ret:
                result[jobName] = f"{para.func}失败:{msg}"
                errFlag = True
            else:
                result[jobName] = f"{para.func}成功:{msg}" if msg else f"{para.func}成功"
        except BaseException as err:
            errFlag = True
            Logger.error(f"'{para.func}'SPU数据制作任务过程中程序运行错误，详情:", traceback.format_exc())
            result[jobName] = f"'{para.func}'SPU数据制作任务过程中程序运行错误，详情: {err}"
    if errFlag:
        return fail(msg=f"SPU数据制作任务'{para.func}'失败", data=result)
    return success(msg=f"SPU数据制作任务'{para.func}'成功", data=result)


@router.post("/create_setup_task", summary="SPU创建开站任务",
             description=description("在网管SPU模块，创建开站任务", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "SPU数据制作", "path": "/v1/api/action/ume/spu/create_datamake_task",
                           "refPattern": "0-1"}]})
async def create_setup_task(request: Request, para: CreateSetup):
    mes = await MeFactory.create(request)
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    datamakeJob = (await Refs(request).get_output())
    try:
        if datamakeJob:
            jobIdOrPath = await Spu(mes[0]).get_job_id_by_job_name(datamakeJob[0], "DataMake")
        else:
            jobIdOrPath = para.localDataSource
        jobName, msg = await SetUpJobService.create_setup_job(mes[0], jobIdOrPath, para.opType, para.paras)
        if msg:
            return fail(msg=f"SPU创建开站任务失败，原因:{msg}", data=jobName)
    except BaseException as err:
        Logger.error(f"SPU创建开站任务过程中程序运行错误，详情:", traceback.format_exc())
        return fail(msg=f"SPU创建开站任务过程中程序运行错误，详情: {err}")
    return success(msg=f"SPU创建开站任务成功", data=jobName)


@router.post("/manage_setup_task", summary="SPU开站任务管理",
             description=description("在网管SPU模块，管理开站任务", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "SPU创建开站任务", "refPattern": "1-*"}]})
async def manage_setup(request: Request, para: ManageSetup):
    mes = await MeFactory.create(request)
    jobNames = await Refs(request).get_output()
    errFlag, result = False, {}
    if not mes:
        return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    for jobName in jobNames:
        try:
            jobId = await Spu(mes[0]).get_job_id_by_job_name(jobName, "Setup")
            ret, msg = await SetUpJobService.management_setup_task(jobId, mes, para.func)
            if not ret:
                result[jobName] = f"{para.func}失败:{msg}"
                errFlag = True
            else:
                result[jobName] = f"{para.func}成功:{msg}" if msg else f"{para.func}成功"
        except BaseException as err:
            errFlag = True
            Logger.error(f"'{para.func}'SPU开站任务过程中程序运行错误，详情:", traceback.format_exc())
            result[jobName] = f"'{para.func}'SPU开站任务过程中程序运行错误，详情:{err}"
    if errFlag:
        return fail(msg=f"SPU开站任务'{para.func}'失败", data=result)
    return success(msg=f"SPU开站任务'{para.func}'成功", data=result)


@router.post("/create_upgrade_task", summary="SPU创建升级任务",
             description=description("在网管SPU模块，创建升级任务", "侯小飞10270755"))
async def create_upgrade_task(request: Request, para: CreateUpgrade):
    mes = await MeFactory.create(request)
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    try:
        attr = para.paras
        if para.opType == "确认式升级(种子基站)":
            attr.update({"isSeed": True})
        jobName, msg = await UpgradeService.create_upgrade_job(mes, para.tarName, para.opType, attr)
        if msg:
            return fail(msg=f"SPU创建升级任务失败，原因:{msg}", data=jobName)
    except BaseException as err:
        Logger.error(f"SPU创建升级任务过程中程序运行错误，详情:", traceback.format_exc())
        return fail(msg=f"SPU创建升级任务过程中程序运行错误，详情: {err}")
    return success(msg=f"SPU创建升级任务成功", data=jobName)


@router.post("/manage_upgrade_task", summary="SPU升级任务管理",
             description=description("在网管SPU模块，管理升级任务", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "SPU创建升级任务", "refPattern": "1-*"}]})
async def manage_upgrade(request: Request, para: ManageUpgrade):
    mes = await MeFactory.create(request)
    jobNames = await Refs(request).get_output()
    errFlag, result = False, {}
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    for jobName in jobNames:
        try:
            jobId = await Spu(mes[0]).get_job_id_by_job_name(jobName, "Upgrade")
            ret, msg = await UpgradeService.management_upgrade_task(jobId, mes, para.func)
            if not ret:
                result[jobName] = f"{para.func}失败:{msg}"
                errFlag = True
            else:
                result[jobName] = f"{para.func}成功:{msg}" if msg else f"{para.func}成功"
        except BaseException as err:
            errFlag = True
            Logger.error(f"'{para.func}'SPU升级任务过程中程序运行错误，详情:", traceback.format_exc())
            result[jobName] = f"'{para.func}'SPU升级任务过程中程序运行错误，详情: {err}"
    if errFlag:
        return fail(msg=f"SPU升级任务'{para.func}'失败", data=result)
    return success(msg=f"SPU升级任务'{para.func}'成功", data=result)


@router.post("/manage_ne_task", summary="SPU网元任务管理",
             description=description("在网管SPU模块，管理网元即任务，！！！谨慎使用！！！，"
                                     "避免和已有任务冲突，开站/升级前将网元未开始的任务删除、将正在运行的任务取消、将失败的任务取消",
                                     "侯小飞10270755"))
async def manage_upgrade(request: Request, para: ManageMeTask):
    mes = await MeFactory.create(request)
    ret = True
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    try:
        if para.func == '开站前处理任务':
            ret = await SetUpJobService.cancle_curr_job_before_create(mes)
        if para.func == '升级前处理任务':
            ret = await UpgradeService.cancle_curr_job_before_create(mes)
        if not ret:
            return fail(msg=f"{para.func}失败")
        else:
            return success(f"{para.func}成功")
    except BaseException as err:
        Logger.error(f"'{para.func}'过程中程序运行错误，详情:", traceback.format_exc())
        return fail(msg=f"'{para.func}'过程中程序运行错误,详情: {err}")


@router.post("/get_pkg_info", summary="查询版本包详细信息",
             description=description("spu模块里，版本仓库查询某个版本包的小版本详细信息,返回一个列表数据",
                                     "10255283"))
async def get_pkg_info(request: Request, para: getPKGInfo):
    mes = await MeFactory.create(request)
    flg, res = await QueryVersion(mes[0]).get_pkg_detail_info(para.tarName)
    if flg:
        return success(msg=f"查询spu版本包详细信息成功", data=res)
    if res.get("code") == 50000:
        return fail(msg=f"查询spu版本包详细信息失败,请检查网管是否存在该版本包", data=res)
    return fail(msg=f"查询spu版本包详细信息失败", data=res)


@router.post("/get_ne_board_version", summary="查询网元单板版本",
             description=description("spu模块里，版本查询任务中查询单板信息,返回一个列表数据",
                                     "10255283"))
async def get_ne_board(request: Request):
    mes = await MeFactory.create(request)
    err = {}
    result = {}
    for me in mes:
        flg, res = await QueryVersion(me).get_ne_ver_detail()
        if flg:
            result.update({me.meId: res})
        else:
            err.update({me.meId: res})
    if err:
        return fail(msg=f"查询spu网元单板版本失败", data=f"原因是{err}")
    return success(msg=f"查询spu网元单板版本成功", data=result)


@router.post("/get_Version_Repository", summary="查询版本仓库版本信息",
             description=description("spu模块里，版本仓库",
                                     "10255283"))
async def get_Version_Repository(request: Request):
    mes = await MeFactory.create(request)
    err = {}
    result = {}
    for me in mes:
        flg, res = await QueryVersion(me).query_package()
        if flg:
            result.update({me.meId: res})
        else:
            err.update({me.meId: res})
    if err:
        return fail(msg=f"查询版本仓库版本失败", data=f"原因是{err}")
    return success(msg=f"查询版本仓库版本成功", data=result)

@router.post("/query_me_version", summary="查询网元版本信息",
             description=description("spu模块里，查询网元版本信息，返回对应版本信息列表", "10270755"))
async def query_me_version(request: Request):
    mes = await MeFactory.create(request)
    result, flag = {}, True
    for me in mes:
        try:
            res = await QueryVersion(me).query_me_version()
            result.update({me.meId: res})
        except Exception as err:
            flag = False
            result.update({me.meId: err})
    if flag:
        return success(msg=f"查询网元版本信息成功", data=result)
    return fail(msg=f"查询网元版本信息失败", data=result)


@router.post("/query_upgrade_para", summary="查询升级规则参数",
             description=description(
                 "升级参数校验功能，升级前根据升级规则文档采集环境的参数填表，文档格式参考：https://zxmte.zte.com.cn:7776/sftpuser/upgrade_para/excel/demo/",
                 "10234064"))
async def query_para_before_upgrade(request: Request, data: ParaBeforeUpgrade):
    try:
        mes = await MeFactory.create(request)
        if len(mes) != 1:
            return fail(msg=f"此Action当前限制支持选择一个网元执行，当前网元数{len(mes)}不满足要求")
        rlt = {}
        filePath = download_file_form_url(data.paraFilePath[0])
        for me in mes:
            up = UpgradePara(me, filePath)
            await up.query()
            url = submit_file_to_sftp(up.dstFilePath, file_type="Excel")
            rlt.update({me.meId: url})
            if up.err:
                return fail(up.err, data=rlt)
        return success(msg=f"查询版本仓库版本成功", data=rlt)
    except Exception as e:
        return fail(f"执行过程中报错：{e}")


@router.post("/verify_upgrade_para", summary="校验升级规则参数",
             description=description("升级后根据升级规则文档采集环境的参数以及升级后采集到的参数填表", "10234064"),
             openapi_extra={"refs": [{"actionName": "查询升级规则参数", "refPattern": "1"}]})
async def verify_para_after_upgrade(request: Request):
    try:
        files = await Refs(request).get_output()
        Logger.info(f"升级前的参数文件：{files}")
        rlt = {}
        for meId, url in files[0].items():
            filePath = download_file_form_url(url)
            me = await MeFactory.create_by_meid(request, meId)
            if not me:
                raise Exception(f"找不到meId{meId}的资源！")
            up = UpgradePara(me, filePath)
            await up.verify()
            url = submit_file_to_sftp(up.dstFilePath, file_type="Excel")
            rlt.update({meId: url})
            if up.err:
                return fail(up.err, data=rlt)
        return success(msg=f"查询版本仓库版本成功", data=rlt)
    except Exception as e:
        return fail(f"执行过程中报错：{e}")
