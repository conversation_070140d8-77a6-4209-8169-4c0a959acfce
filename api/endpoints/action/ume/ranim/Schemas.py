from datetime import datetime
from enum import Enum
from typing import TypeVar, Literal

from pydantic import BaseModel, Field, validator

DataT = TypeVar('DataT')


class ImEnum(str, Enum):
    all = "all"
    packfunction = "packfunction"
    productcode = "productcode"
    unitposition = "unitposition"
    cpuversion = "cpuversion"
    inventoryunitid = "inventoryunitid"
    inventoryunittype = "inventoryunittype"
    dateofmanufacture = "dateofmanufacture"
    vendorunitfamilytype = "vendorunitfamilytype"
    neuuid = "neuuid"
    netype = "netype"
    dateoflastrepair = "dateoflastrepair"
    moid = "moid"
    producttype = "producttype"
    networkaccesstime = "networkaccesstime"
    inventorystatuslastmodifytime = "inventorystatuslastmodifytime"
    neid = "neid"
    userlabel = "userlabel"
    hwcapability = "hwcapability"
    serialnumber = "serialnumber"
    ip = "ip"
    bootversion = "bootversion"
    vendorunittypename = "vendorunittypename"
    inventorystatus = "inventorystatus"
    subnetworkid = "subnetworkid"
    versionnumber = "versionnumber"
    telecomoperators = "telecomoperators"
    subcardinfo = "subcardinfo"
    packsilkscreen = "packsilkscreen"
    updatetime = "updatetime"
    vendorname = "vendorname"
    manualdataentry = "manualdataentry"
    userlabel4naf = "userlabel4naf"
    manufacturerdata = "manufacturerdata"


class optEnum(str, Enum):
    all = "all"
    copperlinklength = "copperlinklength"
    datecode = "datecode"
    dateoflastservice = "dateoflastservice"
    inventorystatus = "inventorystatus"
    inventorystatuslastmodifytime = "inventorystatuslastmodifytime"
    inventoryunitid = "inventoryunitid"
    ip = "ip"
    moid = "moid"
    neid = "neid"
    networkaccesstime = "networkaccesstime"
    neuuid = "neuuid"
    netype = "netype"
    normalbitrate = "normalbitrate"
    opt50umom2linklength = "opt50umom2linklength"
    opt50umom3linklength = "opt50umom3linklength"
    opt625umom1linklength = "opt625umom1linklength"
    producttype = "producttype"
    rxpowerhighalarmthold = "rxpowerhighalarmthold"
    rxpowerhighwarningthold = "rxpowerhighwarningthold"
    rxpowerlowalarmthold = "rxpowerlowalarmthold"
    rxpowerlowwarningthold = "rxpowerlowwarningthold"
    sfpidentifier = "sfpidentifier"
    single100mlinklength = "single100mlinklength"
    singlekmlinklength = "singlekmlinklength"
    subnetworkid = "subnetworkid"
    templowalarmthold = "templowalarmthold"
    templowwarningthold = "templowwarningthold"
    temphighalarmthold = "temphighalarmthold"
    temphighwarningthold = "temphighwarningthold"
    txbiashighalarmthold = "txbiashighalarmthold"
    txbiashighwarningthold = "txbiashighwarningthold"
    txbiaslowalarmthold = "txbiaslowalarmthold"
    txbiaslowwarningthold = "txbiaslowwarningthold"
    txpowerhighalarmthold = "txpowerhighalarmthold"
    txpowerhighwarningthold = "txpowerhighwarningthold"
    txpowerlowalarmthold = "txpowerlowalarmthold"
    txpowerlowwarningthold = "txpowerlowwarningthold"
    unitposition = "unitposition"
    updatetime = "updatetime"
    userlabel = "userlabel"
    vendorname = "vendorname"
    vendorpn = "vendorpn"
    vendorsn = "vendorsn"
    wavelength = "wavelength"
    zteauthenticationstatus = "zteauthenticationstatus"


class hwCapture(BaseModel):
    hwFilter: list[ImEnum] = Field([ImEnum.inventoryunittype, ImEnum.packfunction, ImEnum.vendorunittypename,
                                    ImEnum.serialnumber, ImEnum.unitposition],
                                   description='需要抓取的信息类型，填写"all"表示所有指标都抓取')


class optCapture(BaseModel):
    optFilter: list[ImEnum] = Field(
        [optEnum.sfpidentifier, optEnum.unitposition, optEnum.wavelength, optEnum.vendorsn, optEnum.moid,
         optEnum.temphighalarmthold, optEnum.templowalarmthold],
        description='需要抓取的信息类型，填写"all"表示所有指标都抓取')
