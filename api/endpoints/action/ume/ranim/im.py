import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.lcs.Schemas import NeLcsMange, LcsCenterMange, LcsCenterLoadFile
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from domain.models.ume.ranim.RanIm import Im
from api.endpoints.action.ume.ranim.Schemas import hwCapture, optCapture

router = APIRouter(route_class=bizRoute)


@router.post("/queryHwInventory", summary="硬件资产查询",
             description=description('在网管无线资产管理，查询硬件资产', '杨先恒10255283'))
async def query_hw_inventory(request: Request, paras: hwCapture):
    mes = await MeFactory.create(request)
    result = {}
    for me in mes:
        flag, info = await Im(me).query_gnb_board_inventory(paras)
        if flag:
            result.update({me.meId: info})
    return success(data=result, msg=f"查询硬件资产成功")


@router.post("/queryOptInventory", summary="光模块资产查询",
             description=description('在网管无线资产管理，查询光模块资产', '杨先恒10255283'))
async def query_opt_inventory(request: Request, paras: optCapture):
    mes = await MeFactory.create(request)
    result = {}
    for me in mes:
        flag, info = await Im(me).query_gnb_opt_inventory(paras)
        if flag:
            result.update({me.meId: info})
    return success(data=result, msg=f"查询光模块资产成功")
