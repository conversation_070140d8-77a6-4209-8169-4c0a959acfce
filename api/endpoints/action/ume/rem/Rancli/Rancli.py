# -*- encoding: utf-8 -*-
'''
@File    :   Rancli.py
@Time    :   2023/08/18 15:57:44
<AUTHOR>   何为10156505
@Version :   1.0
@Contact :   <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import traceback

from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.platform.artifact import Artifact
from service.action.ume.rem.RancliService import RancliService
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from api.endpoints.action.ume.rem.Rancli.schemas import ImportDvTable, ExportDvTable, CertUploadPara
from service.action.ume.ose.OseAppService import OseAppService

router = APIRouter(route_class=bizRoute)


@router.post("/exportDvTable", summary="导出DV文件", description=description("导出DV文件", 10263798))
async def export_dv_table_in_rancli(request: Request, radioMode: ExportDvTable):
    mes = await MeFactory.create(request)
    flag, rets = True, {}
    for me in mes:
        try:
            file, url = await OseAppService.get_nes_dv_from_ose(me, None, radioMode.radioMode)
            rets.update({me.meId: url, 'radioMode': radioMode.radioMode})
        except BaseException as err:
            flag = False
            Logger.error(f"{me.meId} 导出DV文件异常,详情:", traceback.format_exc())
            rets.update({me.meId: f"导出DV异常,原因:{err}"})
    if flag:
        return success('DV Table export success', data={'sftpUrls': rets})
    return fail(f'DV Table export fail', data={'sftpUrls': rets})


@router.post("/importDvTableForRecovery", summary="导入DV文件_恢复环境", description=description("导入DV文件, 该action依赖导出dv配置文件操作", 10263798),
             openapi_extra={"refs": [{"actionName": "导出DV文件"}]})
async def import_dv_table_in_rancli_for_recovery(request: Request):
    mes = await MeFactory.create(request)
    sftpUrls = await Refs(request).get_output("sftpUrls")
    rets = {}
    Logger.info("sftpUrls={0}".format(sftpUrls))
    for sftpUrl in sftpUrls:
        for me in mes:
            if not sftpUrl.get(me.meId):
                continue
            localDvFilePath = Artifact.download_file_form_url(sftpUrl.get(me.meId))
            ret = await OseAppService.upload_nes_dv_from_ose(me, localDvFilePath, sftpUrl.get('radioMode'))
            if not ret:
                return fail(f'DV Table import fail {repr(me.meId)}')
            rets.update({me.meId: ret})
    return success('DV Table import success', data=rets)


@router.post("/importDvTable", summary="导入DV文件", description=description("导入DV文件", 10263798))
async def import_dv_table_in_rancli(request: Request, importDvTable: ImportDvTable):
    Logger.info(f"importDvTable={repr(importDvTable)}")
    mes = await MeFactory.create(request)
    rets = {}
    for me in mes:
        if me.meId == importDvTable.meId:
            localDvFilePath = Artifact.download_file_form_url(importDvTable.path[0])
            ret = await OseAppService.upload_nes_dv_from_ose(me, localDvFilePath, importDvTable.radioMode)
            if not ret:
                return fail(f'DV Table import fail {repr(me.meId)}')
            rets.update({me.meId: ret})
        Logger.info(f'The Env do not contain me: {importDvTable.meId}')
    return success('DV Table import success', data=rets)


@router.post("/upload_ca_safe_file", summary="RANCLI证书加载",
             description=description("通过RANCLI导入证书,加载证书前确认对应moId已存在", "10270755"))
async def upload_ca_safe_file(request: Request, para: CertUploadPara):
    mes = await MeFactory.create_by_nr(request)
    rets = {}
    for me in mes:
        rancli = await RancliService.create_rancli(me)
        try:
            ret, msg = await RancliService.upload_ca_safe_file(rancli, para)
            if not ret:
                return fail(f"{me.meId} '{para.certType}'加载失败,原因:{msg}")
            rets.update({me.meId: ret})
        except Exception as err:
            Logger.error(f"{me.meId} '{para.certType}'加载异常,原因:{err}")
            rancli.logout()
    return success(f"'{para.certType}'加载成功", data=rets)