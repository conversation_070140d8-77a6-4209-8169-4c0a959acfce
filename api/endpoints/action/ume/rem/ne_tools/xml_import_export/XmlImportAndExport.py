#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File    :   ConfigImportAndExport.py
@Time    :   2023/08/18 17:01:29
<AUTHOR>   10255257 
@Version :   1.0
@Desc    :   None
'''

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.rem.ne_tools.xml_import_export.Schemas import ImportXmlUserUpload
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.models.ume.rem.Rem import Rem
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/exportXml", summary="导出网元xml配置文件",
             description=description('通过网元工具导出网元的xml配置文件', '刘伟10255257'))
async def export_xml(request: Request):
    mes = await MeFactory.create(request)
    rets = {}
    for me in mes:
        ret = await Rem(me).export_xml()
        if ret:
            rets.update({me.meId: ret})
        else:
            return fail(f'meId: {me.meId} xml config file export fail')
    return success('xml config export success', data={'sftpUrls': rets})


@router.post("/importXmlForRecovery", summary="导入网元xml配置文件_恢复环境",
             description=description("通过网元工具导入网元的xml配置文件，该action依赖导出xml配置文件操作",
                                     "刘伟10255257"),
             openapi_extra={"refs": [{"actionName": "导出网元xml配置文件"}]})
async def import_xml_for_recovery(request: Request):
    mes = await MeFactory.create(request)
    sftpUrls = await Refs(request).get_output("sftpUrls")
    for me in mes:
        sftpUrl = sftpUrls[0].get(me.meId)
        ret = await Rem(me).import_xml(sftpUrl)
        if ret:
            Logger.info(f'meId:{me.meId} xml config file import success')
        else:
            return fail(f'meId:{me.meId} xml config file import fail')
    return success('all xmls import success')


@router.post("/importXmlUserUpload", summary="导入网元xml配置文件_用户上传",
             description=description('通过网元工具导入网元的xml配置文件，该xml由用户上传', '刘伟10255257'))
async def import_xml_user_upload(request: Request, importData: ImportXmlUserUpload):
    mes = await MeFactory.create(request)
    for me in mes:
        if me.meId == importData.meId:
            ret = await Rem(me).import_xml(importData.path[0])
            if ret:
                return success(f'meId:{me.meId} xml file import success')
            return fail(f'meId:{me.meId} xml config file import fail')
    Logger.info(f'The Env do not contain me: {importData.meId}')
