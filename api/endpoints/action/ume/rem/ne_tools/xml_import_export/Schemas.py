from typing import TypeV<PERSON>

from pydantic import BaseModel, Field

from infrastructure.resource.model.GNB import GNB_MEID, GNB_SUBNETWORK
from infrastructure.resource.model.UME import UME_IP, UME_PASSWORD, UME_PORT, UME_USERNAME

DataT = TypeVar('DataT')


class ExportXmlConfig(BaseModel):
    ip: UME_IP = "*************"
    username: UME_USERNAME = "admin"
    password: UME_PASSWORD = "Zenap_123!@#!"
    port: UME_PORT = "28001"
    meId: GNB_MEID = "8016"
    subNetwork: GNB_SUBNETWORK = "340999"


class ImportXmlUserUpload(BaseModel):
    meId: str
    path: DataT = Field(..., type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="300",
                        allowedFileType=".xml", limit=1, params=[])
