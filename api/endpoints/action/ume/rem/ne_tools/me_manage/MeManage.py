#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File    :   MeManage.py
@Time    :   2025/06/27 17:01:29
<AUTHOR>   10270755
@Version :   1.0
@Desc    :   None
'''

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.rem.ne_tools.me_manage.Schemas import ModifyMeInfo
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from service.action.ume.rem.RemService import RemService
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/modify_me_info", summary="编辑网元参数",
             description=description('在网管REM模块里,编辑网元参数', '10270755'))
async def modify_me_info(request: Request, para:ModifyMeInfo):
    mes = await MeFactory.create(request)
    rets, flag = {}, True
    for me in mes:
        try:
            ret = await RemService(me).modify_me_info(para.attrJson)
            rets.update({me.meId: ret})
        except Exception as err:
            flag = False
            rets.update({me.meId: f"{err}"})
    if flag:
        return success('编辑网元参数成功', data=rets)
    return fail('编辑网元参数失败', data=rets)

@router.post("/query_me_info", summary="查询网元信息",
             description=description('在网管REM模块里,查询网元信息', '10270755'))
async def query_me_info(request: Request):
    mes = await MeFactory.create(request)
    rets, flag = {}, True
    for me in mes:
        try:
            ret = await RemService(me).get_me_info()
            rets.update({me.meId: ret})
        except Exception as err:
            flag = False
            rets.update({me.meId: f"{err}"})
    if flag:
        return success('查询网元信息成功', data=rets)
    return fail('查询网元信息失败', data=rets)