#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:41
# <AUTHOR> 10263601
import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.pm.Schemas import FileListWithTimeRange, CreateParas, QueryParams, traceTypeEnum, \
    ExportKpiDataToSftp, ExportHisKpiDataToSftp, CreateMeasureParas, IaskHisKpi, TyCalculateKpi
from api.route.BizRoute import bizRoute
from domain.factories.CellFactory import CellFactory
from domain.factories.MeFactory import MeFactory
from domain.models.ume.pm.pm_task.MeasureTask import MeasureTask
from domain.models.ume.pm.pm_task.PerformanceMonitor import PerformanceMonitor
from domain.platform.ActionInfo import description
from domain.platform.artifact import Artifact
from infrastructure.logger import Logger
from infrastructure.resource.domain.db.ActionFilter import ActionFilter
from infrastructure.resource.service.Refs import Refs
from infrastructure.utils.Ids import IdsApi
from service.action.iask.IaskService import IaskService
from service.action.ume.pm.AutoKpiTask import <PERSON><PERSON><PERSON><PERSON>ask
from service.action.ume.pm.KpiAnalyse import KpiAnalyse
from service.action.utility.performance_index_service.PerformanceIndexService import PerformanceIndexService

router = APIRouter(route_class=bizRoute)

# 2025.6.12 调试界面
# KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/684a2d1d794842dcaa574022"
# HIS_KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/684a2d08794842dcaa574021"
# 2025.7.1 恢复真实界面
KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/660f5d7d328f406e8bb8de90"
HIS_KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/660f9d5c328f406e8bb8de9b"

ANALYSIS_DATA_MAP = {
    "PDCCH链路自适应": "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/6847f18d794842dcaa573fa2",
    "CA（载波聚合）": "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/6847f19a794842dcaa573fa3"
}


@router.post("/create", summary="创建实时KPI任务",
             description=description("网管PM模块下，创建指定指标的实时KPI任务", "10255283"))
async def create_task(request: Request, paras: CreateParas):
    mes = await MeFactory.create(request)
    if traceTypeEnum.neTrace == paras.traceType:
        if len(set([me.meType for me in mes])) > 1:
            return fail(
                msg=f"本action不支持同时选择{set([me.meType for me in mes])}类型资源，请通过资源过滤出单一类型网元资源!")
        if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
        ret, err = await AutoKpiTask.create_realtime_kpi_task(mes, paras.kpiParas, paras.granularity, paras.TempUsage,
                                                              paras.templateName, ignoreError=paras.ignoreError)
    else:
        cells = await CellFactory.create(request)
        if cells is None:
            return fail(data=f"过滤的小区为空，请检查")
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds, mes))
        ret, err = await AutoKpiTask.create_realtime_kpi_task(mes, paras.kpiParas, paras.granularity,
                                                              paras.TempUsage, paras.templateName, cells=cells,
                                                              ignoreError=paras.ignoreError)
    if not err:
        return success(data=ret)
    return fail(data=ret, msg=err)


@router.post("/suspend", summary="暂停实时KPI任务",
             description=description("网管PM模块下，暂停实时KPI任务", "周斌兴10288354", "创建实时KPI任务"),
             openapi_extra={"refs": [{"actionName": "创建实时KPI任务", "refPattern": "1-*"}]})
async def suspend_task(request: Request):
    tasksInfo = await Refs(request).get_output()
    flag, errList = True, []
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            if not taskIds:
                errList.append("me: {}'s task is empty, please check".format(meId))
                continue
            me = await MeFactory.create_by_meid(request, meId)
            flag, err = await PerformanceMonitor(me).suspend_realtime_kpi_tasks(taskIds)
            errList.extend(err)
    if errList:
        return fail(msg=errList)
    return success(data="Suspend task success")


@router.post("/active", summary="激活实时KPI任务",
             description=description("网管PM模块下，激活实时KPI任务", "周斌兴10288354", "创建实时KPI任务"),
             openapi_extra={"refs": [{"actionName": "创建实时KPI任务", "refPattern": "1-*"}]})
async def active_task(request: Request):
    tasksInfo = await Refs(request).get_output()
    flag, errList = True, []
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            if not taskIds:
                errList.append("me: {}'s task is empty, please check".format(meId))
                continue
            me = await MeFactory.create_by_meid(request, meId)
            flag, err = await PerformanceMonitor(me).active_realtime_kpi_task(taskIds)
            errList.extend(err)
    if errList:
        return fail(msg=errList)
    return success(data="Active task success")


@router.post("/delete", summary="删除实时KPI任务",
             description=description("网管PM模块下，删除实时KPI任务", "周斌兴10288354", "创建实时KPI任务"),
             openapi_extra={"refs": [{"actionName": "创建实时KPI任务", "refPattern": "1-*"}]})
async def delete_task(request: Request):
    tasksInfo = await Refs(request).get_output()
    flag, errList = True, []
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            if not taskIds:
                errList.append("me: {}'s task is empty, please check".format(meId))
                continue
            me = await MeFactory.create_by_meid(request, meId)
            flag, err = await PerformanceMonitor(me).delete_realtime_kpi_task(taskIds)
            errList.extend(err)
    if errList:
        return fail(msg=errList)
    return success(data="Delete task success")


@router.post("/autoCreate", summary="全自动创建实时KPI任务",
             description=description("网管PM模块下，创建指定指标的实时KPI任务", "周斌兴10288354"))
async def create_realtime_kpi_task_automatic(request: Request):
    mes = await MeFactory.create(request)
    caseId = request.state.resource.get("systemId")
    cells = await CellFactory.create(request)
    if ActionFilter().is_filtered_cell(request.state.resource.get("pipelineStageId")):
        if cells is None:
            return fail(data=f"过滤的小区为空，请检查")
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    else: cells = []
    ret, errors = await AutoKpiTask().create_realtime_kpi_task_automatic(mes, caseId, cells)
    if not errors:
        return success(data=ret)
    return fail(data=ret, msg=errors)


@router.post("/autoExportSftp", summary="导出实时KPI数据到sftp服务器",
             description=description("导出实时KPI数据到sftp服务器", "周斌兴10288354", "全自动创建实时KPI任务"),
             openapi_extra={"refs": [{"actionName": "创建实时KPI任务", "refPattern": "1-*"}]})
async def auto_export_data_to_sftp(request: Request, para: ExportKpiDataToSftp):
    tasksInfo = await Refs(request).get_output()
    sftpPaths, errList = [], []
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            me = await MeFactory.create_by_meid(request, meId)
            saveSftpPath, errmsg = await AutoKpiTask().export_to_sftp(me, taskIds, para.filePath, para.fileName,
                                                                      para.fileType, para.dataHeaderShowType)
            sftpPaths.extend(saveSftpPath)
            errList.extend(errmsg)
    if errList:
        return fail(data=sftpPaths, msg=errList)
    if not sftpPaths:
        return fail(msg="!!没有要导出文件的任务或导出文件失败!!")
    return success(data=sftpPaths)


@router.post("/savePerfData", summary="KPI数据解析入库",
             description=description("网管PM模块下，KPI数据解析入库", '夏晨10242158'),
             openapi_extra={"refs": [{"actionName": "导出实时KPI数据到sftp服务器"}],
                            "urls": [{"name": "KPI-表格-曲线", "url": KPI_TABLE_URL},
                                     ]})
async def save_realtime_kpi_task_perf_data(request: Request, fileListWithTime: FileListWithTimeRange):
    refs_path = await Refs(request).get_output()
    Logger.info("KPI数据解析入库filePathList:{0}".format(refs_path))
    if not refs_path:
        return fail(msg="!!获取不到依赖action路径文件，filePathList is empty!!")
    attachment = dict(fileListWithTime)
    analysisTypes = [anaType.value for anaType in
                     fileListWithTime.analysisTypes] if fileListWithTime.analysisTypes else []
    Logger.info("KPI数据解析入库attachment:{0}".format(attachment))
    attachment.update({"filePathList": refs_path[0]})

    def transform_perf_para():
        resource = request.state.resource
        taskId, taskName, caseId = resource.get("taskId"), resource.get("taskName"), resource.get("systemId")
        if caseId:
            return {"id": taskId, "name": taskName, "taskAttachment": [],
                    "cases": [{"id": caseId, "system_id": caseId, "caseAttachment": [attachment]}]}
        return {"id": taskId, "name": taskName, "cases": [], "taskAttachment": [attachment]}

    try:
        processor = PerformanceIndexService().handle_perf_request(transform_perf_para())
        files = Artifact.get_file_urls(refs_path[0])
        linkHeads = [KPI_TABLE_URL] + [ANALYSIS_DATA_MAP.get(anaType) for anaType in analysisTypes]
        descs = ["实时KPI数据分析"] + analysisTypes
        links = IdsApi().ids_get_hyperlink_and_send_email(request, files, linkHeads,
                                                          processor.ids_links * len(linkHeads), descs)
        return success(msg=f"实时KPI数据解析入库成功, 点击 {links} 跳转IDS")
    except Exception as err:
        Logger.error(traceback.format_exc())
        return fail(msg=f"实时KPI数据解析入库失败,{err}")


@router.post("/savePerfHisData", summary="历史KPI数据解析入库",
             description=description("网管PM模块下，历史KPI数据解析入库", '杨先恒10255283'),
             openapi_extra={"refs": [{"actionName": "导出历史KPI数据到sftp服务器"}],
                            "urls": [{"name": "HIS-KPI-表格-曲线", "url": HIS_KPI_TABLE_URL},
                                     ]})
async def save_history_kpi_task_perf_data(request: Request, fileListWithTime: FileListWithTimeRange):
    refs_path = await Refs(request).get_output()
    Logger.info("KPI数据解析入库filePathList:{0}".format(refs_path))
    if not refs_path:
        return fail(msg="!!获取不到依赖action路径文件，filePathList is empty!!")
    attachment = dict(fileListWithTime)
    Logger.info("KPI数据解析入库attachment:{0}".format(attachment))
    analysisTypes = [anaType.value for anaType in
                     fileListWithTime.analysisTypes] if fileListWithTime.analysisTypes else []
    attachment.update({"filePathList": refs_path[0]})

    def transform_perf_para():
        resource = request.state.resource
        taskId, taskName, caseId = resource.get("taskId"), resource.get("taskName"), resource.get("systemId")
        if caseId:
            return {"id": taskId, "name": taskName, "taskAttachment": [],
                    "cases": [{"id": caseId, "system_id": caseId, "caseAttachment": [attachment]}]}
        return {"id": taskId, "name": taskName, "cases": [], "taskAttachment": [attachment]}

    try:
        processor = PerformanceIndexService().handle_perf_request(transform_perf_para())
        files = Artifact.get_file_urls(refs_path[0])
        linkHeads = [HIS_KPI_TABLE_URL] + [ANALYSIS_DATA_MAP.get(anaType) for anaType in analysisTypes]
        descs = ["历史KPI数据分析"] + analysisTypes
        links = IdsApi().ids_get_hyperlink_and_send_email(request, files, linkHeads,
                                                          processor.ids_links * len(linkHeads), descs)
        return success(msg=f"历史KPI数据解析入库成功, 点击 {links} 跳转IDS")
    except Exception as err:
        Logger.error(traceback.format_exc())
        return fail(msg=f"历史KPI数据解析入库失败,{err}")


@router.post("/queryData", summary="获取实时KPI指定指标的上报数据",
             description=description("网管PM模查询实时KPI监控数据，可获取指定指标的最新值/最大值/最小值/平均值",
                                     "李双红10258068"),
             openapi_extra={"refs": [{"actionName": "创建实时KPI任务", "refPattern": "1-*"}]})
async def query_realtime_kpi_data(request: Request, queryParams: QueryParams):
    tasksInfo = await Refs(request).get_output()
    queryResult, successFlag = {}, True
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            me = await MeFactory.create_by_meid(request, meId)
            kpiIds = [_.upper().strip() for _ in queryParams.kpiCounter.split(",")]
            result = await AutoKpiTask.get_real_kpi_specified_column_values_by_id(me, taskIds, kpiIds,
                                                                                  queryParams.queryType,
                                                                                  queryParams.page, queryParams.perpage,
                                                                                  queryParams.filterDict)
            Logger.info(f"KpiId: {queryParams.kpiCounter}, result:{result}")
            queryResult.update({me.meId: result})
            # successFlag *= resultflag
    if successFlag and queryResult:
        return success(data=queryResult, msg=f"查询{queryParams.queryType}成功")
    return fail(data=queryResult, msg=f"查询{queryParams.queryType}存在失败，请检查入参")


@router.post("/autoHisKpiExportSftp", summary="导出历史KPI数据到sftp服务器",
             description=description("导出历史KPI数据到sftp服务器", "杨先恒"))
async def auto_export_data_to_sftp(request: Request, paras: ExportHisKpiDataToSftp):
    sftpPaths, errList = [], []
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds or me.meType in ["MEC"], mes))
    if len(set([me.meType for me in mes])) > 1:
        return fail(
            msg=f"本action不支持同时选择{set([me.meType for me in mes])}类型资源，请通过资源过滤出单一类型网元资源!")
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    for me in mes:
        saveSftpPath, errmsg = await AutoKpiTask().export_his_kpi_to_sftp(me, paras.kpiParas, paras.collectionTime,
                                                                          paras.TempUsage, paras.templateName,
                                                                          paras.granularity, ignoreError=paras.ignoreError)
        if saveSftpPath:
            sftpPaths.extend(saveSftpPath)
        errList.extend(errmsg)
    if errList:
        return fail(data=sftpPaths, msg=errList)
    return success(data=sftpPaths)


@router.post("/createMeasure", summary="创建测量任务",
             description=description("网管PM模块下，创建指定指标的测量任务，自动激活无需调用激活测量任务", "10255283"))
async def create_measure(request: Request, paras: CreateMeasureParas):
    mes = await MeFactory.create(request)
    if len(set([me.meType for me in mes])) > 1:
        return fail(
            msg=f"本action不支持同时选择{set([me.meType for me in mes])}类型资源，请通过资源过滤出单一类型网元资源!")
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    ret, err = await AutoKpiTask().create_measure_task(mes, paras.kpiParas, paras.granularity, paras.TrackingTime)
    if not err:
        return success(data=ret)
    return fail(data=ret, msg=err)


@router.post("/suspendMeasure", summary="暂停测量任务",
             description=description("网管PM模块下，暂停指定指标的测量任务", "10255283"),
             openapi_extra={"refs": [{"actionName": "创建测量任务", "refPattern": "1-*"}]})
async def suspend_measure_task(request: Request):
    tasksInfo = await Refs(request).get_output()
    flag, errList = True, []
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            if not taskIds:
                errList.append("me: {}'s task is empty, please check".format(meId))
                continue
            me = await MeFactory.create_by_meid(request, meId)
            for taskId in taskIds:
                ret, err = await MeasureTask(me).suspend_history_kpi_task(taskId)
                errList.extend(err)
    if errList:
        return fail(msg=errList)
    return success(data="暂停测量任务成功")


@router.post("/deleteMeasure", summary="删除测量任务",
             description=description("网管PM模块下，删除指定指标的测量任务", "10255283"),
             openapi_extra={"refs": [{"actionName": "创建测量任务", "refPattern": "1-*"}]})
async def suspend_measure_task(request: Request):
    tasksInfo = await Refs(request).get_output()
    flag, errList = True, []
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            if not taskIds:
                errList.append("me: {}'s task is empty, please check".format(meId))
                continue
            me = await MeFactory.create_by_meid(request, meId)
            for taskId in taskIds:
                ret, err = await MeasureTask(me).delete_history_kpi_task(taskId)
                errList.extend(err)
    if errList:
        return fail(msg=errList)
    return success(data="删除测量任务成功")


@router.post("/activeMeasure", summary="激活测量任务",
             description=description("网管PM模块下，激活指定指标的测量任务", "10255283"),
             openapi_extra={"refs": [{"actionName": "创建测量任务", "refPattern": "1-*"}]})
async def suspend_measure_task(request: Request):
    tasksInfo = await Refs(request).get_output()
    flag, errList = True, []
    for taskInfo in tasksInfo:
        for meId, taskIds in taskInfo.items():
            if not taskIds:
                errList.append("me: {}'s task is empty, please check".format(meId))
                continue
            me = await MeFactory.create_by_meid(request, meId)
            for taskId in taskIds:
                ret, err = await MeasureTask(me).active_history_kpi_task(taskId)
                errList.extend(err)
    if errList:
        return fail(msg=errList)
    return success(data="激活测量任务成功")


@router.post("/iask_anysis_his_kpi", summary="IASK对比分析历史KPI数据",
             description=description(
                 "通过依赖勾选前后两次历史KPI数据(两个action跟踪目标KPI需要一致),自动上传IASK并完成对比分析,生成对比报告",
                 '侯小飞10270755'),
             openapi_extra={"refs": [{"actionName": "导出历史KPI数据到sftp服务器", "refPattern": "2"}]})
async def iask_anysis_his_kpi(request: Request, paras: IaskHisKpi):
    user = request.state.resource.get("user") or "10270755"
    refs_path = await Refs(request).get_output()
    Logger.info(f"KPI数据文件列表: {refs_path}")
    result, successFlag = [], True
    if not refs_path:
        return fail(msg="!!获取不到依赖action文件列表是空的，请确认已勾选依赖!!!")
    try:
        mefiles = IaskService.format_ref_kpi(refs_path)
        if not mefiles:
            fail(
                msg="!!获取2个依赖历史kpi查询结果为空，请确认勾选的2个依赖'导出历史KPI数据到sftp服务器'选择的kpi是一致的!!!")
        for meinfo, files in mefiles.items():
            if len(files) != 2:
                successFlag = False
                result.append(f"{meinfo}对比文件数量不等于2，请确认依赖的2个目标action跟踪的kpi是否一致")
                continue
            ret, info = await IaskService.create_iask_and_get_result(files[0], files[1], user, int(paras.configFileId))
            result.append(info)
            if not ret:
                successFlag = False
    except Exception as err:
        result.append(f"{err}")
        Logger.error(traceback.format_exc())
        successFlag = False
    if successFlag:
        return success("IASK对比分析历史KPI数据执行成功", data=result)
    return fail("IASK对比分析历史KPI数据执行失败", data=result)


@router.post("/calculate_ty_data", summary="调优性能数据处理",
             description=description("输入调优文件，按照计算公式进行调优性能数据计算处理，输出计算后的excel文件链接", "10270755"),
             openapi_extra={"refs": [{"actionName": "导出历史KPI数据到sftp服务器", "refPattern": "1"}]})
async def calculate_ty_data(request: Request, paras: TyCalculateKpi):
    refs_path = (await Refs(request).get_output())[0]
    Logger.info(f"历史KPI数据文件列表: {refs_path}")
    result = []
    if not refs_path:
        return fail(msg="!!获取不到依赖action文件列表是空的，请确认已勾选依赖!!!")
    try:
        result = KpiAnalyse(paras.tYFilePath[0]).batch_process(paras.calculateCmd, refs_path)
        return success("调优性能数据处理成功", data=result)
    except Exception as err:
        Logger.error(traceback.format_exc())
        return fail(f"调优性能数据处理失败{err}", data=result)
