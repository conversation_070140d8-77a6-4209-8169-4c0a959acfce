#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:25
# <AUTHOR> 10263601
from datetime import datetime
from enum import Enum
from typing import Literal, TypeVar

from pydantic import BaseModel, Field, validator

DataT = TypeVar("DataT")
KPI_PARA = {
    "PLMNCUCP类型": ["C600080042"],
    "PLMNDU类型": ["C613250012[1,2,3,4,9]"],
    "DU物理小区类型": ["670068", "C616310000"]
}

MEASURE_PARA = {
    "灵活可配的CUUP小区类型": ["60800"],
    "PLMNCUCP类型": ["60094"]
}


class traceTypeEnum(str, Enum):
    neTrace = "基站级"
    cellTrace = "小区级"

class AnalysisDataMode(str, Enum):
    pdcch = "PDCCH链路自适应"
    ca = "CA（载波聚合）"


class FileListWithTimeRange(BaseModel):
    before_upgrade_beg_time: datetime | None = None
    before_upgrade_end_time: datetime | None = None
    after_upgrade_beg_time: datetime | None = None
    after_upgrade_end_time: datetime | None = None
    analysisTypes: list[AnalysisDataMode] = Field(default=[], description="选择功能规则数据分析,结果中显示对应跳转链接")


class ExportKpiDataToSftp(BaseModel):
    filePath: str = ""
    fileName: str = ""
    fileType: Literal['xlsx', 'csv'] = Field('xlsx')
    dataHeaderShowType: Literal['显示ID和名称', '显示ID', '显示名称'] = Field('显示ID和名称')


class CreateParas(BaseModel):
    kpiParas: DataT = Field(KPI_PARA, type="jsonEditor",
                            description="\n示例：\n{\"PLMNCUUP类型\": [\n\"668010\",\n\"668002\",\n],\n\"DU物理小区类型\": [\n\"670068\",\n\"C616310000\"]}\n")
    granularity: Literal['10', '30', "60"] = Field('10', description='粒度,单位秒')
    traceType: Literal['基站级', '小区级'] = Field('基站级', description='默认基站级')
    TempUsage: Literal[False, True] = Field(False, description='是否使用模板，使用勾选，使用模板后默认忽略kpiParas参数，默认不使用')
    templateName: str = Field(None, description='KPI模板名')
    ignoreError: bool = Field(False, description='是否忽略counter不存在错误，默认不忽略，true - 忽略； false - 不忽略')

class QueryType(str, Enum):
    new = "最新值"
    average = "平均值"
    max = "最大值"
    min = "最小值"


class QueryParams(BaseModel):
    kpiCounter: str = Field(...,
                            description='KPI指标/Counter计数器编号，多个指标/计数器之间用逗号分隔，示例：C616660001,670068')
    queryType: QueryType
    page: int = Field(1, description='KPI数据第几页，默认为1，表示查询第一页(即最新)的数据')
    perpage: int = Field(1500, description='KPI数据每页显示的数据条数，默认为1500，表示查询1500行数据')
    filterDict: str = Field(None,
                            description='KPI任务表头过滤字典列表，格式为:过滤列名=过滤值，多个过滤条件之间用逗号分隔，示例：DU物理小区ID=1,duMeMoId=4449')

    @validator("filterDict")
    def attrs_to_dict(cls, v):
        if not v:
            return None
        d = dict()
        for item in v.split(','):
            k, v = item.split('=')
            d[k.strip()] = v.strip()
        return d

class ExportHisKpiDataToSftp(BaseModel):
    kpiParas: DataT = Field(KPI_PARA, type="jsonEditor",
                            description="\n示例：\n{\"PLMNCUUP类型\": [\n\"668010\",\n\"668002\",\n],\n\"DU物理小区类型\": [\n\"670068\",\n\"C616310000\"]}\n")

    collectionTime: int = Field(8, description='最近小时数据，默认值8，单位小时')
    TempUsage: Literal[False, True] = Field(False, description='是否使用模板，使用勾选，使用模板后默认忽略kpiParas参数，默认不使用')
    templateName: str = Field(None, description='KPI模板名')
    granularity: Literal['15', '5', '30', '1时', '1天', '1周', '1月'] = Field('15', description='粒度,单位min')
    ignoreError: bool = Field(False, description='是否忽略counter不存在错误，默认不忽略，true - 忽略； false - 不忽略')

    @validator("granularity", pre=True)
    def update_value(cls, v):
        match v:
            case "1月":
                v = "43200"
            case "1周":
                v = "10080"
            case "1天":
                v = "1440"
            case "1时":
                v = "60"
            case _:
                v = v
        return v

class CreateMeasureParas(BaseModel):
    kpiParas: DataT = Field(MEASURE_PARA, type="jsonEditor",
                            description="参照默认值格式")
    granularity: Literal['15', '5'] = Field('15', description='粒度,单位min')
    TrackingTime: int = Field(999, description='最近小时数据，默认值999，单位小时')


class IaskHisKpi(BaseModel):
    configFileId: str = Field("462146", description='创建iask任务所选的配置文件id,如果有其他配置文件需求,请联系(任涛)添加提供')

class TyCalculateKpi(BaseModel):
    calculateCmd: str = Field("TuningFactorA*#KPIVALUE#+TuningFactorB*random.random()+TuningFactorC",
                              description='调优计算公式,其中#KPIVALUE#表示KPI的值，其他TuningFactorA表示调优文件中的key值，可根据需要进行编写')
    tYFilePath: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                            allowedFileType=".csv", limit=1, params=[],
                            description="调优规则文件，必须要按照指定规则进行填写")

