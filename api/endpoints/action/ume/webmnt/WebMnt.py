#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/9/27 15:41
# <AUTHOR> 10255283

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.webmnt.Schemas import WebMntParas, setWebMntParas
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.factories.WebmntFactory import WebmntFactory
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from infrastructure.device.dspmonitor.DspMonitorExecutor import DspMonitorExecutor
from domain.factories.VbpFactory import VbpFactory
from domain.models.ume.rancm.replaceableunit.RePlaceableunit import RePlaceableunit

router = APIRouter(route_class=bizRoute)


@router.post("/query", summary="webmnt查询DSP监控值",
             description=description("在webmnt下查询DSP监控值", "10255283"))
async def get_webmnt_dsp(request: Request, paras: WebMntParas):
    mes = await MeFactory.create_by_nr(request)
    webmnts = await WebmntFactory.create(request, mes[0])
    vbps = await VbpFactory.create(request)
    meIds = set(vbp.me._gnbId for vbp in vbps)
    mes = list(filter(lambda me: me.meId in meIds, mes))
    vbpName = await RePlaceableunit(mes[0]).get_vbp_name(vbps[0].slot)
    productVersion = await RePlaceableunit(mes[0]).get_HLF()
    flag,result = await DspMonitorExecutor(webmnts[0], vbpName).read_pointer_value(vbps[0], paras.boardId, paras.coreId,
                                                                              paras.expression, productVersion)
    if flag:
        return success(data={"result": result}, msg="监控值查询成功")
    return fail(data={"result": result}, msg="请检查配置")


@router.post("/set", summary="webmnt设置DSP监控值",
             description=description("在webmnt下设置DSP监控值，", "10255283"))
async def set_webmnt_dsp(request: Request, paras: setWebMntParas):
    mes = await MeFactory.create_by_nr(request)
    webmnts = await WebmntFactory.create(request, mes[0])
    vbps = await VbpFactory.create(request)
    meIds = set(vbp.me._gnbId for vbp in vbps)
    mes = list(filter(lambda me: me.meId in meIds, mes))
    vbpName = await RePlaceableunit(mes[0]).get_vbp_name(vbps[0].slot)
    productVersion = await RePlaceableunit(mes[0]).get_HLF()
    result = await DspMonitorExecutor(webmnts[0], vbpName).write_pointer_value(vbps[0], paras.boardId, paras.coreId,
                                                                               paras.value, paras.expression,
                                                                               productVersion)

    return success(data={"result": result}, msg="监控值修改成功")
