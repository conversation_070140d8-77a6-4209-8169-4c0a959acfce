#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/5/07 10:25
# <AUTHOR> 10255283

from typing import TypeVar, Literal
from pydantic import BaseModel, Field, root_validator
from enum import Enum

DataT = TypeVar("DataT")


class OptionEnum(str, Enum):
    value1 = "VBP"
    value2 = "AAU"
    value3 = "RRU"


class WebMntParas(BaseModel):
    instructions: DataT = Field(None, type="label", viewType="url", urlName="指导手册",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/c6bc80e2ff36407792506bbc959b0d5a/view")

    boardId: int = Field(0, description="板子id")
    coreId: int = Field(15, description="核id", ifEmptyUseDefault=False)
    expression: str = Field("gPhsLogPrintCfg ->logParaCfg.printBitSwitch", description="操作表达式,填写完整路径")

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        coreId_field = cls.__fields__["coreId"]
        if not values.get("coreId"):
            if not coreId_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["coreId"] = 0
        return values


class setWebMntParas(BaseModel):
    instructions: DataT = Field(None, type="label", viewType="url", urlName="指导手册",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/c6bc80e2ff36407792506bbc959b0d5a/view")

    boardId: int = Field(0, description="板子id")
    coreId: int = Field(11, description="核id")
    expression: str = Field("g_stubBsrDataForCell[1].stubBsrSwitch", description="操作表达式,填写完整路径")
    value: int = Field(0, description="需要修改的值")
