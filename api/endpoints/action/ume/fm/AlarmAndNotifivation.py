#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/9/27 15:41
# <AUTHOR> 10255283
import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.fm.Schemas import QueryAlarm, QueryHistoryAlarm, QueryNotification, Diagnosis
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.factories.CellFactory import CellFactory
from domain.platform.ActionInfo import description
from domain.platform.artifact import Artifact
from infrastructure.logger import Logger
from service.action.ume.fm.ActiveAlarmService import ActiveAlarmService
from infrastructure.resource.service.Refs import Refs
from domain.models.ume.fm.Notification import Notification
from domain.models.ume.fm.OneClickDiagnosis import OneClickDiagnosis

router = APIRouter(route_class=bizRoute)


@router.post("/get", summary="第一次查询告警",
             description=description("网管fm模块查询当前告警监控", "10255283"))
async def get_alarm(request: Request):
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds or me.meType in ["MEC"], mes))
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    taskInfo = {}
    for me in mes:
        if me.meType in ["MEC"]: cells = []
        ret = await ActiveAlarmService().query_alarm_info(me, cells)
        if ret:
            taskInfo.update({me.meId: ret})
        else:
            taskInfo.update({me.meId: "无告警"})
    if taskInfo:
        return success(data={"taskInfo": taskInfo}, msg="查询告警成功")
    return success(data={"taskInfo": []}, msg="查询告警成功")


@router.post("/get_second_time", summary="查询是否有新增告警",
             description=description("网管fm模块查询当前告警监控，需配合”第一次查询告警“action使用", "10255283"),
             openapi_extra={"refs": [{"actionName": "第一次查询告警"}]})
async def get_alarm_second_time(request: Request):
    taskInfo = {}
    addalarm = []
    diff = []
    queryInfo = await Refs(request).get_output("taskInfo")
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds or me.meType in ["MEC"], mes))
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    for me in mes:
        if me.meId in queryInfo[0].keys():
            if me.meType in ["MEC"]: cells = []
            ret = await ActiveAlarmService().query_alarm_info(me, cells)
            if ret:
                taskInfo.update({me.meId: ret})
            if taskInfo:
                Logger.debug(f"最新查询的告警为:{taskInfo},meid为{me.meId}")
                if "无告警" not in queryInfo[0].get(me.meId):
                    diff = list(set(taskInfo.get(me.meId).get("alarmraisedtime")) - set(
                        queryInfo[0].get(me.meId).get("alarmraisedtime")))
                else:
                    diff = list(set(taskInfo.get(me.meId).get("alarmraisedtime")))
        if diff:
            for i in range(len(diff)):
                locations = [index for index, value in enumerate(taskInfo.get(me.meId).get("alarmraisedtime")) if
                             value == diff[i]]
                for j in range(len(locations)):
                    addalarm.append(taskInfo.get(me.meId).get("alarmcode")[j])
            return success(data=addalarm, msg=f"{me.meId}网元有新增告警,详情见data!")
    return success(data=None, msg="无新增告警")


@router.post("/check", summary="特定告警码或者告警名称是否上报",
             description=description("网管fm模块查询当前告警监控，通过告警码或者告警码名称查询告警是否存在", "10255283"))
async def get_alarm_special(request: Request, queryAlarm: QueryAlarm):
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds or me.meType in ["MEC"], mes))
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    result = {}
    for me in mes:
        if me.meType in ["MEC"]: cells = []
        res, alarm = await ActiveAlarmService().query_alarm_existance(me, cells, queryAlarm.alarmCodeName)
        if res:
            result.update({me.meId: alarm})
    if result:
        return success(data=result, msg=f"告警{queryAlarm.alarmCodeName}正常上报")
    return success(data=None, msg=f"告警{queryAlarm.alarmCodeName}没有上报")


@router.post("/history", summary="查询历史告警",
             description=description("网管fm模块查询选择的时间段历史告警", "10255283"))
async def get_history_alarm(request: Request, queryHistoryAlarm: QueryHistoryAlarm):
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds or me.meType in ["MEC"], mes))
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    taskInfo = {}
    for me in mes:
        if me.meType in ["MEC"]: cells = []
        ret = await ActiveAlarmService().query_history_alarm(me, cells, queryHistoryAlarm.RelativeDay)
        if ret:
            taskInfo.update({me.meId: ret})
    if taskInfo:
        return success(data={"taskInfo": taskInfo}, msg="查询历史告警成功")
    return success(data=None, msg="无告警")


@router.post("/get_notification_for_day", summary="第一次查询通知",
             description=description("网管fm模块查询当前通知查询", "10255283"))
async def get_notification(request: Request, queryNotification: QueryNotification):
    mes = await MeFactory.create(request)
    taskInfo = {}
    for me in mes:
        ret = await Notification(me).get_notification_info_list(queryNotification.RelativeDay)
        if ret:
            taskInfo.update({me.meId: ret})
    if taskInfo:
        return success(data={"taskInfo": taskInfo}, msg="查询通知成功")
    return success(data={"taskInfo": []}, msg="查询通知成功")


@router.post("/get_second_time_notification", summary="查询是否有新增通知",
             description=description(
                 "网管fm模块查询当前通知，需配合”第一次查询通知“action使用,RelativeDay需和‘第一次通知查询填写一致’",
                 "10255283"),
             openapi_extra={"refs": [{"actionName": "第一次查询通知"}]})
async def get_notification_second_time(request: Request, queryNotification: QueryNotification):
    taskInfo = {}
    queryInfo = await Refs(request).get_output("taskInfo")
    mes = await MeFactory.create(request)
    addalarm = []
    for me in mes:
        ret = await Notification(me).get_notification_info_list(queryNotification.RelativeDay)
        if ret:
            taskInfo[me.meId] = ret
        task_me_info = taskInfo.get(me.meId, [])
        if queryInfo[0]:
            query_me_info = queryInfo[0].get(me.meId, [])
            notificationnum = len(task_me_info) - len(query_me_info)
        else:
            notificationnum = len(task_me_info)
        if notificationnum > 0:
            addalarm.extend(task_me_info[:notificationnum])
            return success(data=[entry["codename"] for entry in addalarm], msg=f"网元{me.meId}有新增通知")

    return success(data=None, msg="无通知")



@router.post("/get_all_notification", summary="查询所有通知",
             description=description("网管fm模块查询当前通知查询", "10255283"))
async def get_all_notification(request: Request):
    mes = await MeFactory.create(request)
    taskInfo = {}
    for me in mes:
        ret = await Notification(me).get_all_notification()
        if ret:
            taskInfo.update({me.meId: ret})
    if taskInfo:
        return success(data={"taskInfo": taskInfo}, msg="查询通知成功")
    return success(data=None, msg="无通知")


@router.post("/start_diagnosis_task", summary="启动根因诊断任务",
             description=description("网管fm模块查询选取特定的告警进行根因诊断", "10255283"))
async def start_diagnosis(request: Request, paras: Diagnosis):
    mes = await MeFactory.create(request)
    taskInfo = {}
    err = {}
    for me in mes:
        flg, ret = await ActiveAlarmService().start_diagnosis_task(me, paras)
        if flg:
            taskInfo.update({me.meId: ret})
        else:
            err.update({me.meId: ret})
    if err:
        return fail(data={"err": err}, msg="创建根因诊断失败")
    return success(data={"taskInfo": taskInfo}, msg="创建根因诊断成功")


@router.post("/get_diagnosis_result", summary="获取根因诊断结果",
             description=description("网管fm模块查询选取特定的告警获取根因诊断结果", "10255283"),
             openapi_extra={"refs": [{"actionName": "启动根因诊断任务"}]})
async def get_diagnosis(request: Request):
    mes = await MeFactory.create(request)
    taskinfo = await Refs(request).get_output("taskInfo")
    err, result, codename = {}, {}, ""
    for me in mes:
        alarmDetail = taskinfo[0].get(me.meId)
        if not alarmDetail: continue
        flg, ret = await OneClickDiagnosis(me).get_diagnosis_detail(alarmDetail)
        codename = alarmDetail.get("codename")
        if flg:
            result.update({me.meId: ret})
        else:
            err.update({me.meId: ret})
    if err:
        return fail(data={"err": err}, msg="获取根因诊断结果失败")
    if codename:
        Artifact.submit_data_as_json(result, name=f"{codename}", desc=f"'{codename}'根因诊断结果信息",
                                     fileType="根因诊断")
    return success(data={"result": result}, msg="获取根因诊断结果成功")
