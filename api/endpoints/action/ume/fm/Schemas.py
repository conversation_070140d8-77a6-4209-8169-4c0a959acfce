#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:25
# <AUTHOR> 10255283
from datetime import datetime
from typing import TypeVar

from pydantic import BaseModel, Field

DataT = TypeVar("DataT")

class QueryAlarm(BaseModel):
    alarmCodeName: str = Field("DU小区退服", description="告警码或者告警名称")

class QueryHistoryAlarm(BaseModel):
    RelativeDay: int = Field(1, description="单位天，最近的天数")

class QueryNotification(BaseModel):
    RelativeDay: int = Field(1, description="单位天，最近的天数")

class Diagnosis(BaseModel):
    alarmCodeName: str = Field("DU小区退服", description="告警码或者告警名称")
    index: int = Field(0, description="诊断的告警顺序，默认0，表示第一个，以此类推")