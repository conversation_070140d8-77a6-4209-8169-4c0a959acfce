#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:25
# <AUTHOR> 10263601
from datetime import datetime
from enum import Enum
from typing import TypeVar, Literal

from pydantic import BaseModel, Field, validator
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")


class FileListWithTimeRange(BaseModel):
    before_upgrade_beg_time: datetime | None = None
    before_upgrade_end_time: datetime | None = None
    after_upgrade_beg_time: datetime | None = None
    after_upgrade_end_time: datetime | None = None


class OptionEnum(str, Enum):
    value = "BBSNIFFER"
    value1 = "BBPHY"
    value2 = "AAUSNIFFER"
    value3 = "BBULOGCONFIG"


TAMP_UDAJSON = {"最大异常触发次数":"2","参数模板":{"Common": {"CellID": [], "Key": "phy", "TimeLen": 60, "storagePolicy": 1, "LogFileSize": 10}, "UeFilter":
    {"TraceUeFlag": 0, "conditionFlag": 1, "ueIdx": [], "ueRecordId": []}, "SceneList": {"SceneId": []}, "TriggerList": {"TriggerId": []},
    "L0LogFilter": {"MsgType": []}, "L1LogFilter": {"MsgType": []}, "L2LogFilter": {"MsgType": []}, "PhyLog": {"phyLogScene": 554381322,
                                                                                                               "phyLogTriggerList":[], "phyLogFilter":[]}}}


class UdaLogCapture(BaseModel):
    logItemId: OptionEnum
    jsonContext: DataT = Field(default=TAMP_UDAJSON, type="jsonEditor",
                               description="抓数对应的JSON文件，请确保输入为JSON格式;可直接输入界面属性参数,比如'最大异常触发次数',底层会自动转换,不输入时会自动填充默认值;"
                                           "对于BBPHY只有输入rhpFpgaCatchFlag开关打开时,才进行AAU联合抓数")


TAMP_AUTOLOGJSON = {"dataFilterTypes": [],
                    "dataTypeGroups": [
                        {"groupName": "UPA_FAIL", "dataTypes": [
                            {"dataId": "46107", "dataIdName": "TRIG_UPA_MSG34_TIMEOUT"}]},
                        {"groupName": "SPA_FAIL", "dataTypes": [
                            {"dataId": "46006", "dataIdName": "TRIG_SPA_RLF"}]}]
                    }


class CreateAutoLogCapture(BaseModel):
    guidebook: DataT = Field(None, type="label", viewType="url", urlName="AutoLog参数及跟踪指标大全，需要英文版参数",
                             url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/"
                                 "9938d0adb52e4f1cb52a2968d31ef0ed/view")
    collectionLevel: Literal["基站级", "小区级", "单板级"] = Field(default="基站级")
    supportUpload: Literal["否", "是"] = Field(default="否")
    collectionStartTime: datetime | None = None
    collectionEndTime: datetime | None = None
    jsonContext: DataT = Field(default=TAMP_AUTOLOGJSON, type="jsonEditor",
                               description='dataFilterTypes:对应Autolog参数,空的话表示默认值。如:"dataFilterTypes":[]'					
                                    'dataTypeGroups:对应Trigger,需要传入groupName以及dataTypes（dataId以及dataidName),'
                                    '每一个groupName都是一个字典。如:'
    '"dataTypeGroups":[{"groupName":"UPA_FAIL","dataTypes":[{"dataId":"46107","dataIdName":"TRIG_UPA_MSG34_TIMEOUT"}]},'
    '{"groupName":"SPA_FAIL","dataTypes":[{"dataId":"46006","dataIdName":"TRIG_SPA_RLF"}]}')

class logItemTypeEnum(str, Enum):
    almLog = "AlmLog"
    dataMLog = "DataMLog"
    nfoamLog = "NfoamLog"
    oamLog = "OamLog"
    ossLog = "OssLog"
    brsLog = "BrsLog"
    sysCtrlLog = "SysCtrlLog"
    fmdata = "Fmdata"
    pmdata = "Pmdata"
    configdata = "Configdata"
    shellLog = "ShellLog"
    blackBox = "BlackBox"
    sgcLog = "SgcLog"
    brsPcap = "BrsPcap"
    swmLog = "SwmLog"
    bspLog = "BspLog"
    rruLog = "RruLog"
    rumLog = "RumLog"
    tcfsLog = "TcfsLog"
    sonLog = "SonLog"
    rcmLog = "RcmLog"
    databaseLog = "DatabaseLog"
    qcellLog = "QcellLog"
    probeLog = "ProbeLog"
    slaveMemLog = "SlaveMemLog"
    bsoLog = "BsoLog"
    baseCheckLog = "BaseCheckLog"
    udtLog = "UdtLog"
    apmLog = "ApmLog"
    verydb = "Verydb"
    ossLogVbp = "OssLogVbp"
    ossLogVsw = "OssLogVsw"
    coreDumpLog = "CoreDumpLog"
    idbData = "IdbData"
    cpaLog = "CpaLog"
    upaLog = "UpaLog"
    ipaLog = "IpaLog"
    fpgaLog = "FpgaLog"
    spaLog = "SpaLog"
    phyLog = "PhyLog"
    operaLog = "OperaLog"
    autoInnerSignaling = "AutoInnerSignaling"
    innerMetrics = "InnerMetrics"
    innerMetricsUPAVonr = "InnerMetricsUPAVonr"
    extendedMetrics = "ExtendedMetrics"
    decodeSnapShot = "DecodeSnapShot"
    autoLog = "AutoLog"
    vonrMetrics = "VonrMetrics"
    performanceMetrics = "PerformanceMetrics"
    BSRMetrics = "BSRMetrics"
    distribMetrics = "DistribMetrics"
    exceptionMetrics = "ExceptionMetrics"
    ITRANMetrics = "ITRANMetrics"
    RHPMetrics = "RHPMetrics"
    CUCPMetrics = "CUCPMetrics"
    UPAMetrics = "UPAMetrics"
    RCAMetrics = "RCAMetrics"
    specialUeLog = "SpecialUeLog"
    UDTTraceLog = "UDTTraceLog"
    innerMetricsLTE = "InnerMetricsLTE"
    IPATraceLog = "IPATraceLog"
    lteAutoLog = "LteAutoLog"
    innerMetricsRNLC = "InnerMetricsRNLC"
    lteInnerMetrics = "LteInnerMetrics"


class LogCapture(BaseModel):
    logItemId: list[logItemTypeEnum] = [logItemTypeEnum.cpaLog, logItemTypeEnum.upaLog, logItemTypeEnum.spaLog, logItemTypeEnum.phyLog]
    collectionLevel: Literal["基站级", "小区级", "单板级"] = Field(default="基站级")
    collectionDuration: int = Field(30, description='采集时长，单位min, 默认30min')



class CustomLogCapture(BaseModel):
    guidebook: DataT = Field(None, type="label", viewType="url", urlName="使用说明",
                             url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/647dc7ca10ec4e5f84820094ab22d662/view")
    level: Literal["基站级", "小区级", "单板级"] = Field(default="基站级", description='采集级别，默认基站级')
    execStrategy: Literal["立即执行", "自定义时段"] = Field(default="立即执行", description='执行策略，默认立即执行')
    duration: int = Field(30, description='采集时长，单位min, 默认30min')
    startTime: datetime | None = Field(None, description='采集开始时间,默认不填,表示当前系统时间为开始时间')
    endTime: datetime | None = Field(None, description='采集结束时间,默认不填,表示当前系统时间延后1小时为结束时间')
    isSupportUpload: bool = Field(False, description='主动上报开关,默认关')
    logItems: DataT = Field(default=[], type="jsonEditor",
                            description='用户F12抓取到创建一键式任务时，与网管交互payload中logItems属性')


    @classmethod
    def schema(cls):
        schema_map = [
            ({"execStrategy": ["立即执行"]}, {"duration": 30}),
            ({"execStrategy": ["自定义时段"]}, {"startTime": None, "endTime": None})
        ]
        dynamic_paras = ["startTime", "endTime", "duration"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)
CustomLogCapture.schema()


QueryItemMap = {"任务状态": "curstatus", "状态描述": "statusdesc", "日志信息": "loginfo"}
class QueryItemEnum(str, Enum):
    curstatus = "任务状态"
    statusdesc = "状态描述"
    loginfo = "日志信息"
class QueryLogCapture(BaseModel):
    queryItems: list[QueryItemEnum] = Field([], description='需要查询信息,默认不勾选,表示返回所有信息')
    @validator("queryItems",)
    def invert_item(cls, v):
        return [QueryItemMap.get(item) for item in v]


class ScenesLogCapture(BaseModel):
    guidebook: DataT = Field(None, type="label", viewType="url", urlName="使用说明",
                             url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/647dc7ca10ec4e5f84820094ab22d662/view")
    level: Literal["基站级", "小区级", "单板级"] = Field(default="基站级", description='采集级别，默认基站级')
    execStrategy: Literal["立即执行", "自定义时段"] = Field(default="立即执行", description='执行策略，默认立即执行')
    duration: int = Field(30, description='采集时长，单位min, 默认30min')
    startTime: datetime | None = Field(None, description='采集开始时间,默认不填,表示当前系统时间为开始时间')
    endTime: datetime | None = Field(None, description='采集结束时间,默认不填,表示当前系统时间延后1小时为结束时间')
    isSupportUpload: bool = Field(False, description='主动上报开关,默认关')
    # logItems: DataT = Field(default=[], type="jsonEditor",description='用户F12抓取到创建一键式任务时，与网管交互payload中logItems属性')
    collectionScenes: str = Field('PM_ACCESS_PROBLEM', description='用户F12抓取到创建的一键式任务时，选择场景模版，请求collectionSceneDetail与网管交互payload中moIds的属性')
    @classmethod
    def schema(cls):
        schema_map = [
            ({"execStrategy": ["立即执行"]}, {"duration": 30}),
            ({"execStrategy": ["自定义时段"]}, {"startTime": None, "endTime": None})
        ]
        dynamic_paras = ["startTime", "endTime", "duration"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)
ScenesLogCapture.schema()


class MetricsItemEnum(str, Enum):
    InnerMetricsFPGA = "InnerMetricsFPGA"
    ITRANMetrics = "ITRANMetrics"
    DistribMetrics = "DistribMetrics"
    InnerMetrics = "InnerMetrics"
    InnerMetricsRCA = "InnerMetricsRCA"
    InnerMetricsUPAMbs = "InnerMetricsUPAMbs"
    VonrMetrics = "VonrMetrics"
    MetricsCustomize = "MetricsCustomize"
    ExceptionMetrics = "ExceptionMetrics"
    CPAMetrics = "CPAMetrics"
    ExtendedMetrics = "ExtendedMetrics"
    RHPMetrics = "RHPMetrics"
    InnerMetricsUPAVonr = "InnerMetricsUPAVonr"
    InnerMetricsUESPA = "InnerMetricsUESPA"
    UPAMetrics = "UPAMetrics"
    InnerMetricsUECPA = "InnerMetricsUECPA"
    InnerMetricsUEUPA = "InnerMetricsUEUPA"
    RCAMetrics = "RCAMetrics"


METRICS_TEMP_JSON = {
    "SPA":["全量"],
    "CPA":["全量"],
    "UPA":["全量"]
}
class MetricsDecode(BaseModel):
    decodeScenes: DataT = Field(default=METRICS_TEMP_JSON,
                                type="jsonEditor",description='场景类型,默认:{"SPA":["全量"],"CPA":["全量"],"UPA":["全量"]},'
                                                              '可参考mia系统场景类型参数,根据需要修改')
    taskType: Literal["仅解码", "解码+分析"] = Field(default="仅解码", description="任务类型")


class HistoryMetricsCollection(BaseModel):
    start_time: datetime | None = None
    end_time: datetime | None = None
    collection_time: int = 60
    decodeScenes: DataT = Field(default=METRICS_TEMP_JSON,
                                type="jsonEditor", description='场景类型,默认:{"SPA":["全量"],"CPA":["全量"],"UPA":["全量"]},'
                                                               '可参考mia系统场景类型参数,根据需要修改')
    taskType: Literal["仅解码", "解码+分析"] = Field(default="仅解码", description="任务类型")
    logItems: list[MetricsItemEnum] = Field([MetricsItemEnum.InnerMetrics], description='需要下载的metrics类型,支持多选')

