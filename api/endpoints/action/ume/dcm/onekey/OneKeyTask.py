import copy
import json
import logging
import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.dcm.onekey.Schemas import FileListWithTimeRange, HistoryMetricsCollection, UdaLogCapture, \
    CreateAutoLogCapture, LogCapture, CustomLogCapture, QueryLogCapture, ScenesLogCapture, MetricsDecode
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from domain.platform.artifact import Artifact
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from infrastructure.utils.ResultFormatter import format_query_mo_result
from service.action.ume.dcm.AutoDcmTask import AutoDcmTask
from service.action.ume.dcm.LogCollectionService import LogCollectionService
from service.action.utility.performance_index_service.PerformanceIndexService import PerformanceIndexService
from domain.factories.CellFactory import CellFactory
from infrastructure.utils.Ids import IdsApi

# 2025.6.12 调试界面
# MTS_LOG_KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/684a2d30794842dcaa574023"
# METRICS_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/684a2d56794842dcaa574025"
METRICS_COMPARE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/67d2eac8e8940b52458d0f8a"
# 2025.7.1 恢复真实界面
MTS_LOG_KPI_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/6537e0f4f3b21d69d436d1ff"
METRICS_TABLE_URL = "https://zxmte.zte.com.cn:8686/#/space/64ee9dca0c0f8d4dab36f25f/page/6697283d2fb6db61a73a7246"

router = APIRouter(route_class=bizRoute)


@router.post("/active", summary="激活一键式采集任务",
             description=description("网管DCM模块下，激活一键式采集任务", "周斌兴10288354"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/onekey", "refPattern": "1-*"}]})
async def active_one_key_task(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    flag, errList = True, []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            flag, err = await LogCollectionService(me).active_one_key_task(info)
            if err:
                errList.append(err)
    if errList:
        return fail(msg=errList)
    return success(data="Active task success")


@router.post("/stop", summary="暂停一键式采集任务",
             description=description("网管DCM模块下，暂停一键式采集任务", "周斌兴10288354"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/onekey", "refPattern": "1-*"}]})
async def stop_one_key_task(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    flag, errList = True, []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            flag, err = await LogCollectionService(me).stop_one_key_task(info)
            if err:
                errList.append(err)
    if errList:
        return fail(msg=errList)
    return success(data="Stop task success")


@router.post("/delete", summary="删除一键式采集任务", description=description(
    "网管DCM模块下，删除一键式采集任务，删除前需先暂停任务", "周斌兴10288354"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/onekey", "refPattern": "1-*"}]})
async def delete_task(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有删除的任务，请先创建!!")
    flag, errList = True, []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            flag, err = await LogCollectionService(me).delete_one_key_task(info)
            if err:
                errList.append(err)
    if errList:
        return fail(msg=errList)
    return success(data="Delete task success")


@router.post("/autoMtsLog", summary="创建MTSLog采集任务",
             description=description("网管DCM模块下，自动创建一键式采集MTSLOG任务", "周斌兴10288354"))
async def create_mts_log_capture_automatic(request: Request):
    mes = await MeFactory.create(request)
    taskInfo, errList = {}, []
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_mts_log_task_automatic(me)
        if taskId:
            taskInfo.update({me.meId: taskId})
        else:
            errList.append(errmsg)
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo})


@router.post("/autoExportSftp", summary="导出MTSLog数据到sftp服务器",
             description=description(
                 "网管DCM模块下，导出MTSLog数据到wxbee，并开始UDACloud解码，使用前确保任务已激活。UCACloud: https://uda.zx.zte.com.cn/#/mia",
                 "周斌兴10288354"),
             openapi_extra={"refs": [{"actionName": "创建MTSLog采集任务"}]})
async def export_one_key_data_mia(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    filesUrl, errList, user = [], [], request.state.resource.get("user", "10288354")
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            fileUrl, errmsg = await AutoDcmTask().download_and_decode_mts_log(me, info, user=user)
            if errmsg:
                errList.append(errmsg)
            else:
                filesUrl.append(fileUrl)
    if errList:
        return fail(data={"filePathList": filesUrl}, msg=errList)
    return success(data={"filePathList": filesUrl})


@router.post("/saveMtsData", summary="MTSLog数据解析入库",
             description=description("网管DCM模块下，导出一键式采集任务数据，MTSLog数据解析入库", '夏晨10242158'),
             openapi_extra={"refs": [{"actionName": "导出MTSLog数据到sftp服务器"}],
                            "urls": [{"name": "MTS-表格-曲线", "url": MTS_LOG_KPI_TABLE_URL}]})
async def save_realtime_kpi_task_perf_data(request: Request, fileListWithTime: FileListWithTimeRange):
    refs_path = await Refs(request).get_output("filePathList")
    Logger.info("MTSLog数据解析入库filePathList:{0}".format(refs_path))
    if not refs_path:
        return fail(msg="!!获取不到依赖action路径文件，filePathList is empty!!")
    attachment = dict(fileListWithTime)
    attachment.update({"filePathList": refs_path[0]})

    def transform_perf_para():
        resource = request.state.resource
        taskId = resource.get("taskId")
        taskName = resource.get("taskName")
        caseId = resource.get("systemId")
        if caseId:
            return {"id": taskId, "name": taskName, "taskAttachment": [],
                    "cases": [{"id": caseId, "system_id": caseId, "caseAttachment": [attachment]}]}
        return {"id": taskId, "name": taskName, "cases": [], "taskAttachment": [attachment]}

    try:
        processor = PerformanceIndexService().handle_perf_request(transform_perf_para())
        files = Artifact.get_file_urls(refs_path[0])
        links = IdsApi().ids_get_hyperlink_and_send_email(request, files, [MTS_LOG_KPI_TABLE_URL],
                                                          processor.ids_links, ["MTSLog数据分析"])
        return success(msg=f"MTSLog数据解析入库成功, 点击 {links} 跳转IDS")
    except Exception as err:
        Logger.error(traceback.format_exc())
        return fail(msg=f"MTSLog数据解析入库失败,{err}")


@router.post("/autoGetHistoryMetricsLog", summary="获取Metrics历史采集数据",
             description=description("网管DCM模块下,获取历史Metrics采集数据,并开始UDACloud解码,返回解码后数据(使用前确保任务已激活),"
                                     "可在mia系统查看任务详情UCACloud: https://uda.zx.zte.com.cn/#/mia", "侯小飞10270755"))
async def get_history_metrics_log_automatic(request: Request, hmc: HistoryMetricsCollection):
    mes = await MeFactory.create(request)
    user = request.state.resource.get("user", "10288354")
    filePathList, errList= [], []
    decodeScenes = [{"area": area.upper(), "scenes": scenes} for area, scenes in hmc.decodeScenes.items()]
    parameters = {"decodeScenes": decodeScenes, "taskType": hmc.taskType}
    logItems = "&".join([x.value for x in hmc.logItems])
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_history_metrics_task_automatic(me, logItems)
        if taskId:
            sftpurl, localpath, errmsg = await AutoDcmTask().export_metrics_log_data_to_sftp(
                me, taskId, hmc.start_time, hmc.end_time, hmc.collection_time, "log", parameters, user, logItems)
            if errmsg:
                errList.append(errmsg)
            if sftpurl:
                filePathList.append(sftpurl)
        else:
            errList.append(errmsg)
    data = {"filePathList": filePathList}
    if errList:
        return fail(msg=f"获取Metrics历史采集数据失败,原因:{errList}")
    return success(msg="获取Metrics历史采集数据成功", data=data)


@router.post("/createRealTimeInnerMetricsTask",
             summary="创建实时InnerMetrics任务",
             description=description("网管DCM模块下，创建一键式实时采集InnerMetrics任务", "李双红10258068"))
async def create_realtime_metrics_log_automatic(request: Request):
    mes = await MeFactory.create(request)
    taskInfo, errList = {}, []
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_metrics_log_task_automatic(me)
        if taskId:
            taskInfo.update({me.meId: taskId})
        else:
            errList.append(errmsg)
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo}, msg="创建成功")


@router.post("/exportRealTimeInnerMetricsData", summary="导出实时InnerMetrics数据到sftp服务器", description=description(
    "网管DCM模块下,导出实时InnerMetrics数据,并开始UDACloud解码,返回解码后数据(使用前确保任务已激活),"
    "可在mia系统查看任务详情UCACloud: https://uda.zx.zte.com.cn/#/mia", "李双红10258068;侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "创建实时InnerMetrics任务", "refPattern": "1-*"}]})
async def export_realtime_metrics_to_sftp(request: Request, paras: MetricsDecode):
    taskInfo = await Refs(request).get_output("taskInfo")
    filePathList, errList = [], []
    user = request.state.resource.get("user", "10288354")
    decodeScenes = [{"area": area.upper(), "scenes": scenes} for area, scenes in paras.decodeScenes.items()]
    parameters = {"decodeScenes": decodeScenes, "taskType": paras.taskType}
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            sftpurl, localpath, errmsg = await AutoDcmTask().export_metrics_log_data_to_sftp(me, info, parameters=parameters, user=user)
            if errmsg:
                errList.append(errmsg)
            if sftpurl:
                filePathList.append(sftpurl)
    data = {"filePathList": filePathList}
    if errList:
        return fail(msg=f"导出实时InnerMetrics数据到sftp服务器失败,原因:{errList}")
    return success(msg="导出实时InnerMetrics数据到sftp服务器成功", data=data)


@router.post("/save_metrics_data", summary="METRICS数据解析入库",
             description=description("导出的METRICS数据解析入库IDS", '侯小飞10270755'),
             openapi_extra={"refs": [{"actionName": "导出实时InnerMetrics数据到sftp服务器|获取Metrics历史采集数据"}],
                            "urls": [{"name": "METRICS-表格-曲线", "url": METRICS_TABLE_URL}]})
async def save_metrics_data(request: Request, fileListWithTime: FileListWithTimeRange):
    refs_path = await Refs(request).get_output("filePathList")
    Logger.info("METRICS数据解析入库filePathList:{0}".format(refs_path))
    if not refs_path:
        return fail(msg="!!获取不到依赖action路径文件,filePathList是空的,请确认依赖action产物是否正确!!")
    attachment = dict(fileListWithTime)
    attachment.update({"filePathList": refs_path[0]})

    def transform_perf_para():
        resource = request.state.resource
        taskId = resource.get("taskId")
        taskName = resource.get("taskName")
        caseId = resource.get("systemId")
        if caseId:
            return {"id": taskId, "name": taskName, "taskAttachment": [],
                    "cases": [{"id": caseId, "system_id": caseId, "caseAttachment": [attachment]}]}
        return {"id": taskId, "name": taskName, "cases": [], "taskAttachment": [attachment]}

    try:
        processor = PerformanceIndexService().handle_perf_request(transform_perf_para())
        files = Artifact.get_file_urls(refs_path[0])
        links = IdsApi().ids_get_hyperlink_and_send_email(request, files, [METRICS_TABLE_URL], processor.ids_links,
                                    ["METRICS数据分析"])
        return success(msg=f"METRICS数据解析入库成功, 点击 {links} 跳转IDS")
    except Exception as err:
        logging.error(traceback.format_exc())
        return fail(msg=f"METRICS数据解析入库失败,{err}")

@router.post("/UDALog", summary="创建UDALog采集任务",
             description=description("网管DCM模块下，自动创建UDALOG采集任务", "10263798"))
async def create_uda_log_capture_automatic(request: Request, udaLogCapture: UdaLogCapture):
    mes = await MeFactory.create(request)
    taskInfo, errList = {}, []
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_uda_log_task(me, udaLogCapture)
        if taskId:
            taskInfo.update({me.meId: taskId})
        else:
            errList.append(errmsg)
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo})


@router.post("/modify", summary="修改UDALog采集任务",
             description=description("网管DCM模块下，修改UDALOG采集任务", "杨先恒10255283"),
             openapi_extra={"refs": [{"actionName": "创建UDALog采集任务", "path": "/v1/api/action/ume/dcm/onekey"}]})
async def modify_one_key_task(request: Request, udaLogCapture: UdaLogCapture):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有删除的任务，请先创建!!")
    errList = []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            msg = await AutoDcmTask().modify_uda_log_task(me, info, udaLogCapture)
            if msg != 200:
                errList.append(msg)
    if not errList:
        return fail(msg=errList)
    return success(data=" modify task success")


@router.post("/checkUDAlog", summary="检查UDA抓数状态",
             description=description("网管DCM模块下，检查UDAlog抓数状态,针对UDAlog中需要确认是否抓数完成的任务",
                                     "10263798"),
             openapi_extra={"refs": [{"actionName": "创建UDALog采集任务"}]})
async def check_uda_log_task(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    flag, errList = True, []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            flag, err = await AutoDcmTask().check_uda_log_task_state(me, info)
            errList.append(err)
    if not flag:
        return fail(msg=errList)
    return success(data="抓数任务已完成！！！")


@router.post("/exportUpaLogSftp", summary="导出UDAlog数据到sftp服务器",
             description=description(
                 "网管DCM模块下，导出UDALog数据到sftp服务器，使用前确保任务是采集完成或者暂停状态",
                 "杨先恒10255283"),
             openapi_extra={"refs": [{"actionName": "创建UDALog采集任务"}]})
async def export_udalog_to_sftp(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    if not taskInfo:
        return fail(msg="!!没有要导出文件的任务，请先创建!!")
    filesPath, downloadErrList = [], []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            mes = await MeFactory.create_by_meid(request, meId)
            downloadErrmsg = await AutoDcmTask().export_uda_log_data_to_sftp(mes, info)
            if downloadErrmsg:
                downloadErrList.append(downloadErrmsg)
    if downloadErrList:
        return fail(msg=f"下载UDALOG数据发生错误：{downloadErrList}")
    return success(data="download success")


@router.post("/createAutoLogTask", summary="创建自动化Log采集任务",
             description=description(
                 "网管DCM模块下，自动创建自动化Log采集任务,对于小区级和单板级需要依赖'查询MO节点属性'传入小区(NRCellDU/NRCellCU)、单板(ReplaceableUnit)ldn属性",
                 "10263798"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_auto_log_capture_automatic(request: Request, paras: CreateAutoLogCapture):
    mes, newQueryRlts = [], {}
    queryRlts = await Refs(request).get_output()
    Logger.debug('依赖的action输出:', queryRlts)
    if queryRlts:
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            for meId in queryRltDict:
                newQueryRlts.setdefault(meId, []).extend(queryRltDict[meId])
        mes = [await MeFactory().create_by_meid(request, meId) for meId in newQueryRlts]
        cells = []
        Logger.info('依赖转换出数据:', newQueryRlts)
    else:
        mes = await MeFactory.create(request)
        cells = await CellFactory.create(request)
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    taskInfo, errList = {}, []
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_auto_log_task_automatic(me, cells, paras, newQueryRlts)
        if taskId:
            taskInfo.update({me.meId: taskId})
        else:
            errList.append(errmsg)
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo, "logItemIds": ["AutoLog"]})


@router.post("/createLogCollection", summary="创建一键式日志采集任务",
             description=description(
                 "网管DCM模块下，自动创建一键式日志采集任务,对于小区级和单板级需要依赖'查询MO节点属性'传入小区(NRCellDU/NRCellCU)、单板(ReplaceableUnit)ldn属性",
                 "杨先恒10255283"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_Log_Collection(request: Request, paras: LogCapture):
    mes, newQueryRlts = [], {}
    queryRlts = await Refs(request).get_output()
    Logger.debug('依赖的action输出:', queryRlts)
    if queryRlts:
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            for meId in queryRltDict:
                newQueryRlts.setdefault(meId, []).extend(queryRltDict[meId])
        mes = [await MeFactory().create_by_meid(request, meId) for meId in newQueryRlts]
        cells = []
        Logger.info('依赖转换出数据:', newQueryRlts)
    else:
        mes = await MeFactory.create(request)
        cells = await CellFactory.create(request)
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    taskInfo, errList = {}, []
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_log_download_task(me, cells, paras, newQueryRlts)
        if taskId:
            taskInfo.update({me.meId: taskId})
        else:
            errList.append(errmsg)
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo, "logItemIds": paras.logItemId})


@router.post("/ExportLogSftp", summary="导出一键式日志采集任务",
             description=description(
                 "网管DCM模块下，导出一键式日志采集任务Log数据到sftp服务器，并上传到WXBee解码，使用前确保任务已激活",
                 "10255283"),
             openapi_extra={"refs": [
                 {"actionName": "创建一键式日志采集任务|创建自动化Log采集任务|创建自定义跟踪项一键式采集任务|创建场景化一键式采集任务"}]})
async def export_one_key_log(request: Request):
    taskInfo = await Refs(request).get_output("taskInfo")
    logItemIds = await Refs(request).get_output("logItemIds")
    filesUrl, errList = [], []
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            if not logItemIds[0]:
                queryResult = json.loads((await me.run_cmd("queryLogCollectInfo", {"taskId": info})).text)
                logItemIds = [[result.get("logitemid") for result in queryResult]]
            for logItemId in logItemIds[0]:
                fileUrl, errmsg = await AutoDcmTask().export_log_data_to_sftp(me, info, logItemId)
                if errmsg:
                    errList.append(errmsg)
                else:
                    filesUrl.append(fileUrl)

    if errList:
        return fail(data={"filePathList": filesUrl}, msg=errList)
    return success(data={"filePathList": filesUrl})


@router.post("/create_custom_collection_task", summary="创建自定义跟踪项一键式采集任务",
             description=description("网管DCM模块下，创建自定义跟踪项一键式采集任务（一键采集任务通用接口，用户自行抓取网管交互logItems属性），"
                                     "对于小区级和单板级需要依赖'查询MO节点属性'传入小区(NRCellDU/NRCellCU)、单板(ReplaceableUnit)ldn属性",
                                     "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_custom_collection_task(request: Request, paras: CustomLogCapture):
    mes, cells, newQueryRlts = [], [], {}
    queryRlts = await Refs(request).get_output()
    Logger.debug('依赖的action输出:', queryRlts)
    if queryRlts:
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            for meId in queryRltDict:
                newQueryRlts.setdefault(meId, []).extend(queryRltDict[meId])
        mes = [await MeFactory().create_by_meid(request, meId) for meId in newQueryRlts]
        Logger.info('依赖转换出数据:', newQueryRlts)
    else:
        mes = await MeFactory.create(request)
        cells = await CellFactory.create(request)
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds or me.meType in ["MEC"], mes))
    if len(set([me.meType for me in mes])) > 1:
        return fail(msg=f"本action不支持同时选择{set([me.meType for me in mes])}类型资源，请通过资源过滤出单一类型网元资源!")
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    taskInfo, errList = {}, []
    logItemId = [item.get("logitemid") for item in paras.logItems]
    if not logItemId: return fail(msg="logItems不能为空!!!")
    if not isinstance(paras.logItems, list): return fail(msg="logItems只能是列表!!!")
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_custom_collection_task(me, cells, paras, newQueryRlts)
        if taskId:
            taskInfo.update({me.meId: taskId})
        else:
            errList.append(f"{me.meId}创建失败:{errmsg}")
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo, "logItemIds": logItemId})


@router.post("/query", summary="查询一键式采集任务信息",
             description=description("网管DCM模块下，查询一键式采集任务信息", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/onekey", "refPattern": "1-*"}]})
async def query(request: Request, paras: QueryLogCapture):
    taskInfo = await Refs(request).get_output("taskInfo")
    ret, succflag = {}, True
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            try:
                if result := await LogCollectionService(me).get_multi_logitem_collect_infos(info, paras.queryItems):
                    ret.setdefault(meId, {}).update(result)
                else:
                    succflag = False
                    Logger.error(f"{meId}任务'{info}'不存在")
                    ret.setdefault(meId, {}).update({info: f"任务不存在"})
            except BaseException as err:
                succflag = False
                Logger.error(f"{meId}查询运行异常:", traceback.format_exc())
                ret.setdefault(meId, {}).update({info: f"{err}"})
    if succflag and ret:
        return success(msg=f"查询一键式采集任务信息成功", data=ret)
    return fail(msg=f"查询一键式采集任务信息失败", data=ret)

@router.post("/create_scenes_collection_task", summary="创建场景化一键式采集任务",
             description=description("网管DCM模块下，创建场景化一键式采集任务，场景模版需要用户输入到collectionScenes参数里",
                                     "对于小区级和单板级需要依赖'查询MO节点属性'传入小区(NRCellDU/NRCellCU)、单板(ReplaceableUnit)ldn属性",
                                     "00256242"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_scenes_collection_task(request: Request, paras: ScenesLogCapture):
    mes, cells, newQueryRlts = [], [], {}
    queryRlts = await Refs(request).get_output()
    Logger.debug('依赖的action输出:', queryRlts)
    if queryRlts:
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            for meId in queryRltDict:
                newQueryRlts.setdefault(meId, []).extend(queryRltDict[meId])
        mes = [await MeFactory().create_by_meid(request, meId) for meId in newQueryRlts]
        Logger.info('依赖转换出数据:', newQueryRlts)
    else:
        mes = await MeFactory.create(request)
        cells = await CellFactory.create(request)
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds or me.meType in ["MEC"], mes))
    if len(set([me.meType for me in mes])) > 1:
        return fail(msg=f"本action不支持同时选择{set([me.meType for me in mes])}类型资源，请通过资源过滤出单一类型网元资源!")
    if not mes: return fail(msg="环境信息无网元，请检查环境信息或资源过滤条件！")
    taskInfo, errList = {}, []
    for me in mes:
        taskId, errmsg = await AutoDcmTask().create_custom_collection_task(me, cells, paras, newQueryRlts)
        if taskId:
            taskInfo.update({me.meId: taskId})
        else:
            errList.append(f"{me.meId}创建失败:{errmsg}")
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    # return success(data={"taskInfo": taskInfo, "logItemIds": logItemId})
    return success(data={"taskInfo": taskInfo})