from datetime import datetime
from enum import Enum
from typing import TypeVar, Optional, Literal
from infrastructure.logger import Logger

from pydantic import BaseModel, Field, validator
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")


class traceTypeEnum(str, Enum):
    neTrace = "基站级"
    cellTrace = "小区级"


class reportPeriodEnum(str, Enum):
    onePeriod = "1024ms"
    twoPeriod = "2048ms"
    thrPeriod = "5120ms"
    fourPeriod = "10240ms"
    fivePeriod = "20480ms"
    sixPeriod = "40960ms"
    sevenPeriod = "1min"
    eightPeriod = "6min"


class includeLocationInfoEnum(str, Enum):
    yes = 'Yes'
    no = 'No'


includeLocationInfo = {'Yes': '1', 'No': '0'}
refSignalType = {'SSB': '0', 'CSI': '1', 'SSBCSI': '3'}
ueNumRestrictedSwitch = {'限制': '0', '不限制': '1', '小区配置': '2'}


class refSignalTypeEnum(str, Enum):
    SSB = 'SSB'
    CSI = 'CSI'
    SSBCSI = 'SSB+CSI'


class ueNumRestrictedSwitchEnum(str, Enum):
    c1 = '限制'
    c2 = '不限制'
    c3 = '小区配置'


class serviceTypeEnum(str, Enum):
    c1 = "nr"


class taskTypeEnum(str, Enum):
    c1 = "MR"
    c2 = "MDT"

EVENT_TEMPLATE = ["DU_CELL_INFO_REPORT"]

MR_PARAMETERS_TEMPLATE = {
    "上报周期": "5120ms",
    "是否上报位置信息": 'No',
    "参考信号类型": "SSB",
    "UE选择因子": "1",
    "是否限制最大用户数": "小区配置",
    "每小区最大的MR用户个数": "20"
}

MDT_PARAMETERS_TEMPLATE = {
    "M1 M6上报间隔": "5120ms",
    "M1测量类型": "同频",
    "M1 定位方法": "None",
    "支持GNSS定位功能的终端比率": "0%",
    "M1 触发机制": "周期",
    "M1 门限类型": "RSRP",
    "M1 RSRP/RSRQ/SINR 阈值": "127",
    "M1 上报次数": "Infinity",
    "终端是否主动上报位置": "否",
    "M2采集周期": "5120ms",
    "M4采集周期": "5120ms",
    "M5采集周期": "5120ms",
    "M7采集周期(min)": "1",
    "本地蓝牙名": "null",
    "无线局域网 SSID": "null",
    "Logged采集持续时间": "10min",
    "Logged采集间隔": "5120ms",
    "Logged 触发机制": "outOfCoverage",
    "L1 门限类型": "RSRP",
    "L1 RSRP/RSRQ/SINR阈值": "51",
    "L1 事件测量判决迟滞": "3",
    "L1 事件发生到上报的时间差": "320ms",
    "区域范围类型": "无",
    "小区CGI": "null",
    "跟踪区编码": "null",
    "跟踪区标识": "null",
    "扩展测量周期": "5120ms"
}

SC_PARAMETERS_TEMPLATE = {
    "上报周期": "5120ms",
    "X2信令跟踪类型": "触发端上报",
    "XN信令跟踪类型": "触发端上报",
    "F1信令跟踪类型": "CUCP上报",
    "信令软采UU Paging开关": "关闭",
    "软采上报规范类型": "自适应规范",
    "Plmn 过滤": "[]",
    "SC ToB用户切片(SST-SD)": "None",
    "SC 5QI配置": "None",
    "每小区最大ToB用户数": "100"
}

VONRMos_PARAMETERS_TEMPLATE = {
    "上报周期": "5120ms"
}

gNBTrace_PARAMETERS_TEMPLATE = {
    "上报周期": "5120ms",
    "跟踪深度": "最大",
    "小区业务跟踪开关": "关闭",
    "UE个数限制开关": "不限制",
    "单小区UE选择个数": "400"
}

VideoTrace_PARAMETERS_TEMPLATE = {
    "上报周期": "10s",
}

Empty_PARAMETERS_TEMPLATE = {}


EventSubTaskParametersKeysMap = {
    "上报周期": "MR_reportIntervalFilter", "是否上报位置信息": "IncludeLocationInfo", "参考信号类型": "MR_rsType", "UE选择因子": "MR_ueSelectionFactor",
    "是否限制最大用户数": "MR_ueNumRestrictedSwitch", "每小区最大的MR用户个数": "MR_cellMaxUeNum", "跟踪深度": "traceDepthFilter", "小区业务跟踪开关": "cellTrafficTraceSwitchFilter",
    "UE个数限制开关": "gNBTrace_ueNumRestrictedSwitch", "单小区UE选择个数": "gNBTrace_cellMaxUeNum","M1 M6上报间隔": "M1_reportIntervalFilter", "M1测量类型": "M1_MeasType",
    "M1 定位方法": "M1_PositionMethod", "支持GNSS定位功能的终端比率": "GnssSupportedUeRatio", "M1 触发机制": "M1_ReportTrigger", "M1 门限类型": "M1_ThresholdType",
    "M1 RSRP/RSRQ/SINR 阈值": "M1_ThresholdValue", "M1 上报次数": "M1_ReportAmount", "终端是否主动上报位置": "obtainLocationUeRatio", "M2采集周期": "M2_reportIntervalFilter",
    "M4采集周期": "M4_reportIntervalFilter", "M5采集周期": "M5_reportIntervalFilter", "M7采集周期(min)": "M7_reportIntervalFilter", "本地蓝牙名": "BT_MeasConfigNameList",
    "无线局域网 SSID": "WLAN_MeasConfigNameList", "Logged采集持续时间": "Logged_LoggingDuration", "Logged采集间隔": "Logged_LoggingInterval", "Logged 触发机制": "Logged_ReportTrigger",
    "L1 门限类型": "L1_ThresholdType", "L1 RSRP/RSRQ/SINR阈值": "L1_ThresholdValue", "L1 事件测量判决迟滞": "L1_Hysteresis", "L1 事件发生到上报的时间差": "L1_TimeToTrigger",
    "区域范围类型": "AreaConfigType", "小区CGI": "CellGlobalIdList", "跟踪区编码": "TrackingAreaCodeList", "跟踪区标识": "TrackingAreaIdentityList", "扩展测量周期": "Ext_MEASReportIntervalFilter",
    "X2信令跟踪类型": "SC_SignalX2TraceType", "XN信令跟踪类型": "SC_SignalXnTraceType", "F1信令跟踪类型": "SC_SignalF1TraceType", "信令软采UU Paging开关": "SC_SignalUUPagingSwitch",
    "软采上报规范类型": "SC_SpecificationType", "Plmn 过滤": "Task_Plmn_Filter", "SC ToB用户切片(SST-SD)": "ToB_NSSAI", "SC 5QI配置": "SC_5QI_Config", "每小区最大ToB用户数":  "SC_cellMaxToBUeNum",
    "TraceIDs(HEX)": "gNBTrace_TraceId"
}

EventSubTaskParametersValuesMap = {
    "否": "0", "SSB": "1", "CSI": "2", "SSB+CSI": "3", "限制": "0", "不限制": "1", "小区配置": "2", "同频": "0", "同频+异频": "1", "同频+异系统": "2", "同频+异频+异系统": "3",
    "None": "0", "GNSS": "1", "0%": "0", "100%": "1", "周期": "0", "A2事件": "1", "A2事件周期": "2", "outOfCoverage": "1", "eventL1": "2", "RSRP": "0", "RSRQ": "1", "SINR": "2",
    "无": "0", "小区": "1", "TAC": "2", "TAI": "3", "最小": "0", "中等": "1", "最大": "2", "关闭": "0", "打开": "1", "触发端上报": "0", "被触发端上报": "1", "全上报": "2",
    "CUCP上报": "0", "DU上报": "1", "开启": "1", "自适应规范": "0", "非共享": "1", "共享": "2"
}

class CreateEventSubTaskSchema(BaseModel):
    '''
    schema 联动 根据不同的依赖关系 开发 支持不同的模版，以 支持 规范的生成对应的jsonschema ,供 前后端 规范通信！
    目前已支持的模版及使用说明文档地址如下：若模版有相似的依赖关系 用户可直接复用对应的类方法 去封装模版；若没有，需要继续开发 增添对应模版；
     https://i.zte.com.cn/#/space/67aa26ca3aef43f4b734f3f627989ab7/wiki/page/49b621f246304e41a3f6a114194dd615/view
    '''
    traceType: Literal["基站级", "小区级"] = Field(default="基站级")
    plmnList: str | None = Field(None, description="按PLMN采集,默认为空,多个plmn用逗号,只有'基站级'才生效','连接", regex=r"^$|[\d\-,]?")
    serviceType: Literal["nr"] = Field(default="nr")
    taskType: Literal["CDT", "MR", "MDT", "SC", "VONRMos", "gNBTrace", "AtmosphericSeq", "VideoTrace", "mts-V", "ISP"] = Field(default="MR")
    eventList: DataT = Field(default=EVENT_TEMPLATE, type="jsonEditor",
                             description="按照标准格式填写 任务Events,例：['DU_CELL_INFO_REPORT'], 默认选则cu,"
                                         "du小区时，DU_CELL_INFO_REPORT为必填")
    parameters: DataT = Field(default=MR_PARAMETERS_TEMPLATE, type="jsonEditor",
                              description="按照标准格式填写 任务参数")
    outPutType: str = Field("NDS-DCM", description="第三方NDS---传输索引：对应网管配置，UDTTC的moId,如：NDS-DCM")
    startTime: Optional[datetime]
    endTime: Optional[datetime]


    @validator('parameters')
    def parameters_analysis(cls, parameters, values):
        ret = {}
        map = {"不限制": "0", "限制": "1"}
        for key, value in parameters.items():
            if key == "UE个数限制开关":
                ret.update({EventSubTaskParametersKeysMap.get(key): map.get(value)})
            elif values["taskType"] == "VONRMos" and key == "上报周期":
                ret.update({"VONRMos_reportIntervalFilter": value})
            else:
                ret.update({EventSubTaskParametersKeysMap.get(key, key): EventSubTaskParametersValuesMap.get(value, value)})
        return ret

    @classmethod
    def schema(cls):
        dependency_dict = {
            "taskType": {
                "MR": [
                    {"parameters": MR_PARAMETERS_TEMPLATE}
                ],
                "MDT": [
                    {"parameters": MDT_PARAMETERS_TEMPLATE}
                ],
                "SC": [
                    {"parameters": SC_PARAMETERS_TEMPLATE}
                ],
                "VONRMos": [
                    {"parameters": VONRMos_PARAMETERS_TEMPLATE}
                ],
                "gNBTrace": [
                    {"parameters": gNBTrace_PARAMETERS_TEMPLATE}
                ],
                "VideoTrace": [
                    {"parameters": VideoTrace_PARAMETERS_TEMPLATE}
                ],
                "AtmosphericSeq": [
                    {"parameters": Empty_PARAMETERS_TEMPLATE}
                ],
                "CDT": [
                    {"parameters": Empty_PARAMETERS_TEMPLATE}
                ],
                "mts-V": [
                    {"parameters": Empty_PARAMETERS_TEMPLATE}
                ],
                "ISP": [
                    {"parameters": Empty_PARAMETERS_TEMPLATE}
                ]
            }
        }
        return MySchema().generate_basic_notime_schema(super().schema(), dependency_dict)


CreateEventSubTaskSchema.schema()

class CreateGlobalEventSubTaskSchema(BaseModel):
    traceType: Literal["全网", "子网", "分组", "UE"] = Field(default="全网")
    advancedResInfos: str = Field(None, description="当traceType不是'全网'时此处需要填写子网ID或网元分组名或16进制的TraceIDs,例如填写16016,16010或12345d,12345a,默认此处为空")
    serviceType: Literal["nr"] = Field(default="nr")
    taskType: Literal["CDT", "MR", "MDT", "gNBTrace", "ISP", "mts-V"] = Field(default="MR", description="当traceType为UE时此处只能选择mts-V")
    eventList: DataT = Field(default=EVENT_TEMPLATE, type="jsonEditor",
                             description="按照标准格式填写 任务Events,例：['CELL_MAC_CARRIER_INFO'],DU_CELL_INFO_REPORT为必填")
    parameters: DataT = Field(default=MR_PARAMETERS_TEMPLATE, type="jsonEditor",
                              description="按照标准格式填写 任务参数")
    outPutType: str = Field("NDS-DCM", description="第三方NDS---传输索引：对应网管配置，UDTTC的moId,如：NDS-DCM")
    startTime: Optional[datetime]
    endTime: Optional[datetime]

    @validator('parameters')
    def parameters_analysis(cls, parameters):
        ret = {}
        map = {"不限制": "0", "限制": "1"}
        if not parameters:
            return {}
        for key, value in parameters.items():
            if key == "UE个数限制开关":
                ret.update({EventSubTaskParametersKeysMap.get(key): map.get(value)})
            else:
                ret.update({EventSubTaskParametersKeysMap.get(key, key): EventSubTaskParametersValuesMap.get(value, value)})
        return ret

    @classmethod
    def schema(cls):
        dependency_dict = {
            "taskType": {
                "MR": [
                    {"parameters": MR_PARAMETERS_TEMPLATE}
                ],
                "MDT": [
                    {"parameters": MDT_PARAMETERS_TEMPLATE}
                ],
                "gNBTrace": [
                    {"parameters": gNBTrace_PARAMETERS_TEMPLATE}
                ]
            }
        }
        return MySchema().generate_basic_notime_schema(super().schema(), dependency_dict)
CreateGlobalEventSubTaskSchema.schema()


QueryItemMap = {"任务同步状态": "synStatus", "同步详情": "synStatusDetail", "网元任务状态": "taskStatus", "任务详情": "taskStatusDetail"}
class QueryItemEnum(str, Enum):
    synStatus = "任务同步状态"
    synStatusDetail = "同步详情"
    taskStatus = "网元任务状态"
    taskStatusDetail = "任务详情"

class QueryEvent(BaseModel):
    queryItems: list[QueryItemEnum] = Field([], description='需要查询信息,默认不勾选,表示返回所有信息')
    @validator("queryItems",)
    def invert_item(cls, v):
        return [QueryItemMap.get(item) for item in v]