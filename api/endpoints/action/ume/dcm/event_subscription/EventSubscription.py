
import traceback
import pytz
import time
import datetime

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.dcm.event_subscription.Schemas import traceTypeEnum, \
    CreateEventSubTaskSchema, CreateGlobalEventSubTaskSchema, QueryEvent
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.factories.CellFactory import CellFactory
from infrastructure.logger import Logger
from infrastructure.utils.TimeHandler import get_timestamp, format_to_zero_time, format_startEndTime
from infrastructure.resource.service.Refs import Refs
from domain.models.ume.dcm.Dcm import Dcm
from domain.platform.ActionInfo import description

router = APIRouter(route_class=bizRoute)


def _generate_default_time_strategy():
    startTime = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.000Z")
    endTime = (datetime.datetime.now() + datetime.timedelta(hours=24)).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    return startTime, endTime


def _set_datetime(startTime, endTime):
    if startTime and endTime:
        newStartTime = (startTime.astimezone(pytz.utc) + datetime.timedelta(hours=8)).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        newEndTime = (endTime.astimezone(pytz.utc) + datetime.timedelta(hours=8)).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    else:
        newStartTime, newEndTime = _generate_default_time_strategy()
    return newStartTime, newEndTime

async def create_ne_trace(request, para, startTime, endTime):
    mes = await MeFactory.create(request)
    taskInfo = {}
    errInfo = {}
    for me in mes:

        taskName = "Hyper_Automation_" + me.meId + '_' + str(int(time.time()))
        paras = {"taskName": taskName, "serviceType": para.serviceType,
                 "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                 "traceType": "neTrace", "taskType": para.taskType, "outPutType": para.outPutType,
                 "parameters": para.parameters}
        if para.plmnList:
            paras.update({"plmnList": para.plmnList.split(",")})
        Logger.debug(paras)
        subscriptionId, message = await Dcm(me).create_event_subscription_task(paras)
        if not subscriptionId:
            errInfo.update({me.meId: message})
        taskInfo.update({me.meId: {"subscriptionId": subscriptionId, 'taskName': taskName, 'message': message}})
    return taskInfo, errInfo

async def create_global_subscription(request, para, startTime, endTime):
    mes = await MeFactory.create(request)
    taskInfo = {}
    errInfo = {}
    me = mes[0]
    taskName = "Hyper_Automation_" + me.meId + '_' + str(int(time.time()))
    paras = {"taskName": taskName, "serviceType": para.serviceType, "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
             "taskType": para.taskType, "outPutType": para.outPutType,  "dataViewMode": "3", "parameters": {}, "advancedResInfos": [],
             "isNetWork": True}
    if para.traceType != "UE":
        paras.update({"traceType": "netrace"})
        advancedResInfos = []
        if para.advancedResInfos:
            for advancedResInfo in para.advancedResInfos.split(','):
                advancedResInfos.append({"nbiId": advancedResInfo, "name": advancedResInfo.strip()})
            paras.update({"scope": "1"})
        paras.update({"advancedResInfos": advancedResInfos, "parameters": para.parameters})
    elif para.traceType == "UE":
        advancedResInfos = para.advancedResInfos.split(",")
        paras.update({"traceType": "traceidtrace", "scope": "3", "traceIdList": advancedResInfos})
    Logger.debug(paras)
    subscriptionId, message = await Dcm(me).create_network_event_sub_task(paras)
    if not subscriptionId:
        errInfo.update({me.meId: message})
    taskInfo.update({me.meId: {"subscriptionId": subscriptionId, 'taskName': taskName, 'message': message}})
    return taskInfo, errInfo

async def create_cell_trace(request, para, startTime, endTime):
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    Logger.debug(cells)

    cellTypeMap = {}
    taskInfo = {}
    errInfo = {}
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    for me in mes:
        cellTypeInfoMap = {}
        taskName = "Hyper_Automation_" + me.meId + '_' + str(int(time.time()))
        for cell in cells:
            if cell.me.meId == me.meId:
                Logger.debug(cell.me.meId)
                Logger.debug(me.meId)
                cellTypeInfoMap[cell.cellId] = ["nrcellcu", "nrcelldu"]
                Logger.debug(cellTypeInfoMap)
                cellTypeMap.update({cell.me.meId: cellTypeInfoMap})
        Logger.debug(cellTypeMap)
        paras = {"taskName": taskName, "serviceType": para.serviceType,
                 "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                 "traceType": "cellTrace", "cellTypeMap": cellTypeMap, "taskType": para.taskType,
                 "outPutType": para.outPutType, "parameters": para.parameters}
        subscriptionId, message = await Dcm(me).create_event_subscription_task(paras)
        if not subscriptionId:
            errInfo.update({me.meId: message})
        taskInfo.update({me.meId: {"subscriptionId": subscriptionId, 'taskName': taskName, 'message': message}})
    return taskInfo, errInfo

async def modify_ne_trace(request, para, startTime, endTime):
    taskInfo = await Refs(request).get_output("taskInfo")
    Logger.debug(taskInfo)
    modifiedTaskInfo = {}
    errInfo = {}
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            taskName = info['taskName']
            paras = {"taskName": taskName, "serviceType": para.serviceType,
                     "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                     "traceType": "neTrace", "taskType": para.taskType, "outPutType": para.outPutType,
                     "parameters": para.parameters}
            if para.plmnList:
                paras.update({"plmnList": para.plmnList.split(",")})
            Logger.debug(paras)
            subscriptionId, message = await Dcm(me).modify_event_subscription_task(info['subscriptionId'], paras)
            if not subscriptionId:
                errInfo.update({me.meId: message})
            modifiedTaskInfo.update({me.meId: {"subscriptionId": subscriptionId, 'taskName': taskName, 'message': message}})
    return modifiedTaskInfo, errInfo

async def modify_cell_trace(request, para, startTime, endTime):
    taskInfo = await Refs(request).get_output("taskInfo")
    Logger.debug(taskInfo)
    modifiedTaskInfo = {}
    cells = await CellFactory.create(request)
    errInfo = {}
    cellTypeMap ={}
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            taskName = info['taskName']
            for cell in cells:
                if cell.me.meId == me.meId:
                    cellTypeMap.setdefault(cell.me.meId, {})
                    cellTypeMap[cell.me.meId][cell.cellId] = ["nrcellcu", "nrcelldu"]
            paras = {"taskName": taskName, "serviceType": para.serviceType,
                     "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                     "traceType": "cellTrace", "cellTypeMap": cellTypeMap, "taskType": "MR",
                     "outPutType": para.outPutType, "parameters": para.parameters}
            subscriptionId, message = await Dcm(me).modify_event_subscription_task(info['subscriptionId'], paras)
            if not subscriptionId:
                errInfo.update({me.meId: message})
            modifiedTaskInfo.update({me.meId: {"subscriptionId": subscriptionId, 'taskName': taskName, 'message': message}})
    return modifiedTaskInfo, errInfo

async def modify_global_event_sub(request, para, startTime, endTime):
    taskInfo = await Refs(request).get_output("taskInfo")
    Logger.debug(taskInfo)
    modifiedTaskInfo = {}
    errInfo = {}
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            taskName = info['taskName']
            paras = {"taskName": taskName, "serviceType": para.serviceType, "eventList": para.eventList, "startTime": startTime, "endTime": endTime,
                     "taskType": para.taskType, "outPutType": para.outPutType,  "dataViewMode": "3", "parameters": {}, "advancedResInfos": [],
                     "isNetWork": True}
            if para.traceType != "UE":
                paras.update({"traceType": "netrace"})
                advancedResInfos = []
                if para.advancedResInfos:
                    for advancedResInfo in para.advancedResInfos.split(','):
                        advancedResInfos.append({"nbiId": advancedResInfo, "name": advancedResInfo.strip()})
                    paras.update({"scope": "1"})
                paras.update({"advancedResInfos": advancedResInfos, "parameters": para.parameters})
            elif para.traceType == "UE":
                advancedResInfos = para.advancedResInfos.split(",")
                paras.update({"traceType": "traceidtrace", "scope": "3", "traceIdList": advancedResInfos})
            subscriptionId, message = await Dcm(me).modify_network_event_subscription_task(info['subscriptionId'], paras)
            if not subscriptionId:
                errInfo.update({me.meId: message})
            modifiedTaskInfo.update({me.meId: {"subscriptionId": subscriptionId, 'taskName': taskName, 'message': message}})
    return modifiedTaskInfo, errInfo

@router.post("/create_event_sub_task", summary="创建事件订阅任务",
             description="【功能】网管DCM模块下，创建事件订阅任务【作者】10263798")
async def create_event_sub_task(request: Request, para: CreateEventSubTaskSchema):
    try:
        taskInfo = {}
        errInfo = {}
        Logger.info(para.parameters)
        newStartTime, newEndTime = _set_datetime(para.startTime, para.endTime)
        if traceTypeEnum.neTrace == para.traceType:
            taskInfo, errInfo = await create_ne_trace(request, para, newStartTime, newEndTime)
        if traceTypeEnum.cellTrace == para.traceType:
            taskInfo, errInfo = await create_cell_trace(request, para, newStartTime, newEndTime)
        if errInfo:
            return fail(data={"errInfo": errInfo}, msg='Task is created failed')
        return success(data={"taskInfo": taskInfo}, msg='Task is created successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/modify_event_sub_task", summary="修改事件订阅任务",
             description="【功能】网管DCM模块下，修改事件订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def modify_event_sub_task(request: Request, para: CreateEventSubTaskSchema):
    try:
        taskInfo = {}
        errInfo = {}
        Logger.info(para.parameters)
        startTime, endTime = _set_datetime(para.startTime, para.endTime)
        if traceTypeEnum.neTrace == para.traceType:
            taskInfo, errInfo = await modify_ne_trace(request, para, startTime, endTime)
        if traceTypeEnum.cellTrace == para.traceType:
            taskInfo, errInfo = await modify_cell_trace(request, para, startTime, endTime)
        if errInfo:
            return fail(data={"errInfo": errInfo}, msg='Task is modified failed')
        return success(data={"taskInfo": taskInfo}, msg='Task is modified successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/cancel_event_sub_task", summary="挂起事件订阅任务",
             description="【功能】网管DCM模块下，挂起事件订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def cancel_event_sub_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        cancelTaskInfo = {}
        errInfo = {}
        for meTask in taskInfo:
            for meId, info in meTask.items():
                me = await MeFactory.create_by_meid(request, meId)
                rlt, message = await Dcm(me).cancel_event_subscription_task(info['subscriptionId'])
                if not rlt:
                    errInfo.update({info['subscriptionId']: message})
                cancelTaskInfo.update({info['subscriptionId']: message})
        if errInfo:
            return fail(data=errInfo, msg='Task suspended failed')
        return success(data=cancelTaskInfo, msg='Task suspended successfully')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/delete_event_sub_task", summary="删除事件订阅任务",
             description="【功能】网管DCM模块下，删除事件订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def delete_event_sub_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        deleteTaskInfo = {}
        errInfo = {}
        for meTask in taskInfo:
            for meId, info in meTask.items():
                me = await MeFactory.create_by_meid(request, meId)
                rlt, message = await Dcm(me).delete_event_subscription_task(info['subscriptionId'])
                if not rlt:
                    errInfo.update({info['subscriptionId']: message})
                deleteTaskInfo.update({info['subscriptionId']: message})
        if errInfo:
            return fail(data=errInfo, msg='Task deleted failed')
        return success(data=deleteTaskInfo, msg='Task deleted successfully')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_event_sub_task", summary="激活事件订阅任务",
             description="【功能】网管DCM模块下，激活事件订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def start_event_sub_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        activeTaskInfo = {}
        errInfo = {}
        for meTask in taskInfo:
            for meId, info in meTask.items():
                me = await MeFactory.create_by_meid(request, meId)
                rlt, message = await Dcm(me).start_event_subscription_task(info['subscriptionId'])
                if not rlt:
                    errInfo.update({info['subscriptionId']: message})
                activeTaskInfo.update({info['subscriptionId']: message})
        if errInfo:
            return fail(data=errInfo, msg='Task activation failed')
        return success(data=activeTaskInfo, msg='Task activation successfully')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/create_global_event_sub_task", summary="创建全网订阅任务",
             description="【功能】网管DCM模块下，创建全网订阅任务【作者】10263798")
async def create_global_event_sub_task(request: Request, para: CreateGlobalEventSubTaskSchema):
    try:
        Logger.info(para.parameters)
        newStartTime, newEndTime = _set_datetime(para.startTime, para.endTime)
        taskInfo, errInfo = await create_global_subscription(request, para, newStartTime, newEndTime)
        if errInfo:
            return fail(data={"errInfo": errInfo}, msg='全网订阅任务创建失败')
        return success(data={"taskInfo": taskInfo}, msg='全网订阅任务创建成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/stop_global_event_sub_task", summary="挂起全网订阅任务",
             description="【功能】网管DCM模块下，挂起全网订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": ".*全网订阅任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def stop_global_event_sub_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        cancelTaskInfo = {}
        errInfo = {}
        for meTask in taskInfo:
            for meId, info in meTask.items():
                me = await MeFactory.create_by_meid(request, meId)
                rlt, message = await Dcm(me).stop_network_event_subscription_task(info['subscriptionId'])
                if not rlt:
                    errInfo.update({info['subscriptionId']: message})
                cancelTaskInfo.update({info['subscriptionId']: message})
        if errInfo:
            return fail(data=errInfo, msg='全网订阅任务挂起失败')
        return success(data=cancelTaskInfo, msg='全网订阅任务挂起成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/delete_global_event_sub_task", summary="删除全网订阅任务",
             description="【功能】网管DCM模块下，删除全网订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": ".*全网订阅任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def delete_event_sub_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        deleteTaskInfo = {}
        errInfo = {}
        for meTask in taskInfo:
            for meId, info in meTask.items():
                me = await MeFactory.create_by_meid(request, meId)
                rlt, message = await Dcm(me).delete_network_event_subscription_task(info['subscriptionId'])
                if not rlt:
                    errInfo.update({info['subscriptionId']: message})
                deleteTaskInfo.update({info['subscriptionId']: message})
        if errInfo:
            return fail(data=errInfo, msg='全网订阅任务删除失败')
        return success(data=deleteTaskInfo, msg='全网订阅任务删除成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_global_event_sub_task", summary="激活全网订阅任务",
             description="【功能】网管DCM模块下，激活全网订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": ".*全网订阅任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def start_global_event_sub_task(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        activeTaskInfo = {}
        errInfo = {}
        for meTask in taskInfo:
            for meId, info in meTask.items():
                me = await MeFactory.create_by_meid(request, meId)
                rlt, message = await Dcm(me).start_network_event_subscription_task(info['subscriptionId'])
                if not rlt:
                    errInfo.update({info['subscriptionId']: message})
                activeTaskInfo.update({info['subscriptionId']: message})
        if errInfo:
            return fail(data=errInfo, msg='全网订阅任务激活失败')
        return success(data=activeTaskInfo, msg='全网订阅任务激活成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/modify_global_event_sub_task", summary="修改全网订阅任务",
             description="【功能】网管DCM模块下，修改全网订阅任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": ".*全网订阅任务", "path": "/v1/api/action/ume/dcm/event_subscription"}]})
async def modify_global_event_sub_task(request: Request, para: CreateGlobalEventSubTaskSchema):
    try:
        Logger.info(para.parameters)
        startTime, endTime = _set_datetime(para.startTime, para.endTime)
        taskInfo, errInfo = await modify_global_event_sub(request, para, startTime, endTime)
        if errInfo:
            return fail(data={"errInfo": errInfo}, msg='全网订阅任务修改失败')
        return success(data={"taskInfo": taskInfo}, msg='全网订阅任务修改成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/query", summary="查询事件订阅任务信息",
             description=description("网管DCM模块下，查询事件订阅任务信息", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/dcm/event_subscription", "refPattern": "1-*"}]})
async def query(request: Request, paras: QueryEvent):
    taskInfo = await Refs(request).get_output("taskInfo")
    ret, succflag = {}, True
    for meTask in taskInfo:
        for meId, info in meTask.items():
            me = await MeFactory.create_by_meid(request, meId)
            try:
                if result := await Dcm(me).get_event_subscription_task_detail(info['subscriptionId'], paras.queryItems):
                    ret.setdefault(meId, {}).update(result)
                else:
                    succflag = False
                    Logger.error(f"{meId}任务'{info}'不存在")
                    ret.setdefault(meId, {}).update({info: f"任务不存在"})
            except BaseException as err:
                succflag = False
                Logger.error(f"{meId}查询运行异常:", traceback.format_exc())
                ret.setdefault(meId, {}).update({info: f"{err}"})
    if succflag and ret:
        return success(msg=f"查询事件订阅任务信息成功", data=ret)
    return fail(msg=f"查询事件订阅任务信息失败", data=ret)