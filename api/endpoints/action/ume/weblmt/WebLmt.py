#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/9/27 15:41
# <AUTHOR> 10255283

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.weblmt.Schemas import TraceMe, CertUpload
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from service.action.ume.weblmt.WeblmtService import WeblmtService

router = APIRouter(route_class=bizRoute)


@router.post("/create", summary="创建traceme任务",
             description=description("在weblmt下的开发者工具-系统信息跟踪创建traceme任务", "10255283"))
async def create_traceme_task(request: Request, paras: TraceMe):
    mes = await MeFactory.create_by_lte(request)
    config = request.state.resource.get("config")
    taskInfo = {}
    err = {}
    dataAreaId = paras.dataAreaId if paras.dataAreaId else config.get("dataAreaId")
    for me in mes:
        taskid, winids, resultflag = await WeblmtService().create_trace_task(me, paras, dataAreaId)
        if resultflag:
            taskInfo.update({me.meId: [taskid, winids]})
        else:
            err.update({me.meId: taskid})
    if err:
        return fail(data=f"网管返回失败原因：{err}")
    return success(data={"taskInfo": taskInfo, "dataAreaId": dataAreaId}, msg="任务创建成功")


@router.post("/delete", summary="删除traceme任务",
             description=description("在weblmt下的开发者工具-系统信息跟踪删除traceme任务", "10255283"),
             openapi_extra={"refs": [{"actionName": "创建traceme任务"}]})
async def delete_traceme_task(request: Request):
    mes = await MeFactory.create_by_lte(request)
    queryInfo = await Refs(request).get_output("taskInfo")
    dataAreaId = await Refs(request).get_output("dataAreaId")
    err = {}
    for me in mes:
        if me.meId == list(queryInfo[0].items())[0][0]:
            taskid = queryInfo[0][me.meId][0]
            for winid in queryInfo[0][me.meId][1]:
                ret = await WeblmtService().remove_trace_task(me, taskid, winid, dataAreaId[0])
                if ret is not True:
                    err.update({me.meId: ret})
    if err:
        return fail(data=f"网管返回失败原因：{err}")
    return success(data="任务删除成功")


@router.post("/login", summary="登录weblmt",
             description=description("通过基站ip登录weblmt", "10255283"))
async def login_weblmt(request: Request):
    mes = await MeFactory.create_by_lte(request)
    err = {}
    info = []
    for me in mes:
        resultflag = await WeblmtService().weblmt_login(me)
        if resultflag:
            err.update({me.meId: resultflag})
        else:
            info.append(me.meId)
    if err:
        return fail(data=f"网管返回失败原因：{err}")
    return success(data=f"{info}", msg="登录成功")


@router.post("/importCert", summary="weblmt证书加载",
             description=description("实现在weblmt上的路径：安全管理--证书加载功能实现，使用前把MO节点captchaSwitch关闭", "10255283"))
async def import_cert(request: Request, paras: CertUpload):
    mes = await MeFactory.create_by_nr(request)
    err = {}
    info = []
    for me in mes:
        resultflag, ret = await WeblmtService().load_cert_file(me, paras)
        if resultflag:
            info.append(me.meId)
        else:
            err.update({me.meId: ret})
    if err:
        return fail(data=f"网管返回失败原因：{err}")
    return success(data=f"{info}", msg="证书加载成功")
