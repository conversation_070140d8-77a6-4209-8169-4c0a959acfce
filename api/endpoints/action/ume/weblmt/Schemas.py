#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/5/07 10:25
# <AUTHOR> 10255283

from typing import TypeVar, Literal
from pydantic import BaseModel, Field

from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar('DataT')


class TraceMe(BaseModel):
    moduleId: str = Field("1900", description="功能模块，网管界面上的id号,用逗号隔开，最多填写4个")
    rackId: int = Field(1, description="架号")
    shelfId: int = Field(1, description="框号")
    slotId: int = Field(1, description="槽位")
    cpuId: int = Field(1, description="CPU")
    productType: Literal["LTE-FDD", "LTE-TDD", "NB-IoT", "GSM", "UMTS"] = Field(default="LTE-FDD")
    dataAreaId: str | None = None


class CertUpload(BaseModel):
    certType: Literal["运营商证书", "设备商证书", "信任链证书", "交叉证书", "证书撤销列表"] = Field(
        default="运营商证书")
    moId: str = Field("1", description="请先创建对应的MO对象")
    caFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="0.03",
                          allowedFileType="*.cer;*.p7b", limit=1, params=[])
    pfxFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="0.016",
                           allowedFileType=".pfx", limit=1, params=[])
    p7bFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="0.03",
                           allowedFileType=".p7b", limit=1, params=[])
    crlFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="0.256",
                           allowedFileType=".crl", limit=1, params=[])
    password: str = Field(None, description="证书密码")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"certType": ["运营商证书"]}, {"moId": "1", "caFile": ..., "pfxFile": ..., "password": ...}),
            ({"certType": ["设备商证书"]}, {"caFile": ..., "pfxFile": ..., "password": ...}),
            ({"certType": ["信任链证书"]}, {"moId": "1", "caFile": ...}),
            ({"certType": ["交叉证书"]}, {"moId": "1", "p7bFile": ...}),
            ({"certType": ["证书撤销列表"]}, {"moId": "1", "crlFile": ...})
        ]
        dynamic_paras = ["moId", "caFile", "pfxFile", "p7bFile", "crlFile", "password"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)


CertUpload.schema()