# -*- encoding: utf-8 -*-
"""
@File    :   DvService.py
@Time    :   2023/08/24 10:47:44
<AUTHOR>   侯小飞10270755,李双红10258068
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import json

from fastapi import APIRouter, Request
from api.Response import fail, success
from api.endpoints.action.ume.ose.dv.Schemas import DvModifyPara, DvReplacePara, DvReadParaList
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from service.action.ume.ose.DvService import DvService
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/modify", summary="修改基站DV参数",
             description=description("修改用户指定基站DV参数值", "侯小飞10270755;李双红10258068"))
async def modify(request: Request, paras: DvModifyPara):
    mes = await MeFactory.create(request)
    modifyInfo, successFlag = {}, True
    for me in mes:
        if paras.meId and paras.meId != me.meId:
            continue
        Logger.info(f"正在修改{me.meId}")
        ret, resultflag = await DvService.modify_me_dv(me, json.loads(paras.attrs), paras.scenelist)
        modifyInfo.update({me.meId: ret})
        successFlag *= resultflag
    if successFlag and modifyInfo:
        return success(data=modifyInfo, msg="dv全部修改成功")
    return fail(data=modifyInfo, msg="dv修改有失败")


@router.post("/stringReplace", summary="替换DV字符串",
             description=description("替换网元DV文件中指定的所有字符串", "侯小飞10270755;李双红10258068"))
async def replace_string_in_dv(request: Request, paras: DvReplacePara):
    mes = await MeFactory.create(request)
    modifyInfo, successFlag = {}, True
    for me in mes:
        if paras.meId and paras.meId != me.meId:
            continue
        Logger.info(f"正在替换{me.meId}")
        ret, resultflag = await DvService.replace_string_me_dv(me, paras.originString, paras.newString)
        modifyInfo.update({me.meId: ret})
        successFlag *= resultflag
    if successFlag and modifyInfo:
        return success(data=modifyInfo, msg="dv全部修改成功")
    return fail(data=modifyInfo, msg="dv修改有失败")


@router.post("/readValue", summary="查询DV参数值",
             description=description("读取dv文件参数对应的value值", "侯小飞10270755;李双红10258068"))
async def read_dv_value(request: Request, paras: DvReadParaList):
    mes = await MeFactory.create(request)
    modifyInfo, successFlag = {}, True
    for me in mes:
        if paras.meId and paras.meId != me.meId:
            continue
        Logger.info(f"正在查询{me.meId}")
        ret, resultflag = await DvService.read_me_dv(me, paras.attrlist.split())
        modifyInfo.update({me.meId: ret})
        successFlag *= resultflag
    if successFlag and modifyInfo:
        return success(data=modifyInfo, msg="查询成功")
    return fail(data=modifyInfo, msg="查询存在失败，请检查入参")
