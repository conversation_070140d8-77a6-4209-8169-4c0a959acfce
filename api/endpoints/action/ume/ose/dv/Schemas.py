# -*- encoding: utf-8 -*-
"""
@File    :   DvService.py
@Time    :   2023/08/24 10:47:44
<AUTHOR>   侯小飞10270755,李双红10258068
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import json
import re

from enum import Enum
from pydantic import BaseModel, Field, validator
from typing import List

SCENCECODE = {"低频":"0", "高频":"1", "sub1G":"2", "qcell":"3", "低频高铁":"4", "SuperMIMO":"5", "5G-R": "6"}

class Scene(str, Enum):
    lf = "低频"
    hf = "高频"
    sub1g = "sub1G"
    qcell = "qcell"
    lfhsr = "低频高铁"
    supermimo = "SuperMIMO"
    g5_r = "5G-R"


class DvModifyPara(BaseModel):
    meId: str | None = Field(None, description='网元Id,默认不填表示修改环境中所有网元')
    attrs: str = Field(
        ...,
        description='需要修改的DV参数(支持参数路径方式),例: PuschPc.tpcDCISwich: false, DlMaxSchedUeNum: 30, dlStaReptRUNum: 1;1;1'
                    '注意:如果key或者value中包含":"冒号,请用2个连续冒号来区分,例: startTime:23::30')
    scenelist: List[Scene] = Field([Scene.lf,
                                    Scene.hf,
                                    Scene.sub1g,
                                    Scene.qcell,
                                    Scene.lfhsr,
                                    Scene.supermimo,
                                    Scene.g5_r
                                    ],
                                   description='场景类型,默认全部修改')

    @validator("attrs", pre=True)
    def attrs_to_jsonstr(cls, v):
        v = v.rstrip(', ，}')
        v = v.lstrip('{ ')
        v = re.sub(r"\s*[:：]\s*True", ":true", v)
        v = re.sub(r"\s*[:：]\s*False", ":false", v)
        v = re.sub(r"['\"’‘“”]", "", v)
        v = re.sub(r"\s*[:：]\s*", "\":\"", v)
        v = re.sub(r"\":\"\":\"", ":", v)
        v = re.sub(r"\s*[,，]\s*", "\",\"", v)
        return "{\"" + v + "\"}"

    @validator("attrs")
    def attrs_validator(cls, v):
        try:
            attr = json.loads(v)
        except json.JSONDecodeError:
            raise ValueError("json格式有误，请检查!")
        attr_upper = list(map(str.upper, attr.keys()))
        if len(attr_upper) == len(set(attr_upper)):
            return v
        else:
            raise ValueError("输入的参数中有重复（忽略大小写），请检查!")

    @validator("scenelist")
    def scenelist_to_num(cls, v):
        return [SCENCECODE.get(_) for _ in v]


class DvReplacePara(BaseModel):
    meId: str | None = Field(None, description='网元Id,默认不填表示修改环境中所有网元')
    originString: str = Field(..., description='原始字符串，例:"tpcDCISwich":false')
    newString: str = Field(..., description='新的字符串,例:"tpcDCISwich":true')

    @validator("originString", "newString", pre=True)
    def remove_space(cls, v):
        v = re.sub(r"'", "\"", v)
        v = re.sub(r"\s*:\s*", ": ", v)
        v = re.sub(r": True", ": true", v)
        v = re.sub(r": False", ": false", v)
        return v


class DvReadParaList(BaseModel):
    meId: str | None = Field(None, description='网元Id,默认不填表示修改环境中所有网元')
    attrlist: str = Field(..., description='需要查询的字段，多个参数间用空格" "隔开,例:tpcDCISwich DlMaxSchedUeNum')
