# -*- encoding: utf-8 -*-
"""
@File    :   DvService.py
@Time    :   2023/08/25 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import os
import traceback

from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from service.action.ume.ose.OseAppService import OseAppService
from api.endpoints.action.ume.ose.ose_genreal.Schemas import RunOseApp
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/run", summary="执行OSE应用",
             description=description("执行指定名称OSE应用（请确保网管上已上传相应OSE应用;也支持用户自行上传OSE应用）",
                                     "侯小飞10270755"))
async def run(request: Request, paras: RunOseApp):
    mes = await MeFactory.create(request)
    appTemp = paras.appName or (paras.appFile[0] if paras.appFile else "")
    if not appTemp: return fail(msg=f"必须输入应用名称或者上传本地应用包")
    packetName = os.path.basename(appTemp).split('.oap')[0]
    try:
        resultflag, result, taskname = await \
            OseAppService.run_app_and_get_data_file(mes[0], appTemp, request=request, paras=paras)
        if resultflag:
            return success(msg=f"{packetName}执行成功", data={"任务名称":taskname, "结果": result})
        return fail(msg=f"{packetName}执行失败,原因:{result},请通过产物查看OSE运行详情", data={"任务名称":taskname})
    except BaseException as err:
        Logger.error(f"{packetName}执行过程中存在失败,详情:",traceback.format_exc())
        return fail(msg=f"{packetName}执行过程中存在失败,原因:{err}")



