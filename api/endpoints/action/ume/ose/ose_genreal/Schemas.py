# -*- encoding: utf-8 -*-
"""
@File    :   DvService.py
@Time    :   2023/10/24 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

from pydantic import BaseModel, Field
from typing import TypeVar

DataT = TypeVar("DataT")


class RunOseApp(BaseModel):
    guidebook: DataT = Field(None, type="label", viewType="url", urlName="执行OSE应用使用说明",
                             url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/"
                                 "72f804da96ca41e0b861be25b6b789cb/view")
    appName: str = Field(None, description='应用名称,如果服务器上存在同名oap应用程序,将用服务器上的ose更新;如果服务器上不存在同名应用,将使用网管上已有的应用程序')
    appFile: DataT  = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                                 allowedFileType=".oap", limit=1, params=[], description='本地应用路径,用户可通过此参数上传本地应用程序,和appName任选其一')
    parasJson: DataT = Field({}, type="jsonEditor",
                             description="应用执行参数,请按照所选应用的所需属性按json格式输入,可以参考'脚本执行引擎'->'应用详情'->'输入参数'说明,或者参考应用界面上参数和值配置."
                                         "注意:1.对于有默认值的属性,即使没有实际入参,action也会按默认值处理;"
                                         "2.对于File类参数,如果是可选参数,true表示上传/false或者不入这个属性表示不上传;"
                                         "如果是必选参数,入不入参,action都会通过file参数上传的文件依次按序去替换;"
                                         "3.对于Resource类参数,当前仅支持me、cell类型,参数中输入过滤类型me、CU&DU&PHY,"
                                         "action会根据环境信息中的网元、小区信息自动填充(如果不入将以默认类型替换)")
    file: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                        allowedFileType=".*", params=[],
                        description='上传应用参数中类型是File的参数文件,注意应用中如果有多个File参数,'
                                    'action将根据这里的上传先后顺序进行依次按序替换,注意需要和parasJson中file类参数顺序对应')

