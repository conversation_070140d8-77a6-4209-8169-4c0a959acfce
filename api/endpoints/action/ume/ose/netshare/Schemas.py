# -*- encoding: utf-8 -*-
"""
@File    :   DvService.py
@Time    :   2023/10/24 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

from pydantic import BaseModel, Field, validator

class NetShareCreatePara(BaseModel):
    gNBId:  str = Field(..., description='逻辑站gNBId(范围：0~**********)，当前网元中gNBId+plmn组合逻辑站不存在时，将创建独立逻辑小区组网网络共享（新增逻辑站），'
                                         '否则创建共享逻辑小区组网网络共享（在原有逻辑小区新增plmn），注意：'
                                         'GNBCUCPFunction.pLMNId+gNBId全网不能存在重复',
                        regex= r'^(0|[1-9]\d{0,8}|[1-3]\d{9}|4[0-1]\d{8}|42[0-8]\d{7}|429[0-3]\d{6}|4294[0-8]\d{5}|'
                               r'42949[0-5]\d{4}|429496[0-6]\d{3}|4294967[0-1]\d{2}|42949672[0-8]\d|429496729[0-5])$')
    NSSAI:  str = Field(..., description='网络切片(格式参考开站文件)，其中第一个plmn为gNodeB承建方运营商格式：tac@plmn:sd '
                                         '举例：512002@460-08;460-01:1118481;118486', regex= r'^(\d{1,7}@)?\d{3}-\d{2}')
    cellId:  str = Field(..., description='小区cellLocalId(范围：0~16383)，当前网元 gNBId+plmn+cellLocalId组合逻辑小区不存在时，'
                                               '将创建独立逻辑小区组网网络共享（新增逻辑小区），'
                                               '否则创建共享逻辑小区组网网络共享（在原有逻辑小区新增plmn）',
                         regex= r'^(0|[1-9]\d{0,3}|1[0-5]\d{3}|16[0-2]\d{2}|163[0-7]\d|1638[0-3])$')
    phyCellMoId: str = Field(..., description='逻辑小区的索引物理小区的moId，只有新增逻辑小区（gNBId+plmn+cellLocalId组合逻辑小区不存在时）'
                                                     '该参数才生效')
    plan_area: str | None= Field(None, description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    remotePort: str | None= Field(None, description='NGAP对应SCTP远端端口号（选填），默认不填表示使用原站参数')
    remoteIp: str | None = Field(None, description='NGAP远端地址（选填），默认不填表示使用原站参数')
    IPmoId: str | None = Field(None, description='IP对象(TransportNetwork->Ip)moId标识（选填），默认不填表示使用原站参数')

    @validator("gNBId", "cellId")
    def attrs_to_str(cls, v):
        return str(v)

    @validator("remotePort", "remoteIp", "IPmoId")
    def none_to_str(cls, v):
        return v if v else ""

