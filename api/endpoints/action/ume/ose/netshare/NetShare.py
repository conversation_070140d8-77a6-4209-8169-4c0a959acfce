# -*- encoding: utf-8 -*-
"""
@File    :   DvService.py
@Time    :   2023/08/25 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import json
import traceback

from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from api.endpoints.action.ume.ose.netshare.Schemas import NetShareCreatePara
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from service.action.ume.ose.OseAppService import OseAppService
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/create", summary="创建网络共享",
             description=description("通过OSE脚本，在网管上创建网络共享配置(注意：仅支持一个网元配置，请通过资源选择选出目标网元)",
                                     "侯小飞10270755"))
async def modify(request: Request, paras: NetShareCreatePara):
    mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="当前环境信息中没有网元，请确保环境中有网元资源")
    if len(mes) > 1:
        return fail(msg=f"当前环境中有{len(mes)}网元，请通过资源选择选出一个目标网元")
    try:
        _paras = {k: v if v else "" for k, v in dict(paras).items()}
        _paras.update({"cellLocalId":_paras.get("cellId", ""), "nrPhysicalCellDUId":_paras.get("phyCellMoId", "")})
        Logger.info(f"网元{mes[0].meId}正在创建网络共享配置:{_paras}")
        resultflag, result, taskname = await OseAppService.create_net_share(mes[0], _paras)
        if resultflag:
            return success(msg=f"{mes[0].meId}: 网络共享创建成功", data={"任务名称":taskname})
        return fail(msg=f"{mes[0].meId}: 网络共享创建失败,原因：{result},可进入规划区(ID:{_paras.get('plan_area')})查看详情",
                    data={"任务名称":taskname})
    except BaseException as err:
        Logger.error(f"{mes[0].meId} 创建网络共享执行失败,详情:",traceback.format_exc())
        return fail(msg=f"{mes[0].meId}: 创建网络共享执行失败，原因:{err},可通过ose执行日志定位")

