from typing import TypeVar, Literal
from datetime import datetime
from pydantic import BaseModel, Field, validator, root_validator
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar('DataT')

INTENTYPE_MAP = {"业务保障": "QOS", "节能": "ES", "视频保障": "MOS", "自定义": "CUSTOM"}
TEMP_MODIFY_JSON = {"qosTypeEnsurePolicy":[["27860|2", {"Level-3GuaranteeParas": "qosTypePFCoeff=250;preSchedulethreshold4RRC=3500"}]]}

class ImportQosPolicy(BaseModel):
    policyFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                              allowedFileType=".zip", limit=1, params=[],
                              description="导入zip格式策略文件,当输入该参数时,表明本地导入(高优先级);"
                                          "如果action有依赖'IBOT导出业务和视频保障策略',将导入其导出的策略文件")
    isActive: bool = Field(True, description='是否激活，true - 激活； false - 不激活', ifEmptyUseDefault=False)

    @validator('isActive', pre=True, always=False)
    def validate_keyMoPath(cls, v):
        return True if v else False


    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        isActive_field = cls.__fields__["isActive"]
        if not values.get("isActive"):
            if not isActive_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["isActive"] = False
        return values


class CreateIntentTask(BaseModel):
    intentType: Literal["业务保障", "节能", "视频保障", "自定义"] = Field("业务保障", description="意图类型")
    bussinessNames: str = Field(None, description="应用名，多个应用名用顿号'、'连接")
    inputIntent: str = Field("明天对网元18045开启平衡模式节能", description="自定义输入的意图")
    guaranteeLevel: Literal["一级", "二级", "三级"] = Field("三级", description="保障等级")
    esMode: Literal["节能最大化模式", "平衡模式", "性能无损模式"] = Field("节能最大化模式", description="节能模式")
    mosValue: str = Field("2.0", description="视频保障MOS值")
    startTime: datetime | None = Field(None, description='任务开始时间,默认不填,默认当前系统时间')
    endTime: datetime | None = Field(None, description='任务结束时间,默认不填,表示当前系统时间延后1小时为结束时间')

    @validator("intentType")
    def update_intent_type(cls, v):
        return INTENTYPE_MAP.get(v, "QOS")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"intentType": ["业务保障"]}, {"bussinessNames": "", "guaranteeLevel": "三级", "startTime": None, "endTime": None}),
            ({"intentType": ["节能"]}, {"esMode": "节能最大化模式", "startTime": None, "endTime": None}),
            ({"intentType": ["视频保障"]}, {"bussinessNames": "", "mosValue": "2.0", "startTime": None, "endTime": None}),
            ({"intentType": ["自定义"]}, {"inputIntent": "明天对网元18045开启平衡模式节能"})
        ]
        dynamic_paras = ["bussinessNames", "guaranteeLevel", "esMode", "mosValue", "inputIntent", "startTime", "endTime"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

CreateIntentTask.schema()


class ManageIntentPolicy(BaseModel):
    opType: Literal["删除网元保障策略", "激活网元策略", "查询网元策略详情", "修改网元策略", "修改MOS基础数据采集时长"] = (
        Field("删除网元保障策略", description="管理网元意图操作类型"))
    policyName: str = Field(None, description="删除、激活、查询业务策略文件名,当输入时优先使用其策略激活;"
                                              "不填时,如果action有依赖'IBOT导入业务和视频保障策略',将激活对应导入的策略;"
                                              "如果不填,又没有依赖，将操作网元对应系统预置策略;"
                                              "注:修改策略时，该参数和资源不生效，会在当前网元已激活策略上导出修改))")
    modifyJsonText: DataT = Field(default=TEMP_MODIFY_JSON, type="jsonEditor", description="修改策略文件对应的JSON，请确保按照指定格式输入")
    minutes: int = Field(0, description="视频保障MOS基础数据采集时长,默认0分钟")


    @classmethod
    def schema(cls):
        schema_map = [
            ({"opType": ["删除网元保障策略"]}, {"policyName": ""}),
            ({"opType": ["激活网元策略"]}, {"policyName": ""}),
            ({"opType": ["查询网元策略详情"]}, {"policyName": ""}),
            ({"opType": ["修改网元策略"]}, {"modifyJsonText": TEMP_MODIFY_JSON}),
            ({"opType": ["修改MOS基础数据采集时长"]}, {"minutes": 0})
        ]
        dynamic_paras = ["policyName", "modifyJsonText", "minutes"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

ManageIntentPolicy.schema()
