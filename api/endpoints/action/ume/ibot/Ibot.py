import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.ibot.Schemas import CreateIntentTask, ImportQosPolicy, ManageIntentPolicy
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from service.action.ume.ibot.IntentManagerService import IntentManagerService

router = APIRouter(route_class=bizRoute)


@router.post("/create_task", summary="IBOT创建意图网络任务",
             description=description("IBOT创建意图网络任务", "侯小飞10270755"))
async def create_task(request: Request, para: CreateIntentTask):
    mes = await MeFactory.create_by_nr(request)
    successFlag, taskInfo = True, {}
    for me in mes:
        try:
            intentId = await IntentManagerService.create_task(me, dict(para))
            taskInfo.update({me.meId: intentId})
        except BaseException as err:
            Logger.error(f"网元：{me.meId} IBOT创建意图网络任务失败，详情:", traceback.format_exc())
            taskInfo.update({me.meId: f"{err}"})
            successFlag = False
    if successFlag:
        return success(data=taskInfo, msg="IBOT创建意图网络任务成功")
    return fail(msg=f"IBOT创建意图网络任务失败", data=taskInfo)


@router.post("/delete_task", summary="IBOT删除意图网络任务",
             description=description("IBOT删除意图网络任务", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "IBOT创建意图网络任务", "refPattern": "1-*"}]})
async def delete_task(request: Request):
    mes = await MeFactory.create_by_nr(request)
    taskInfos = await Refs(request).get_output()
    Logger.info("依赖IBOT查询意图网络任务详情", taskInfos)
    if not taskInfos[0]:
        return fail(
            msg="IBOT删除意图网络任务失败,当前依赖'IBOT创建意图网络任务'结果为空,请确认依赖action是否执行正常!!!")
    successFlag, result = True, {}
    for taskInfo in taskInfos:
        for meId, intentId in taskInfo.items():
            try:
                await IntentManagerService.delete_task(mes[0], intentId)
            except BaseException as err:
                Logger.error(f"网元：{meId} IBOT删除意图网络任务失败，详情:", traceback.format_exc())
                successFlag = False
                result.update({meId: f"{err}"})
    if successFlag:
        return success(msg="IBOT删除意图网络任务成功")
    return fail(msg=f"IBOT删除意图网络任务失败", data=result)


@router.post("/query_task", summary="IBOT查询意图网络任务详情",
             description=description("IBOT查询意图网络任务详情", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "IBOT创建意图网络任务", "refPattern": "1-*"}]})
async def query_task(request: Request):
    mes = await MeFactory.create_by_nr(request)
    taskInfos = await Refs(request).get_output()
    Logger.info("依赖IBOT创建意图网络任务详情", taskInfos)
    if not taskInfos[0]:
        return fail(
            msg="IBOT查询意图网络任务详情失败,当前依赖'IBOT创建意图网络任务'结果为空,请确认依赖action是否执行正常!!!")
    successFlag, result = True, {}
    for taskInfo in taskInfos:
        for meId, intentId in taskInfo.items():
            try:
                detail = await IntentManagerService.query_task(mes[0], intentId)
                result.update({meId: detail})
            except BaseException as err:
                Logger.error(f"网元：{meId} IBOT查询意图网络任务详情失败，详情:", traceback.format_exc())
                successFlag = False
                result.update({meId: f"{err}"})
    if successFlag:
        return success(data=result, msg="IBOT查询意图网络任务详情成功")
    return fail(msg=f"IBOT查询意图网络任务详情失败", data=result)


@router.post("/export_qos_policy", summary="IBOT导出业务和视频保障策略",
             description=description(
                 "IBOT导出业务和视频保障策略:优先导出基站已激活的策略,如果没有则导出版本对应的系统预置策略",
                 "侯小飞10270755"))
async def query_task(request: Request):
    mes = await MeFactory.create_by_nr(request)
    successFlag, result = True, {}
    for me in mes:
        try:
            _, policyUrl = await IntentManagerService.export_qos_policy(me)
            result.update({me.meId: policyUrl})
        except BaseException as err:
            Logger.error(f"网元：{me.meId} IBOT导出业务和视频保障策略失败，详情:", traceback.format_exc())
            successFlag = False
            result.update({me.meId: f"{err}"})
    if successFlag:
        return success(data=result, msg="IBOT导出业务和视频保障策略成功")
    return fail(msg=f"IBOT导出业务和视频保障策略失败", data=result)


@router.post("/import_qos_policy", summary="IBOT导入业务和视频保障策略",
             description=description("IBOT导入业务和视频保障策略", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "IBOT导出业务和视频保障策略", "refPattern": "0-1"}]})
async def import_qos_policy(request: Request, para: ImportQosPolicy):
    mes = await MeFactory.create_by_nr(request)
    policyInfo = (await Refs(request).get_output())
    Logger.info("依赖IBOT导出业务和视频保障策略详情", policyInfo)
    if not para.policyFile and not policyInfo:
        return fail(msg=f"IBOT导入业务和视频保障失败,界面policyFile和资源依赖至少选择1个")
    successFlag, result, isImportFlag = True, {}, False
    for me in mes:
        try:
            if para.policyFile:
                if not isImportFlag:
                    isImportFlag = True
                    newFileName = await IntentManagerService.import_qos_policy(me, para.policyFile[0])
            else:
                newFileName = await IntentManagerService.import_qos_policy(me, policyInfo[0].get(me.meId))
            result.update({me.meId: newFileName})
            if para.isActive:
                await IntentManagerService.active_ne_qos_policy(me, newFileName)
        except BaseException as err:
            Logger.error(f"{me.meId} IBOT导入业务和视频保障策略后激活失败，详情:", traceback.format_exc())
            successFlag = False
            result.update({me.meId: f"{err}"})
    if successFlag:
        return success(msg="IBOT导入业务和视频保障策略成功(已激活)", data=result)
    return fail(msg=f"IBOT导入业务和视频保障策略失败", data=result)


@router.post("/management_intent_policy", summary="IBOT网元意图策略管理",
             description=description(
                 "IBOT网元意图策略管理,删除、激活、查询业务策略可通过依赖'IBOT导入业务和视频保障策略'操作对应策略",
                 "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "IBOT导入业务和视频保障策略", "refPattern": "0-1"}]})
async def management_intent_policy(request: Request, para: ManageIntentPolicy):
    mes = await MeFactory.create_by_nr(request)
    policyInfo = (await Refs(request).get_output())
    Logger.info("IBOT导入业务和视频保障策略", policyInfo)
    successFlag, result = True, {}
    for me in mes:
        try:
            paras = dict(para)
            policyName = para.policyName if (para.policyName or not policyInfo) else policyInfo[0].get(me.meId)
            paras.update({"policyName": policyName, "modifyDict": para.modifyJsonText})
            data = await IntentManagerService.management_intent_policy(me, para.opType, paras)
            result.update({me.meId: data})
        except BaseException as err:
            Logger.error(f"{me.meId} IBOT网元意图策略管理:{para.opType}失败，详情:", traceback.format_exc())
            successFlag = False
            result.update({me.meId: f"{err}"})
    if successFlag:
        return success(msg=f"IBOT网元意图策略管理:{para.opType}成功", data=result)
    return fail(msg=f"IBOT网元意图策略管理:{para.opType}失败", data=result)
