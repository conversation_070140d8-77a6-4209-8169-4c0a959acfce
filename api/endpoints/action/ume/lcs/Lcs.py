import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.lcs.Schemas import NeLcsMange, LcsCenterMange, LcsCenterLoadFile
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from service.action.ume.lcs.LcsService import LcsService

router = APIRouter(route_class=bizRoute)



@router.post("/lcs_center_upload_file", summary="LCS中心上传文件",
             description=description("在网管License管理模块，license中心上传license文件,只支持.LCS文件", "侯小飞10270755"))
async def lcs_center_upload_file(request: Request, para: LcsCenterLoadFile):
    mes = await MeFactory.create(request)
    try:
        res, msg = await LcsService.upload_file_to_license_center(mes[0], para.lcsFile[0])
        if res: return success(msg=f"LCS中心上传license文件成功", data=msg)
    except BaseException as err:
        Logger.error(f"LCS中心上传license文件失败，详情:", traceback.format_exc())
        return fail(msg=f"LCS中心上传license文件失败，详情: {err}")


@router.post("/manage_lcs_center", summary="LCS中心文件管理",
             description=description("在网管License管理模块，管理license文件", "侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "LCS中心上传文件", "refPattern": "0-1"}]})
async def manage_lcs_center(request: Request, para: LcsCenterMange):
    mes = await MeFactory.create(request)
    lcsfile = await Refs(request).get_output()
    lcsfile = lcsfile[0] if lcsfile else para.lcsFileName
    if not lcsfile:
        return fail(msg=f"必须通过依赖'LCS中心上传文件'或者界面输入管理的license文件名")
    try:
        paraTemp = dict(para)
        paraTemp.update({"fileName": lcsfile})
        ret, msg = await LcsService.lcs_center_management(mes[0], para.opType, paraTemp)
        if ret:
            return success(msg=f"LCS中心文件管理'{para.opType}'执行成功", data=msg)
        return fail(msg=f"LCS中心文件管理'{para.opType}'执行失败", data=msg)
    except BaseException as err:
       return fail(msg=f"LCS中心文件管理'{para.opType}'执行失败,原因:{err}")


@router.post("/manage_ne_lcs", summary="网元LCS管理",
             description=description("在网管License管理模块,管理网元license文件", "侯小飞10270755"))
async def manage_ne_lcs(request: Request, para: NeLcsMange):
    mes = await MeFactory.create(request)
    errFlag, result = False, {}
    if not mes: return fail(msg=f"当前无满足条件网元,请检查:环境信息中是否存在网元!")
    for me in mes:
        try:
            paraTemp = dict(para)
            ret, msg = await LcsService.ne_lcs_management(me, para.opType, paraTemp)
            if ret:
                result.update({me.meId: msg or "成功"})
            else:
                errFlag = True
                result.update({me.meId: f"{para.opType}执行失败,原因:{msg}"})
        except BaseException as err:
            errFlag = True
            result.update({me.meId: f"{para.opType}执行失败,原因:{err}"})
    if errFlag:
        return fail(msg=f"网元LCS管理'{para.opType}'执行失败", data=result)
    return success(msg=f"网元LCS管理'{para.opType}'执行成功", data=result)
