from typing import TypeVar, Literal

from pydantic import BaseModel, Field, validator
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar('DataT')


class NeLcsMange(BaseModel):
    opType: Literal["加载license", "删除临时license", "设置紧急状态", "查询license"] = \
        Field("加载license", description="网元LCS操作类型")
    product: Literal["NR", "LTE", "MEC"] = Field("NR", description="产品名称 'NR'(默认)'")
    table: str = Field("基本信息", description="查询网元license信息目标键值, 默认查'基本信息',如果要查多个表，请用'&'连接")
    operate: Literal["进入", "退出"] = Field("进入", description="设置紧急模式类型,默认:进入")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"opType": ["加载license"]}, {"product": "NR"}),
            ({"opType": ["删除临时license"]}, {}),
            ({"opType": ["设置紧急状态"]}, {"operate": "进入"}),
            ({"opType": ["查询license"]}, {"table": "基本信息"})]
        dynamic_paras = ["product", "table", "operate"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)


NeLcsMange.schema()


class LcsCenterMange(BaseModel):
    opType: Literal["删除"] = Field("删除", description="License中心管理操作类型")
    lcsFileName: str|None = Field(None, description="删除LCS中心LCS文件名,当依赖了'LCS中心上传license文件'时,该参数不生效", regex=r".*\.LCS")


class LcsCenterLoadFile(BaseModel):
    lcsFile: DataT = Field(..., type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="10",
                                allowedFileType="*.LCS", limit=1, params=[],
                                description="license文件,只支持一个LCS格式文件上传,对于需要加载多个LCS文件的,请多次调用")
