import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.uac.Schemas import CreateUacUser, ManageUacUser
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from service.action.ume.uac.UacService import UacService

router = APIRouter(route_class=bizRoute)


@router.post("/create_user", summary="UAC创建用户",
             description=description("在网管UAC模块(请务必确认网管账户具备UAC权限)，新创建用户(如果用户已经存在，则按修改模式更新用户信息)",
                                     "侯小飞10270755"))
async def create_user(request: Request, paras: CreateUacUser):
    mes = await MeFactory.create(request)
    mode = "创建"
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    try:
        _, mode = await UacService.create_user(mes[0], dict(paras))
    except BaseException as err:
        Logger.error(f"UAC{mode}用户失败，详情:", traceback.format_exc())
        return fail(msg=f"UAC{mode}用户失败，详情: {err}")
    return success(msg=f"UAC{mode}用户成功", data= {"user": paras.userName})


@router.post("/manage_user", summary="UAC用户管理",
             description=description("在网管UAC模块(请务必确认网管账户具备UAC权限)，管理创建用户", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "UAC创建用户", "refPattern": "0-*"}]})
async def manage_user(request: Request, paras: ManageUacUser):
    users = await Refs(request).get_output("user") or [paras.user] or [""]
    if paras.func == "删除用户" and (not users or users == [""]):
        return fail(msg="当操作类型是'删除用户'时, user不能为空, 请检查输入!!!")
    mes = await MeFactory.create(request)
    if not mes: return fail(msg=f"当前无满足条件网元，请检查：环境信息中是否存在网元!")
    errFlag, result = False, {}
    for user in users:
        try:
            parasDict = dict(paras)
            parasDict.update({"user": user})
            ret, msg= await UacService.user_management(mes[0], paras.func, parasDict)
            if ret:
                result[user] = msg
        except BaseException as err:
            Logger.error(f"UAC用户管理'{paras.func}失败，详情:", traceback.format_exc())
            errFlag = True
            result[user] = f"{err}"
    if errFlag:
        return fail(msg=f"UAC用户管理'{paras.func}'失败", data=result)
    return success(msg=f"UAC用户管理'{paras.func}'成功", data=result)
