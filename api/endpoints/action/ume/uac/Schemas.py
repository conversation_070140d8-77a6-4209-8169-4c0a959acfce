from enum import Enum
from typing import TypeVar, Literal

from pydantic import BaseModel, Field, validator
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar('DataT')


class UserType(str, Enum):
    neuserType = "网元用户"
    nmuserType = "网管用户"


class TenantRoleNames(str, Enum):
    t1 = "系统管理员"
    t2 = "系统维护员"
    t3 = "系统操作员"
    t4 = "系统监控员"
    t5 = "安全管理员"
    t6 = "北向Socket告警角色"
    t7 = "通用 Open API角色"
    t8 = "个人数据安全管理员"
    t9 = "辅运营商通用角色"
    t10 = "辅运营商运维角色"


class NeRoleNames(str, Enum):
    n1 = "NRRcsAdministrator"
    n2 = "LTEENBCUUPFunctionAdministrator"
    n3 = "NRGNBDUFunctionReadonly"
    n4 = "LTEENBDUFunctionReadonly"
    n5 = "NRGNBCUCPFunctionAdministrator"
    n6 = "NRGNBCUUPFunctionAdministrator"
    n7 = "NRGNBCUCPFunctionReadonly"
    n8 = "NRGNBCUUPFunctionReadonly"
    n9 = "NRGNBDUFunctionAdministrator"
    n10 = "SystemAdministrator"
    n11 = "NRRcsReadonly"
    n12 = "NRSonAdministrator"
    n13 = "LTEMCEFunctionAdministrator"
    n14 = "LTEENBCUCPFunctionAdministrator"
    n15 = "LTEENBCUUPFunctionReadonly"
    n16 = "LTEENBCUCPFunctionReadonly"
    n17 = "LTEMCEFunctionReadonly"
    n18 = "SystemReadOnly"
    n19 = "NRSonReadonly"
    n20 = "LTEENBDUFunctionAdministrator"
    n21 = "SystemSecurityAdministrator"


ADDITION_TEMPLATE = {"账户规则": "默认规则", "用户组": "根用户组", "同时登录数限制": "不限制"}


class CreateUacUser(BaseModel):
    userName: str = Field(..., description="用户名，必填")
    userNetworkType: list[UserType] = Field([UserType.neuserType, UserType.nmuserType], description="用户类型")
    password: str = Field("Zenap_123", description="密码，如果是创建用户必填;修改用户填'#{EMPTY}'表示保持上一次的密码值")
    tenantRoleNames: list[TenantRoleNames] = Field([TenantRoleNames.t1], description="本地系统用户角色")
    neroleNames: list[NeRoleNames] = Field([NeRoleNames.n5, NeRoleNames.n6, NeRoleNames.n9], description="用户网元角色")
    addition: DataT = Field(default=ADDITION_TEMPLATE, type="jsonEditor",
                            description=f"其他附加参数,可按照网管界面参数入参,例: {ADDITION_TEMPLATE}")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"userNetworkType": [["网元用户"]]}, {"neroleNames": all}),
            ({"userNetworkType": [["网元用户", "网管用户"]]}, {"neroleNames": all, "tenantRoleNames": all}),
            ({"userNetworkType": [["网管用户"]]}, {"tenantRoleNames": all})
        ]
        dynamic_paras = ["tenantRoleNames", "neroleNames"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

CreateUacUser.schema()

class ManageUacUser(BaseModel):
    func: Literal["删除用户", "查询用户"] = Field("删除用户", description="用户管理操作类型,默认'查询用户'")
    user: str = Field("", description="被操作的用户名，当操作类型是'删除用户'时,该参数需填写或者依赖'UAC创建用户';"
                                      "如果操作类型是'查询用户'是,该参数填空并且未依赖'UAC创建用户'时,将查询当前所有用户信息")



