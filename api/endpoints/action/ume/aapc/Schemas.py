import json
from datetime import datetime
from enum import Enum
from typing import TypeVar, Optional, Literal
from infrastructure.logger import Logger

from pydantic import BaseModel, Field, validator

DataT = TypeVar("DataT")


AAPC_OPENAPI_PARAMETERS_TEMPLATE = {
"productInfo": "NR",
"netype": "ITBBU",
"policy": ""
}

AAPC_GUI_PARAMETERS_TEMPLATE = {
    "taskinfo": {
        "scene": 2,
        "taskstate": 1
    },
    "logicalLocation": [],
    "parameter": {
        "optScenario": "0",
        "modelBuildSwtch": "1",
        "optObject": "0",
        "aapcDataCollectionPolicy": "0",
        "aapcServingCellRsrpThrd": "-110",
        "aapcSampleUeType": "0",
        "aapcNbrRsrpThrd": "-110",
        "dataCollectTime": "24",
        "emergencyOptSampleTimeLen": "15",
        "cellSampleNumber": "2100",
        "aapcSampleThrd": "2000",
        "emergencyOptSampleNumThrd": "500",
        "aapcTimeGrade": "1",
        "aapcDataValidRatio": "10",
        "successActivatedRatio": "90",
        "maxSSBnumber": "8",
        "beamHybridNetwork": "0",
        "comWeightAdaptorSwitch": "0",
        "fastConvergenceAlgorithmSwitch": "1",
        "xBeamNumChangeable": "1",
        "optimizationTarget": "3",
        "singleOptimalWeight": "100",
        "overlapNeigCellNum": "2",
        "csiOptSwitch": "1",
        "initWeightCorrectSwth": "1",
        "evaluateSwitch": "0",
        "adaptiveRollbackSwitch": "0",
        "powerFactorRollBackList": "[0]",
        "cellCusterNum4AapcKpiOpt": "4",
        "timeOutAdaptiveRollback": "24",
        "cellSatisfyEvaluateRatio": "80",
        "RRCcellConnectionThred": "200",
        "rollbackSwitch": "0",
        "cellRollbackSwitch": "1",
        "aapcEvaluteTimeLen": "24",
        "emergencyEvaluteTimeLen": "15",
        "weakRsrpThrd": "-110",
        "hdoa_step": "1",
        "vdoa_step": "1",
        "sample_thr": "50",
        "isAutoCluster": "1",
        "clusterCellNumThrd": "20",
        "clusterSplitThd": "0.15",
        "ucOverlapSrvThd": "-110",
        "ucOverlapNbrThd": "-110",
        "ucOverlapNbrDifferThd": "6",
        "weakCoverageRSRPJudgmentThreshold": "-120",
        "cellWeightType": "2",
        "algFunction": "0",
        "cdfgamma1": "0.5",
        "cdfgamma2": "0.5",
        "aapcFuncMode": "0",
        "tideCorrThr": "0.6",
        "tidalSelfRecognitionSwitch": "1",
        "noWorkDayHours": "[]",
        "workDayHours": "[]",
        "workdayCollectionLengthbyTime": "1",
        "nonWorkdayCollectionLengthbyTime": "1",
        "weakCoverRsrpThrd": "-100",
        "weakCoverNumThrd": "10",
        "weakCoverRatioThrd": "0.2",
        "overlapCoverServRsrpThrd": "-100",
        "overlapCoverNeibRsrpThrd": "-100",
        "overlapCoverDiffThrd": "6",
        "overlapCoverNumThrd": "10",
        "overlapCoverRatioThrd": "0.2",
        "overlapbaselinesampleratio": "1",
        "poorCoverZoneThr": "0.3",
        "overlapZoneThr": "0.3",
        "weightType": "241,242",
        "tiltLimitThrd": "255",
        "azimuthThrd": "255",
        "bwhThrd": "255",
        "beamdecpwr": "0",
        "optTypeFor1x": "0",
        "doaSwitch": "1",
        "emergencyScenarioDetectionPrd": "0",
        "emergencyMaxTimeLen": "6",
        "cqiGoodRateFluctuationThrd": "10",
        "qpskCodeRateFluctuationThrd": "10",
        "avgRrcConnectNumThrd": "10",
        "taDistributeUserPercentThrd": "50",
        "taDistributeFluctuationThrd": "2",
        "beamGridUsrNumFluctuatThrd": "50",
        "beamGridAvgNumUsrFluctThrd": "2.00",
        "optTakeEffectSwth": "1",
        "emergencyOptSwitch": "1",
        "highLoadOptSwitch": "1",
        "emergType": "0,1",
        "alarmClearanceWaitTime": "15",
        "highLoadIdentifiedPeriod": "1",
        "rrcNumHighLoadThrd": "150",
        "dlPrbHighLoadThrd": "20",
        "ulPrbHighLoadThrd": "20",
        "highLoadRrcThrdforNeib": "80",
        "highLoadIdentifiedDelayTime": "2",
        "highLoadTopWeightNum": "500",
        "highLoadRrcAjustRatioThrd": "70",
        "highLoadRrcNumDeThrd1forEst": "300",
        "highLoadRrcNumDeThrd2forEst": "20",
        "highLoadRrcRatioThrdforRec": "30",
        "highLoadRsrpThrdforDe": "10",
        "highLoadSinrThrdforDe": "10",
        "avgRRCThrd4Fault": "3",
        "assistNeibCellUeNumThrd": "200",
        "alphFailCell": "0.7",
        "targetCellSampleThrd": "50",
        "loadSharingEvaluationSwitch": "1",
        "weightOptType": "0",
        "weakSampleRatioThrd": "45",
        "downtiltAdjustmentRange": "-2#6",
        "avgRRCThrd": "30",
        "weakHDOAThrd": "45",
        "sinrdiffThrd": "5",
        "rsrpdiffThrd": "5",
        "minMechanicalAzimuthInterval": "60",
        "optimizeSwthFor8TR": "0",
        "multiCellOptPolicy": "2",
        "optCellssbFrequency": "",
        "modelBuildSampleThrd": "40000",
        "modelBuildOptSampleTimeLen": "96"
    }
}
AAPC_TIDAL_TIME = {
        "workDayHours": [1,24],
        "noWorkDayHours": []
    }

class CreateTask(BaseModel):
    templateType: Literal['GUI'] = Field('GUI', description='网管AAPC任务创建界面默认模板')
    parameters: DataT = Field(default=AAPC_GUI_PARAMETERS_TEMPLATE, type="jsonEditor", description="按照标准格式填写 任务参数")
class AapcMonitorReport(BaseModel):
    tidalTime: DataT = Field(default=AAPC_TIDAL_TIME, type="jsonEditor", description="按照标准格式填写,此参数只在潮汐场景时才生效；潮汐场景时，工作日和非工作日时段互斥，只能填写其一")


class AapcExecuteResult(BaseModel):
    optimizeParameter: Literal["优化参数", "机械角建议调整信息"] = Field("优化参数", description='用户选择查看执行结果中的 优化参数  or 机械角调整信息')

    @validator("optimizeParameter")
    def validate_check_name(cls, optimizeParameter):
        if optimizeParameter == '优化参数':
            return 1
        return 2