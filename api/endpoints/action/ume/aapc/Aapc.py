import traceback
import pytz
import time
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.aapc.Schemas import CreateTask, AapcMonitorReport, AapcExecuteResult
from api.route.BizRoute import bizRoute
from domain.factories.MeFactory import MeFactory
from domain.factories.CellFactory import CellFactory
from infrastructure.logger import Logger
from infrastructure.utils.ResultFormatter import format_query_mo_result
from infrastructure.resource.service.Refs import Refs
from domain.models.ume.aapc.AapcGui import AapcGui

router = APIRouter(route_class=bizRoute)


@router.post("/create_nr_task_action", summary="创建NR天线权值自优化任务", description="【功能】创建NR天线权值自优化任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "path": "/v1/api/action/ume/rancm/mo/get"}]})
async def create_nr_task_action(request: Request, para: CreateTask):
    try:
        mes = []
        newQueryRlts = []
        queryRlts = await Refs(request).get_output()
        Logger.debug(queryRlts)
        needKeys = ["ManagedElement", "SubNetwork", "ldn", "nrPhysicalCellDUId", "userLabel", "cellAtt"]
        if queryRlts:
            for queryRlt in queryRlts:
                queryRltDict = format_query_mo_result(queryRlt)
                Logger.debug(queryRltDict)
                for meId in queryRltDict:
                    Logger.debug(meId)
                    for cellInfo in queryRltDict[meId]:
                        if not all(key in cellInfo for key in needKeys):
                            return fail(msg="NRPhysicalCellDU的 ManagedElement, SubNetwork, ldn, nrPhysicalCellDUId, userLabel, cellAtt必须包含在查询结果中")
                        me = await MeFactory().create_by_meid(request, meId)
                        newQueryRlts.append(queryRltDict)
                        mes.append(me)
        bodyDict = para.parameters
        Logger.debug(para.parameters)
        bodyDict.update(para.parameters)
        if para.templateType == 'GUI':
            rlt, rltInfo = await AapcGui().create_nr_task(mes, newQueryRlts, bodyDict)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is created failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is created successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/modify_nr_task_action", summary="修改NR天线权值自优化任务",
             description="【功能】修改NR天线权值自优化任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/aapc"},
                                     {"actionName": "查询MO节点属性", "path": "/v1/api/action/ume/rancm/mo/get"}]})
async def modify_nr_task_action(request: Request, para: CreateTask):
    try:
        mes = []
        newQueryRlts = []
        queryRlts = await Refs(request).get_output()
        Logger.debug("queryRlts===={0}".format(queryRlts))
        needKeys = ["ManagedElement", "SubNetwork", "ldn", "nrPhysicalCellDUId", "userLabel", "cellAtt"]
        taskId = 0
        taskName = ''
        if queryRlts:
            for queryRlt in queryRlts:
                if 'taskInfo' in queryRlt:
                    taskId = queryRlt.get('taskInfo').get('taskId')
                    taskName = queryRlt.get('taskInfo').get('taskName')
                    Logger.debug(taskId)
                    Logger.debug(taskName)
                    if taskId != 0 and taskName != '':
                        continue
                    return fail(msg="获取已建任务taskId：{0}, taskName: {1}异常".format(str(taskId), taskName))
                queryRltDict = format_query_mo_result(queryRlt)
                for meId in queryRltDict:
                    Logger.debug(queryRltDict)
                    Logger.debug("meId === {0}".format(meId))
                    for cellInfo in queryRltDict[meId]:
                        if not all(key in cellInfo for key in needKeys):
                            return fail(msg="NRPhysicalCellDU的 ManagedElement, SubNetwork, ldn, nrPhysicalCellDUId, userLabel, cellAtt必须包含在查询结果中")
                        me = await MeFactory().create_by_meid(request, meId)
                        mes.append(me)
                        newQueryRlts.append(queryRltDict)
        bodyDict = para.parameters
        Logger.debug(taskId)
        Logger.debug(taskName)
        Logger.debug(para.parameters)
        bodyDict.update(para.parameters)
        if para.templateType == 'GUI':
            rlt, rltInfo = await AapcGui().modify_nr_task(str(taskId), taskName, mes, newQueryRlts, bodyDict)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is modified failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is modified successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/stop_nr_task_action", summary="停止NR天线权值自优化任务",
             description="【功能】停止NR天线权值自优化任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/aapc"}]})
async def stop_nr_task_action(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        Logger.debug(taskInfo)
        mes = await MeFactory.create(request)
        Logger.debug(mes)
        taskId = taskInfo[0].get('taskId')
        rlt, rltInfo = await AapcGui().stop_nr_task(str(taskId), mes)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is stopped failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is stopped successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/active_nr_task_action", summary="激活NR天线权值自优化任务",
             description="【功能】激活NR天线权值自优化任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/aapc"}]})
async def active_nr_task_action(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        Logger.debug(taskInfo)
        mes = await MeFactory.create(request)
        Logger.debug(mes)
        taskId = taskInfo[0].get('taskId')
        rlt, rltInfo = await AapcGui().active_nr_task(str(taskId), mes)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is actived failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is actived successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/delete_nr_task_action", summary="删除NR天线权值自优化任务",
             description="【功能】删除NR天线权值自优化任务【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/aapc"}]})
async def delete_nr_task_action(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        Logger.debug(taskInfo)
        mes = await MeFactory.create(request)
        Logger.debug(mes)
        taskId = taskInfo[0].get('taskId')
        rlt, rltInfo = await AapcGui().delete_nr_task(str(taskId), mes)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='Task is deleted failed')
        return success(data={"taskInfo": rltInfo}, msg='Task is deleted successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/get_nr_collect_mr_count_action", summary="获取NR天线权值自优化任务MR数量",
             description="【功能】获取NR-AAPC任务MR数量【作者】10263798",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/aapc"}]})
async def get_nr_collect_mr_count_action(request: Request):
    try:
        taskInfo = await Refs(request).get_output("taskInfo")
        Logger.debug(taskInfo)
        mes = await MeFactory.create(request)
        Logger.debug(mes)
        taskId = taskInfo[0].get('taskId')
        rlt, rltInfo = await AapcGui().get_nr_collect_mr_count(str(taskId), mes)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='MR is got failed')
        return success(data={"taskInfo": rltInfo}, msg='MR is got successful')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/check_nr_aapc_process_monitor", summary="获取NR-AAPC过程监控日志",
             description="【功能】获取NR-AAPC任务过程监控日志,选择潮汐场景时，需要输入工作日或非工作日时段且只支持查询1个时段，格式按照界面显示填写,必须依赖 创建NR天线权值自优化任务。【作者】00256242",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/aapc"}]})
async def check_nr_aapc_process_monitor(request: Request, para: AapcMonitorReport):
    taskInfo = await Refs(request).get_output("taskInfo")
    mes = await MeFactory.create(request)
    if not taskInfo:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')
    try:
        taskId = taskInfo[0].get('taskId')
        result, taskParameters = await AapcGui().get_task_parameters(str(taskId), mes)
        if not result:
            return fail(data={"errInfo": taskParameters}, msg='任务参数信息获取失败')
        aapcFuncMode = taskParameters.get("srcObj").get("aapcFuncMode")
        daytime = para.tidalTime
        id, logUrl = await AapcGui().get_nr_process_monitor_url(aapcFuncMode, daytime)
        rlt, rltInfo = await AapcGui().check_nr_process_monitor(str(taskId), str(id), str(logUrl), mes)
        Logger.debug(rlt)
        Logger.debug(rltInfo)
        if not rlt:
            return fail(data={"errInfo": rltInfo}, msg='The Query Results is got failed')
        return success(data={"taskInfo": rltInfo}, msg='The Query Results is got successful')
    except Exception as e:
        traceback.print_exc()
        Logger.error(traceback.format_exc())
        return fail("ACTION 执行失败", data=str(e))

@router.post("/display_nr_aapc_execute_result", summary="获取NR-AAPC执行结果",
             description="【功能】获取NR-AAPC执行结果,查看执行结果中的 优化参数 or 机械角调整信息，必须依赖 创建NR天线权值自优化任务。【作者】00256242",
             openapi_extra={"refs": [{"actionName": "创建.*任务", "path": "/v1/api/action/ume/aapc"}]})
async def display_nr_aapc_execute_result(request: Request, para: AapcExecuteResult):
    taskInfo = await Refs(request).get_output("taskInfo")
    mes = await MeFactory.create(request)
    if not taskInfo:
        return fail(data={"errInfo": ''}, msg='上一步查询到的运行日志为空')
    try:
        taskId = taskInfo[0].get('taskId')
        optimizeParameter = para.optimizeParameter
        if optimizeParameter == 2:
            rlt, rltInfo = await AapcGui().display_exceed_limit_prompt(str(taskId), mes)
            if not rlt:
                return fail(data={"errInfo": rltInfo}, msg='机械角调整信息获取失败')
            return success(data={"taskInfo": rltInfo}, msg='机械角调整信息获取成功')
        else:
            result, taskParameters = await AapcGui().get_task_parameters(str(taskId), mes)
            if not result:
                return fail(data={"errInfo": taskParameters}, msg='任务参数信息获取失败')
            aapcFuncMode = taskParameters.get("srcObj").get("aapcFuncMode")
            rlt, activeTime = await AapcGui().get_cell_active_time(str(taskId), mes)
            if not rlt:
                return fail(data={"errInfo": activeTime}, msg='任务激活时间获取失败，请检查任务是否激活成功过')
            result, rltInfo = await AapcGui().display_nr_execute_result(str(taskId), activeTime, str(aapcFuncMode), mes)
            if not result:
                return fail(data={"errInfo": rltInfo}, msg='优化参数信息获取失败')
            return success(data={"taskInfo": rltInfo}, msg='优化参数信息获取成功')
    except Exception as e:
        traceback.print_exc()
        Logger.error(traceback.format_exc())
        return fail("ACTION 执行失败", data=str(e))
