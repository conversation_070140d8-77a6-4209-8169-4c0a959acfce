# -*- encoding: utf-8 -*-
'''
@File    :   Schemas.py
@Time    :   2023/08/25 18:47:44
<AUTHOR>   李海升10227494 马凯10296591
@Version :   1.0
@Contact :   <EMAIL> <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
from enum import Enum

from pydantic import BaseModel, Field

DIRECTION_BI = ""


class DirectionEnum(str, Enum):
    value = "Bidirection"
    value1 = "Unidirection"


class NeighborInfo(BaseModel):
    srcMeId: int
    srcPlmnId: str = Field(None, description='源网元主Plmn, 举例：460-01')
    srcCellIds: str = Field(..., description='源侧小区Id,格式：cellId1,cellId2 举例：31,32',
                            regex=r'\d{1,5},?')
    dstMeId: int
    dstPlmnId: str = Field(None, description='目标网元主Plmn, 举例：460-01')
    dstCellIds: str = Field(..., description='目标侧小区Id, 格式：cellId1,cellId2 举例：41,42',
                            regex=r'\d{1,5},?')
    mode: DirectionEnum
    dataAreaId: str | None = None


class ClearNeighborInfo(BaseModel):
    meId: int
    dataAreaId: str | None = None



class NeighborCellInfo(BaseModel):
    nrcell: str = Field("CELL_1", description="NR小区id别名，在资源选择中选择与查看，默认为CELL_1")
    ltecell: str = Field("CELL_2", description="lte小区id别名，在资源选择中选择与查看，默认为CELL_2")
    dataAreaId: str | None = None

class NeighborLteCellInfo(BaseModel):
    sourceCell: str = Field("CELL_1", description="lte源小区id别名，在资源选择中选择与查看，默认为CELL_1")
    targetCell: str = Field("CELL_2", description="lte目标小区id别名，在资源选择中选择与查看，默认为CELL_2")
    dataAreaId: str | None = None
