# -*- encoding: utf-8 -*-
'''
@File    :   NeighborCell.py
@Time    :   2023/08/25 18:47:44
<AUTHOR>   李海升10227494 马凯10296591
@Version :   1.0
@Contact :   <EMAIL> <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.rancm.neighbor_cell.Schemas import NeighborInfo, DirectionEnum, ClearNeighborInfo, \
    NeighborCellInfo, NeighborLteCellInfo
from api.route.BizRoute import bizRoute
from domain.factories.CellFactory import CellFactory
from domain.factories.MeFactory import MeFactory
from domain.models.ume.rancm.nrcellrelation.CellRelationInfo import CellRelationInfo, TYPE_ADD_RELATION, \
    TYPE_DEL_RELATION
from domain.platform.ActionInfo import description
from service.action.ume.rancm.gnbcucpfunction.LteNeighborRelation import LteNeighborRelation
from service.action.ume.rancm.gnbcucpfunction.NRCellRelationService import NRCellRelationService

router = APIRouter(route_class=bizRoute)


@router.post("/add_relation", summary="添加邻区关系",
             description=description("通过网管添加NR邻区关系，如果PlmnId不填，将会通过cellId匹配整站小区进行邻区添加",
                                     '10227494,10296591'))
async def add_relation(request: Request, para: NeighborInfo) -> dict:
    config = request.state.resource.get("config")
    dataAreaId = para.dataAreaId if para.dataAreaId else config.get("dataAreaId")
    mes = await MeFactory.create(request)
    mode = 0  # 默认为双向邻区
    if para.mode == DirectionEnum.value1:
        mode = 1
    relationInst = CellRelationInfo(para.srcMeId, para.srcPlmnId, para.dstMeId, para.dstPlmnId, mode, para.srcCellIds,
                                    para.dstCellIds, TYPE_ADD_RELATION)
    ret, retMsg = await NRCellRelationService.handle_neighbor_relation(relationInst, mes, dataAreaId)
    if ret:
        return success(msg="Add neighbor cells relation Success.")
    return fail(msg=retMsg)


@router.post("/clear_relation", summary="删除所有邻区关系",
             description=description("通过网管删除所有邻区关系", "10227494,10296591"))
async def clear_relation(request: Request, para: ClearNeighborInfo) -> dict:
    config = request.state.resource.get("config")
    dataAreaId = para.dataAreaId if para.dataAreaId else config.get("dataAreaId")
    mes = await MeFactory.create(request)
    for me in mes:
        if para.meId == int(me.meId):
            ret, retMsg = await NRCellRelationService.delete_all_relations(me, dataAreaId)
            if ret:
                return success(msg="Clear neighbor cells relation Success.")
            return fail(msg=retMsg)
    return fail(msg="No meId Found!")


@router.post("/del_relation", summary="删除邻区关系",
             description=description("通过网管删除NR邻区关系，如果PlmnId不填，将会通过cellId匹配整站小区进行邻区删除",
                                     '10227494,10296591'))
async def del_relation(request: Request, para: NeighborInfo) -> dict:
    config = request.state.resource.get("config")
    dataAreaId = para.dataAreaId if para.dataAreaId else config.get("dataAreaId")
    mes = await MeFactory.create(request)
    mode = 0  # 默认为双向邻区
    if para.mode == DirectionEnum.value1:
        mode = 1
    relationInst = CellRelationInfo(para.srcMeId, para.srcPlmnId, para.dstMeId, para.dstPlmnId, mode, para.srcCellIds,
                                    para.dstCellIds, TYPE_DEL_RELATION)
    ret, retMsg = await NRCellRelationService.handle_neighbor_relation(relationInst, mes, dataAreaId)
    if ret:
        return success(msg="Delete neighbor cells relation Success.")
    return fail(msg=retMsg)


@router.post("/add_nr_lte_relation", summary="添加nr_lte邻区关系",
             description=description("通过网管添加nr小区与lte小区邻区关系", '10255283'))
async def add_relation(request: Request, paras: NeighborCellInfo) -> dict:
    config = request.state.resource.get("config")
    dataAreaId = paras.dataAreaId if paras.dataAreaId else config.get("dataAreaId")
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    nrCellAlias = await CellFactory.create_by_alias(request, paras.nrcell)
    lteCellAlias = await CellFactory.create_by_alias(request, paras.ltecell)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    mes_dict = {mes[i]._meId: mes[i] for i in range(len(mes))}
    nrme = mes_dict[nrCellAlias[0]._me._meId]
    lteme = mes_dict[lteCellAlias[0]._me._meId]
    dataAreaId = dataAreaId if dataAreaId else nrme.dataAreaId
    dataAreaId = dataAreaId if dataAreaId else await nrme.create_default_plan_area()
    ret, retMsg = await LteNeighborRelation.add_eutrancell_relation(nrme, lteme, nrCellAlias[0], lteCellAlias[0],
                                                                    dataAreaId)
    if ret:
        return success(msg="Add neighbor cells relation Success.")
    return fail(msg=retMsg)


@router.post("/delte_nr_lte_relation", summary="删除nr_lte邻区关系",
             description=description("通过网管删除nr小区与lte小区邻区关系", '10255283'))
async def delete_relation(request: Request, paras: NeighborCellInfo) -> dict:
    config = request.state.resource.get("config")
    dataAreaId = paras.dataAreaId if paras.dataAreaId else config.get("dataAreaId")
    mes = await MeFactory.create(request)
    cells = await CellFactory.create(request)
    nrCellAlias = await CellFactory.create_by_alias(request, paras.nrcell)
    lteCellAlias = await CellFactory.create_by_alias(request, paras.ltecell)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    mes_dict = {mes[i]._meId: mes[i] for i in range(len(mes))}
    nrme = mes_dict[nrCellAlias[0]._me._meId]
    lteme = mes_dict[lteCellAlias[0]._me._meId]
    ret, retMsg = await LteNeighborRelation.del_eutrancell_relation(nrme, lteme, nrCellAlias[0], lteCellAlias[0],
                                                                    dataAreaId)
    if ret:
        return success(msg="delete neighbor cells relation Success.")
    return fail(msg=retMsg)


@router.post("/add_lte_lte_relation", summary="添加lte_lte邻区关系",
             description=description("通过网管添加lte小区与lte小区邻区关系", '10255283'))
async def add_lte_relation(request: Request, paras: NeighborLteCellInfo) -> dict:
    config = request.state.resource.get("config")
    dataAreaId = paras.dataAreaId if paras.dataAreaId else config.get("dataAreaId")
    mes = await MeFactory.create_by_lte(request)
    cells = await CellFactory.create(request)
    sourceCell = await CellFactory.create_by_alias(request, paras.sourceCell)
    targetCell = await CellFactory.create_by_alias(request, paras.targetCell)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    mes_dict = {mes[i]._meId: mes[i] for i in range(len(mes))}
    sourceMe = mes_dict[sourceCell[0]._me._meId]
    targetMe = mes_dict[targetCell[0]._me._meId]
    ret, retMsg = await LteNeighborRelation.add_lte_lte_relation(sourceMe, targetMe, sourceCell[0], targetCell[0],
                                                                 dataAreaId)
    if ret:
        return success(msg="Add neighbor cells relation Success.")
    return fail(msg=retMsg)


@router.post("/del_lte_lte_relation", summary="删除lte_lte邻区关系",
             description=description("通过网管删除lte小区与lte小区邻区关系", '10255283'))
async def del_lte_relation(request: Request, paras: NeighborLteCellInfo) -> dict:
    config = request.state.resource.get("config")
    dataAreaId = paras.dataAreaId if paras.dataAreaId else config.get("dataAreaId")
    mes = await MeFactory.create_by_lte(request)
    cells = await CellFactory.create(request)
    sourceCell = await CellFactory.create_by_alias(request, paras.sourceCell)
    targetCell = await CellFactory.create_by_alias(request, paras.targetCell)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    mes_dict = {mes[i]._meId: mes[i] for i in range(len(mes))}
    sourceMe = mes_dict[sourceCell[0]._me._meId]
    targetMe = mes_dict[targetCell[0]._me._meId]
    ret, retMsg = await LteNeighborRelation.del_lte_lte_relation(sourceMe, targetMe, sourceCell[0], targetCell[0],
                                                                 dataAreaId)
    if ret:
        return success(msg="delete neighbor cells relation Success.")
    return fail(msg=retMsg)
