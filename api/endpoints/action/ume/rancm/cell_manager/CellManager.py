from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.rancm.cell_manager.Schemas import paramSuite, hParamSuite
from api.route.BizRoute import bizRoute
from domain.factories.CellFactory import CellFactory
from domain.factories.MeFactory import MeFactory
from domain.models.ume.rancm.SmartCmMoConfig import SmartCmMoConfig
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from infrastructure.utils.ResultFormatter import format_query_mo_result
from service.action.ume.rancm.MoConfigService import MoConfigService

router = APIRouter(route_class=bizRoute)


@router.post("/param_suite_config", summary="ITRAN低频TNR或FNR参数套配置",
             description=description("实现RANCM模块下，小区管理的ITRAN低频TNR或FNR参数套配置功能", "岳昊冉10263798"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-1"}]})
async def param_suite_config(request: Request, paras: paramSuite):
    # 依赖获取
    refs = await Refs(request).get_output()
    refQueryRlts = refs[0] if refs else None
    flattenedRes = [element for sublist in refQueryRlts for element in sublist] if refQueryRlts else None  # 合并多个查询结果
    nrPhysicalCellDUIds = paras.nrPhysicalCellDUId.split(',') if paras.nrPhysicalCellDUId else []
    if flattenedRes:
        for flattenedRe in flattenedRes:
            nrPhysicalCellDUIds.append(flattenedRe.get("nrPhysicalCellDUId"))
    mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList = list()
    for me in mes:
        dataAreaId = paras.dataAreaId if paras.dataAreaId else me.dataAreaId
        if paras.radioMode == 'TDD':
            for nrPhysicalCellDUId in nrPhysicalCellDUIds:
                param = {"nrPhysicalCellDUId": nrPhysicalCellDUId, "ueNumPhyResAdm": paras.ueNumPhyResAdm,
                         "TNR_paramSuite": paras.paramSuiteId}
                res = await SmartCmMoConfig(me, dataAreaId).config_mo_by_template(param, 'update', 'cfgTNRParamSuite',
                                                                                  'NRPhysicalCellDU')
        else:
            param = {"nrPhysicalCellDUId": paras.nrPhysicalCellDUId, "ueNumPhyResAdm": paras.ueNumPhyResAdm,
                     "FNR_paramSuite": paras.paramSuiteId}
            res = await SmartCmMoConfig(me, dataAreaId).config_mo_by_template(param, 'update', 'cfgFNRParamSuite',
                                                                              'NRPhysicalCellDU')
        if res is not True:
            errList.append(res)
    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据")


@router.post("/createMts", summary="ITRAN高频参数配置修改",
             description=description("实现RANCM模块下，小区管理的ITRAN高频参数配置修改功能", "10255283"),
             openapi_extra={
                 "refs": [{"actionName": "查询MO节点属性", "refPattern": "0-*"}]})
async def create_mts_task(request: Request, paras: hParamSuite):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = []
    taskInfo, errList = {}, []
    s = paras.mmWave_paramSuite
    start_index = s.find('[') + 1
    end_index = s.find(']', start_index)
    suiteId = s[start_index:end_index]
    queryRlts = await Refs(request).get_output()
    Logger.debug(f'依赖的action输出:{queryRlts}')
    needKeys = ["meId", "ldn", "subNetWork", "nrPhysicalCellDUId"]
    if queryRlts:
        for queryRlt in queryRlts:
            queryRltDict = format_query_mo_result(queryRlt)
            Logger.debug(queryRltDict)
            for meId in queryRltDict:
                for cellInfo in queryRltDict[meId]:
                    if not all(key in cellInfo for key in needKeys):
                        return fail(msg="NRCellDU的 meId;SubNetwork;ldn;nrPhysicalCellDUId 必须包含在查询结果中")
                    me = await MeFactory().create_by_meid(request, cellInfo['meId'])
                    nrPhysicalCellDUId = cellInfo['nrPhysicalCellDUId']
                    dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
                    tpdata = {"nrPhysicalCellDUId": nrPhysicalCellDUId, "ueNumPhyResAdm": paras.ueNumPhyResAdm,
                              "mmWave_paramSuite": suiteId}
                    res = await SmartCmMoConfig(me, dataAreaId).config_mo_by_template(tpdata, 'update',
                                                                                      'cfgmmWaveParamSuite',
                                                                                      'NRPhysicalCellDU')
                    if res is not True:
                        errList.append(res)

    else:
        mes = await MeFactory.create(request)
        cells = await CellFactory.create(request)
        cellmeIds = set(cell.me.meId for cell in cells)
        mes = list(filter(lambda me: me.meId in cellmeIds, mes))
        nrPhysicalCellDUIds = [int(part) for part in paras.nrPhysicalCellDUId.split(",")]
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        for nrPhysicalCellDUId in nrPhysicalCellDUIds:
            cellinfo = await MoConfigService.query_mo(me, "NRCellDU", ["moId", "ldn"],
                                                      {"nrPhysicalCellDUId": str(nrPhysicalCellDUId)})
            if not cellinfo:
                return fail(msg=f"网元{me.meId}无物理小区nrPhysicalCellDUId={nrPhysicalCellDUId}，请修改并清除规划区")
            tpdata = {"nrPhysicalCellDUId": nrPhysicalCellDUId, "ueNumPhyResAdm": paras.ueNumPhyResAdm,
                      "mmWave_paramSuite": suiteId}
            res = await SmartCmMoConfig(me, dataAreaId).config_mo_by_template(tpdata, 'update', 'cfgmmWaveParamSuite',
                                                                              'NRPhysicalCellDU')
            if res is not True:
                errList.append(res)
    if errList:
        return fail(errList)
    return success(f"物理小区修改成功，请使用'检查并激活配置'激活规划区数据")
