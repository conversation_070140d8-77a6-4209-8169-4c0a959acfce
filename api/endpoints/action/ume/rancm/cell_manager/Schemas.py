from pydantic import BaseModel, Field
from typing import Literal, TypeVar
from enum import Enum

DataT = TypeVar("DataT")


class mmWaveTypeEnum(str, Enum):
    e200 = "mmWave_singlebwp_DDDSU_200M[200]"
    e201 = "mmWave_singlebwp_DSSSU_100M[201]"
    e202 = "mmWave_singlebwp_DDDSU_100M[202]"
    e203 = "mmWave_singlebwp_DSSSU_200M[203]"
    e204 = "mmWave_dualbwp_DDDSU_200M_200M+200M[204]"
    e205 = "mmWave_dualbwp_DSSSU_200M_200M + 200M[205]"
    e206 = "mmWave_dualbwp_DDDSU_100M_100M + 100M[206]"
    e207 = "mmWave_dualbwp_DSSSU_100M_100M + 100M[207]"


class paramSuite(BaseModel):
    nrPhysicalCellDUId: str = Field(..., description='小区Id, 格式：cellId,举例：31,32',
                                    regex=r'\d{1,5},?')
    radioMode: Literal["TDD", "FDD"] = Field("TDD", description='选择基站制式为TDD或FDD')
    paramSuiteId: int | None
    ueNumPhyResAdm: int | None = 200
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")


class hParamSuite(BaseModel):
    ueNumPhyResAdm: Literal["50", "200", "400", "800", "undefined"] = Field("200", description='物理资源支持的用户数')
    mmWave_paramSuite: Literal[mmWaveTypeEnum.e200, mmWaveTypeEnum.e201, mmWaveTypeEnum.e202, mmWaveTypeEnum.e203,
    mmWaveTypeEnum.e204, mmWaveTypeEnum.e205, mmWaveTypeEnum.e206, mmWaveTypeEnum.e207] = Field(mmWaveTypeEnum.e200,
                                                                                                description='高频参数套配置')
    nrPhysicalCellDUId: str | None = Field(description='物理小区DU标志, 可填写多个，示例：31,32,当依赖查询MO节点属性可以不填写，依赖后该配置不生效')
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
