# @Time    : 2023/7/7 14:22
# <AUTHOR> 10111815
import traceback

from fastapi import APIRouter, Request

from api.Response import fail, success
from api.endpoints.action.ume.rancm.data_baseline_management.schemas import CommercialParaByErms, ConfigParaByErms, \
    BaselineCmp
from api.route.BizRoute import bizRoute
from domain.factories.CellFactory import CellFactory
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger
from service.action.ume.rancm.data_baseline_management.BaselineComparisonService import BaselineComparisonService
from service.action.ume.rancm.data_baseline_management.ParameterBaselineService import ParameterBaselineService

router = APIRouter(route_class=bizRoute)


@router.post("/flash_commercial_para", summary="商用基线刷参",
             description=description("通过ose刷商用基线,支持指定GNB刷参", "侯小飞10270755"))
async def flash_commercial_para(request: Request, para: CommercialParaByErms):
    mes = await MeFactory.create(request)
    if not mes:
        return fail(msg=f"当前无满足条件网元，请检查：1.资源过滤后环境信息中是否存在网元!")
    infos = set()
    planned_area_id = await ParameterBaselineService.get_data_area_id(mes)
    data = {"规划区ID": planned_area_id, "网元": ",".join([str(me.meId) for me in mes])}
    for me in mes:
        await me.delete_plan_area_data(planned_area_id)
        infos.add((me.subNetwork, me.meId, None, None))
    try:
        result, msg, taskname = await ParameterBaselineService.brush_para(mes[0], para.path, infos, planned_area_id)
        data.update({"任务名称": taskname})
        if result and msg: return success(f"{msg}", data=data)
        if result:
            dv_result, msg = await ParameterBaselineService.brush_dv_para(mes, para.path)
            if dv_result: return success(f"商用基线刷参成功{msg}", data=data)
        return fail(f"商用基线刷参失败，结果详情：{msg}", data=data)
    except BaseException as err:
        Logger.error(f"商用基线刷参过程中执行失败,详情:", traceback.format_exc())
        return fail(f"商用基线刷参过程中执行失败,原因:{err}", data=data)


@router.post("/flash_cfg_para", summary="用例配置刷参",
             description=description("ose用例配置刷参，支持指定小区、GNB刷参，支持LTE", "侯小飞10270755"))
async def flash_cfg_para(request: Request, para: ConfigParaByErms):
    resource = request.state.resource
    system_id = resource.get("systemId")
    config = resource.get("config")
    cells = await CellFactory.create(request)
    mes = await MeFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    if not mes:
        return fail(msg=f"当前无满足条件网元，请检查：1.资源过滤后环境信息中是否存在网元!")
    flag = ParameterBaselineService.judege_is_brush_cellinfos(config)
    planned_area_id = await ParameterBaselineService.get_data_area_id(mes)
    data = {"规划区ID": planned_area_id, "网元": ",".join([str(me.meId) for me in mes])}
    infos = ParameterBaselineService.format_cell_info(cells, flag)
    for me in mes:
        await me.delete_plan_area_data(planned_area_id)
    try:
        result, msg, taskname = await ParameterBaselineService.brush_para(mes[0], para.path, infos, planned_area_id,
                                                                          system_id)
        data.update({"任务名称": taskname})
        if result:
            if msg: return fail(f"用例刷参结束，但结果为空，请核对用例参数模板是否和所选网元匹配", data=data)
            return success(f"用例刷参成功", data=data)
        return fail(f"用例刷参失败，结果详情：{msg}", data=data)
    except BaseException as err:
        Logger.error(f"用例刷参过程中执行失败,详情:", traceback.format_exc())
        return fail(f"用例刷参过程中执行失败,原因:{err}", data=data)


@router.post("/baseline_comparison", summary="基线比较",
             description=description("网管数据基线管理中的基线比较功能,返回比较结果相同或不同", "刘伟10255257"))
async def baseline_comparison(request: Request, para: BaselineCmp):
    config = request.state.resource.get("config")
    mes = await MeFactory.create(request)
    dataAreaId = para.dataAreaId if para.dataAreaId else config.get("dataAreaId")
    retDict = {}
    for me in mes:
        ret = await BaselineComparisonService().get_baseline_compare_result(me, para.baselineParameter, dataAreaId)
        if ret:
            retDict.update({me.meId: ret})
        else:
            return fail(f'meId:{me.meId} 基线比较失败')
    return success('比较结果', data=retDict)


@router.post("/set_baseline", summary="刷版本基线",
             description=description("网管数据基线管理中的基线比较结果不同则刷参，相同则什么也不做", "刘伟10255257"))
async def set_baseline(request: Request, para: BaselineCmp):
    config = request.state.resource.get("config")
    mes = await MeFactory.create(request)
    dataAreaId = para.dataAreaId if para.dataAreaId else config.get("dataAreaId")
    for me in mes:
        ret = await BaselineComparisonService().set_baseline(me, para.baselineParameter, dataAreaId)
        if not ret:
            return fail(f'meId:{me.meId} 版本基线刷参失败')
    return success('版本基线刷参成功')
