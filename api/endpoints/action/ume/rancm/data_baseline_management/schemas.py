#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/7/13 15:25
# <AUTHOR> 10111815
import os
from typing import TypeVar
from enum import Enum

from pydantic import BaseModel, Field, validator

DataT = TypeVar('DataT')  # 创建一个 TypeVar 的实例


class CommercialParaByErms(BaseModel):
    path: DataT = Field(..., type="url", source="tdl", returnType="array", viewType="tree", params=[],
                        url="https://zxmte.zte.com.cn:3303/tdl/other/sftp_dir_tree?path=/sftpuser/商用基线")

    @validator("path")
    def attrs_validator(cls, v):
        if os.path.splitext(v)[-1] not in ['.zip', '.ZIP', '.xlsx', '.XLSX']:
            raise ValueError("输入的文件不正确，请确认选择的是zip或者xlsx文件")
        return v

class ConfigParaByErms(BaseModel):
    path: DataT = Field(..., type="func", source="tdl", returnType="array", viewType="select", params=[],
                        func="service.platform.param_filler.TestcaseService.TestcaseService.query_para_file_paths")

    @validator("path")
    def attrs_validator(cls, v):
        if os.path.splitext(v)[-1] not in ['.zip', '.ZIP', '.xlsx', '.XLSX']:
            raise ValueError("输入的文件不正确，请确认选择的是zip或者xlsx文件")
        return v

class BaselineName(str, Enum):
    value1 = "5GNRParameterBaseline"
    value2 = "5GNRHFParameterBaseline"
    value3 = "DualBwpAndDSS"
    value4 = "5GNRHFTUE"

class BaselineCmp(BaseModel):
    baselineParameter: BaselineName
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")