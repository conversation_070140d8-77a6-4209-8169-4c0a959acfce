# -*- encoding: utf-8 -*-
'''
@File    :   ModifyParamsuite.py
@Time    :   2023/08/18 18:47:44
<AUTHOR>   李海升10227494 马凯10296591
@Version :   1.0
@Contact :   <EMAIL> <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
from typing import Dict
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.endpoints.action.ume.rancm.paramsuite.Schemas import ParamsuiteInfo
from api.Response import fail, success
from domain.factories.CellFactory import CellFactory
from domain.factories.MeFactory import MeFactory
from domain.models.ume.rancm.multibwpconfig.MultiBwpConfig import MultiBwpConfig

router = APIRouter(route_class=bizRoute)


@router.post("/modify", summary="刷参数套", description="【功能】通过网管刷参数套【作者】10227494,10296591,支持通过资源选择目标小区进行刷参")
async def modify_paramsuite(request: Request, para: ParamsuiteInfo) -> Dict:
    taskInfo, successFlag = {}, True
    config = request.state.resource.get("config")
    dataAreaId = para.dataAreaId if para.dataAreaId else config.get("dataAreaId")
    mes = [await MeFactory.create_by_meid(request, para.meId)] if para.meId else await MeFactory.create(request)
    cells = await CellFactory.create(request)
    cellmeIds = set((cell.me.meId, hasattr(cell, 'gnb')) for cell in cells)
    mes = list(filter(lambda me: (me.meId, me.serviceType.lower() == "5gnr") in cellmeIds, mes))
    if not mes:
        return fail(msg="未找到匹配的网元，请确认界面输入的meId和资源中的小区资源或者GNB资源是否匹配")
    for me in mes:
        multiBwpConfig = MultiBwpConfig(me, para.radioMode, dataAreaId) if dataAreaId \
            else await MultiBwpConfig.with_default_plan_area(me, para.radioMode)
        meCells = list(filter(lambda cell: (cell.me.meId, hasattr(cell, 'gnb')) == (me.meId, me.serviceType.lower()=="5gnr"), cells))
        ret, retMsg = await multiBwpConfig.modify_paramsuite(para.cellIds or meCells, para.paramsuiteId, para.ueNumPhyResAdm)
        if ret:
            taskInfo.update({me.meId: ret})
        else:
            successFlag = False
            taskInfo.update({me.meId: retMsg})
    if successFlag:
        return success(msg="Modify BWP paramsuite Success.", data=taskInfo)
    return fail(msg="Modify BWP paramsuite Fail.", data=taskInfo)
