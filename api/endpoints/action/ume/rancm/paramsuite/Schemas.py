from enum import Enum

from pydantic import BaseModel, Field


class RadioModeEnum(str, Enum):
    value = "TDD"
    value1 = "FDD"
    value2 = "HF_TDD"


class ParamsuiteInfo(BaseModel):
    meId: int | None = Field(None, description='网元ID,非必填；该参数不填时,将使用资源中的小区信息来查找对应网元')
    cellIds: str | None = Field(None, description='小区Id, 格式：cellId1,cellId2 举例：31,32，非必填；该参数不填时,将使用资源中的小区进行刷参',
                        regex=r'^\s*$|\d{1,5},?')
    radioMode: RadioModeEnum
    paramsuiteId: int
    ueNumPhyResAdm: int = 200
    dataAreaId: str | None = None
