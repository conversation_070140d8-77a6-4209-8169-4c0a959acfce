import traceback

from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from infrastructure.logger import Logger
from infrastructure.resource.service.Refs import Refs
from service.action.ume.rancm.MoConfigService import MoConfigService
from api.endpoints.action.ume.rancm.mo.Schemas import VirtualCellRealtion, MoExportFile, MoImportFile
from api.Response import fail, success
from domain.factories.MeFactory import MeFactory
from domain.factories.CellFactory import CellFactory
from domain.platform.ActionInfo import description
from service.action.ume.rancm.data_baseline_management.ParameterBaselineService import ParameterBaselineService

router = APIRouter(route_class=bizRoute)


@router.post("/virtual_cell_relation", summary="虚配邻区关系",
             description=description("在网管RANCM中通过导出模板，并生成满配邻区关系模板后导入，实现虚配邻区关系",
                                     "侯小飞10270755"))
async def virtual_cell_relation(request: Request, para: VirtualCellRealtion):
    resource = request.state.resource
    config = resource.get("config")
    cells = await CellFactory.create(request)
    mes = await MeFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    if not mes:
        return fail(msg=f"当前无满足条件网元，请检查：1.资源过滤后环境信息中是否存在网元!")
    flag = ParameterBaselineService.judege_is_brush_cellinfos(config)

    result = {}
    for me in mes:
        try:
            await MoConfigService.mass_config_mo_by_import_temlate(me, cells, para.func)
            result.update({me.meId: "成功"})
        except BaseException as err:
            result.update({me.meId: f"失败,详情:{err}"})
            Logger.error(f"虚配邻区关系失败，详情:", traceback.format_exc())
            return fail(msg=f"虚配邻区关系失败", data=result)
    return success(msg=f"虚配邻区关系成功", data=result)


@router.post("/mo_export_template", summary="MO编辑器导出模板",
             description=description("在网管RANCM中通过导出模板文件", "侯小飞10270755"))
async def mo_export_template(request: Request, para: MoExportFile):
    mes = await MeFactory.create(request)
    if not mes:
        return fail(msg=f"当前无满足条件网元，请检查：1.资源过滤后环境信息中是否存在网元!")
    result = {}
    for me in mes:
        try:
            file = await MoConfigService.mo_export_template(me, para.mocNames, para.onlyMandatory, para.dataAreaId)
            result.update({me.meId: file})
        except BaseException as err:
            result.update({me.meId: f"失败,详情:{err}"})
            Logger.error(f"MO编辑器导出模板文件失败，详情:", traceback.format_exc())
            return fail(msg=f"MO编辑器导出模板文件失败", data=result)
    return success(msg=f"MO编辑器导出模板成功", data=result)


@router.post("/mo_import_template", summary="MO编辑器导入模板",
             description=description("在网管RANCM中通过导入模板文件","侯小飞10270755"),
             openapi_extra={
                 "refs": [{"actionName": "MO编辑器导出模板", "refPattern": "0-1"}]})
async def mo_import_template(request: Request, para: MoImportFile):
    mes = await MeFactory.create(request)
    fileInfos = await Refs(request).get_output()
    if not mes:
        return fail(msg=f"当前无满足条件网元，请检查：1.资源过滤后环境信息中是否存在网元!")
    result = {}
    for me in mes:
        try:
            if para.filePath:
                filePath = para.filePath[0]
            else:
                if not fileInfos:return fail(msg=f"必须输入导入模板文件或者通过依赖'MO编辑器导出文件'")
                filePath = fileInfos[0].get(me.meId)
            await MoConfigService.mo_import_template(me, filePath, para.dataAreaId, para.isActive)
            result.update({me.meId: "成功"})
        except BaseException as err:
            result.update({me.meId: f"失败,详情:{err}"})
            Logger.error(f"MO编辑器导入模板文件失败，详情:", traceback.format_exc())
            return fail(msg=f"MOMO编辑器导入模板失败", data=result)
    return success(msg=f"MO编辑器导入模板成功", data=result)