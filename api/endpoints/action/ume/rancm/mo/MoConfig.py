import json
import traceback

from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from service.action.ume.rancm.MoConfigService import MoConfigService
from domain.models.ume.rancm.SmartCmMoConfig import SmartCmMoConfig
from domain.models.ume.nrsupercell.NrSuperCell import NrSuperCell
from api.endpoints.action.ume.rancm.mo.Schemas import CreateInfo, UpdateInfo, GetInfo, DeleteInfo, CommitInfo, \
    ClearSpecifiedMocRecords, GetCurrInfo, CellManageParas, DelAreaParas, AdjustCellParas, SuperCellParas, \
    SplitSuperCellParas, SplitSuperMIMOCellParas, ReplaceBoard, BatchCreateInfo, BatchUpdateInfo, BatchDeleteInfo, \
    NeChangeHistory

from api.endpoints.action.ume.rancm.mo.MoConfigVO import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ete<PERSON>
from api.Response import fail, success
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from domain.factories.CellFactory import CellFactory
from infrastructure.logger import Logger
from domain.models.ume.rancm.MoConfig import MoConfig
from infrastructure.utils.DataDealer import unique_list
from service.action.ume.rancm.DataCheckService import DataCheckService

router = APIRouter(route_class=bizRoute)


@router.post("/create", summary="创建MO节点",
             description=description("在网管规划区创建mo节点，可通过 [检查并激活配置] 激活到现网区",
                                     "许王禾子 10333721"))
async def create(request: Request, para: CreateInfo):
    # 参数校验
    try:
        createMo = CreateMO(para.dict())
    except Exception as err:
        return fail(msg=f"入参格式有误，err:{err}")
    # 环境校验
    if createMo.meId:
        me = await MeFactory.create_by_meid(request, createMo.meId)
        mes = [me] if me is not None else []
    else:
        mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")

    errList, succList = list(), list()
    for me in mes:
        dataAreaId = createMo.dataAreaId if createMo.dataAreaId else me.dataAreaId
        res, err = await MoConfigService.create_mo(me, createMo.mocName, createMo.attrs, createMo.keyMoPath, dataAreaId, createMo.templateType)
        if err:
            errList.append(f"create mo failed, me: {me.meId}, reason: {err}")
        elif not res:
            errList.append(f"create mo failed, me: {me.meId}")
        else:
            succList.append(f"create mo success, me: {me.meId}")
    if errList:
        return fail(errList + succList)
    return success(msg=succList)


@router.post("/batch_create", summary="批量创建MO节点",
             description=description("在网管规划区批量创建mo节点", "许王禾子 10333721"))
async def batch_create(request: Request, createMo: BatchCreateInfo):

    mes = [await MeFactory.create_by_meid(request, createMo.meId)] if createMo.meId else await MeFactory.create(request)
    if not mes or mes == [None]:
        return fail(msg="无满足条件网元，请检查环境配置或 meId！")

    errList, succList = list(), list()
    for me in mes:
        dataAreaId = createMo.dataAreaId if createMo.dataAreaId else me.dataAreaId
        res, err = await MoConfigService.batch_create_mo(me, createMo.moDatas, createMo.mocName, createMo.keyMoPath, dataAreaId)
        if err:
            errList.append(f"create mo failed, me: {me.meId}, reason: {err}")
        elif not res:
            errList.append(f"create mo failed, me: {me.meId}")
        else:
            succList.append(f"create mo success, me: {me.meId}")
    if errList:
        return fail(errList + succList)
    return success(msg=succList)


@router.post("/get", summary="查询MO节点属性",
             description=description("通过网管查询mo节点规划区的属性", "许王禾子 10333721 刘伟10255257"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-1"}]})
async def get(request: Request, para: GetInfo):
    # 参数校验
    try:
        getMo = GetMO(para.dict())
    except Exception as err:
        return fail(msg=f"入参格式有误，err:{err}")
    # 依赖获取
    refs = await Refs(request).get_output()
    refQueryRlts = refs[0] if refs else None
    flattenedRes = unique_list(
        [element for sublist in refQueryRlts for element in sublist]) if refQueryRlts else None  # 合并多个查询结果

    # 环境校验
    if getMo.meId:
        me = await MeFactory.create_by_meid(request, getMo.meId)
        mes = [me] if me is not None else []
    else:
        mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")

    # 配置了依赖，根据依赖结果去处理
    if flattenedRes:
        Logger.info("query by refs")
        results, errList = await MoConfigService.query_mo_by_ref(mes, getMo, flattenedRes)
    # 配置了小区，按小区去处理
    elif cells := await CellFactory.create_by_filter(request):
        Logger.info("query by cell config")
        results, errList = await MoConfigService.query_mo_by_cell(cells, mes, getMo)
    # 按基站去处理
    else:
        Logger.info("query by basic")
        results, errList = await MoConfigService.query_mo_basic(mes, getMo)

    if errList:
        return fail(errList)

    results = unique_list(results)

    return success(data=results)


@router.post("/get_curr", summary="查询MO节点属性(现网区)",
             description=description("通过网管查询mo节点现网区的属性", "许王禾子 10333721"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-1"}]})
async def get_curr(request: Request, para: GetCurrInfo):
    # 参数校验
    try:
        getMo = GetMO(para.dict(), curr=True)
    except Exception as err:
        return fail(msg=f"入参格式有误，err:{err}")
    # 依赖获取
    refs = await Refs(request).get_output()
    refQueryRlts = refs[0] if refs else None
    flattenedRes = unique_list(
        [element for sublist in refQueryRlts for element in sublist]) if refQueryRlts else None  # 合并多个查询结果

    # 环境校验
    if getMo.meId:
        me = await MeFactory.create_by_meid(request, getMo.meId)
        mes = [me] if me is not None else []
    else:
        mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")

    # 配置了依赖，根据依赖结果去处理
    if flattenedRes:  # 1.配置了依赖
        Logger.info("query by refs")
        results, errList = await MoConfigService.query_mo_by_ref(mes, getMo, flattenedRes)
    # 配置了小区，按小区去处理
    elif cells := await CellFactory.create_by_filter(request):
        Logger.info("query by cell config")
        results, errList = await MoConfigService.query_mo_by_cell(cells, mes, getMo)
    # 按基站去处理
    else:
        Logger.info("query by basic")
        results, errList = await MoConfigService.query_mo_basic(mes, getMo)

    if errList:
        return fail(errList)

    results = unique_list(results)

    return success(data=results)


@router.post("/update", summary="修改MO节点属性",
             description=description("在网管规划区修改mo节点的属性，可通过 [检查并激活配置] 激活到现网区", \
                                     "许王禾子 10333721 刘伟10255257"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-1"}]})
async def update(request: Request, para: UpdateInfo):
    # 参数校验
    try:
        updateMo = UpdateMO(para.dict())
    except Exception as err:
        return fail(msg=f"入参格式有误，err:{err}")
    # 依赖获取
    refs = await Refs(request).get_output()
    refQueryRlts = refs[0] if refs else None
    flattenedRes = unique_list(
        [element for sublist in refQueryRlts for element in sublist]) if refQueryRlts else None  # 合并多个查询结果
    # 环境校验
    if updateMo.meId:
        me = await MeFactory.create_by_meid(request, updateMo.meId)
        mes = [me] if me is not None else []
    else:
        mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")

    errList, succList = list(), list()
    for me in mes:
        dataAreaId = updateMo.dataAreaId if updateMo.dataAreaId else me.dataAreaId
        res, err = await MoConfigService.update_mo(me, updateMo.mocName, updateMo.attrs, updateMo.filter,
                                                   updateMo.keyMoPath,
                                                   dataAreaId, flattenedRes, updateMo.templateType)
        if err:
            errList.append(f"update mo failed, me: {me.meId}, reason: {err}")
        elif not res:
            errList.append(f"update mo failed, me: {me.meId}, please check log")
        else:
            succList.append(f"update mo success, me: {me.meId}")
    if errList:
        return fail(errList + succList)
    return success(msg=succList)


@router.post("/batch_update", summary="批量修改MO节点",
             description=description("在网管规划区批量修改mo节点", "许王禾子 10333721"))
async def batch_update(request: Request, updateMo: BatchUpdateInfo):

    mes = [await MeFactory.create_by_meid(request, updateMo.meId)] if updateMo.meId else await MeFactory.create(request)
    if not mes or mes == [None]:
        return fail(msg="无满足条件网元，请检查环境配置或 meId！")

    errList, succList = list(), list()
    for me in mes:
        dataAreaId = updateMo.dataAreaId if updateMo.dataAreaId else me.dataAreaId
        res, err = await MoConfigService.batch_update_mo(me, updateMo.moDatas, updateMo.mocName, updateMo.keyMoPath, dataAreaId)
        if err:
            errList.append(f"update mo failed, me: {me.meId}, reason: {err}")
        elif not res:
            errList.append(f"update mo failed, me: {me.meId}")
        else:
            succList.append(f"update mo success, me: {me.meId}")
    if errList:
        return fail(errList + succList)
    return success(msg=succList)


@router.post("/delete", summary="删除MO节点",
             description=description("在网管规划区删除mo节点，可通过 [检查并激活配置] 激活到现网区",
                                     "许王禾子 10333721 刘伟10255257"),
             openapi_extra={"refs": [{"actionName": "查询MO节点属性", "refPattern": "0-1"}]})
async def delete(request: Request, para: DeleteInfo):
    # 参数校验
    try:
        deleteMo = DeleteMO(para.dict())
    except Exception as err:
        return fail(msg=f"入参格式有误，err:{err}")
    # 依赖获取
    refs = await Refs(request).get_output()
    refQueryRlts = refs[0] if refs else None
    flattenedRes = unique_list(
        [element for sublist in refQueryRlts for element in sublist]) if refQueryRlts else None  # 合并多个查询结果
    # 环境校验
    if deleteMo.meId:
        me = await MeFactory.create_by_meid(request, deleteMo.meId)
        mes = [me] if me is not None else []
    else:
        mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")

    errList, succList = list(), list()
    for me in mes:
        dataAreaId = deleteMo.dataAreaId if deleteMo.dataAreaId else me.dataAreaId
        res, err = await MoConfigService.delete_mo(me, deleteMo.mocName, deleteMo.filter, deleteMo.keyMoPath,
                                                   dataAreaId, flattenedRes)
        if err:
            errList.append(f"delete mo failed, me: {me.meId}, reason: {err}")
        elif not res:
            errList.append(f"delete mo failed, me: {me.meId}, please check log")
        else:
            succList.append(f"delete mo success, me: {me.meId}")
    if errList:
        return fail(errList + succList)
    return success(msg=succList)


@router.post("/batch_delete", summary="批量删除MO节点",
             description=description("在网管规划区批量删除mo节点", "许王禾子 10333721"))
async def batch_delete(request: Request, updateMo: BatchDeleteInfo):

    mes = [await MeFactory.create_by_meid(request, updateMo.meId)] if updateMo.meId else await MeFactory.create(request)
    if not mes or mes == [None]:
        return fail(msg="无满足条件网元，请检查环境配置或 meId！")

    errList, succList = list(), list()
    for me in mes:
        dataAreaId = updateMo.dataAreaId if updateMo.dataAreaId else me.dataAreaId
        res, err = await MoConfigService.batch_delete_mo(me, updateMo.moDatas, updateMo.mocName, updateMo.keyMoPath, dataAreaId)
        if err:
            errList.append(f"delete mo failed, me: {me.meId}, reason: {err}")
        elif not res:
            errList.append(f"delete mo failed, me: {me.meId}")
        else:
            succList.append(f"delete mo success, me: {me.meId}")
    if errList:
        return fail(errList + succList)
    return success(msg=succList)


@router.post("/commit_data", summary="检查并激活配置",
             description=description("通过网管检查并激活规划区的配置", "许王禾子 10333721"))
async def commit_data(request: Request, para: CommitInfo):
    dataAreaId = para.dataAreaId if para.dataAreaId else ""
    if para.meId:
        me = await MeFactory.create_by_meid(request, para.meId)
        mes = [me] if me is not None else []
    else:
        mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList, succList = list(), list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        res, err = await MoConfigService.commit_data(me, dataAreaId, wait_time=para.waitTime)
        if err:
            errList.append(f"commit data failed, me: {me.meId}, reason: {err}")
        elif not res:
            errList.append(f"commit data failed, me: {me.meId}, please check log")
        else:
            succList.append(f"commit data success, me: {me.meId}")
    if errList:
        return fail(errList + succList)
    return success(msg=succList)


@router.post("/clear_specified_moc_datas", summary="一键删除指定mo节点数据",
             description=description("一键删除指定mo节点数据,如：NRCellRelation", 10263798))
async def clear_specified_moc_records(request: Request, para: ClearSpecifiedMocRecords):
    dataAreaId = para.dataAreaId if para.dataAreaId else ""
    if para.meId:
        me = await MeFactory.create_by_meid(request, para.meId)
        mes = [me] if me is not None else []
    else:
        mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        mocNames = para.mocNames.split(',')
        for mocName in mocNames:
            await MoConfigService.close_plan_area(me, dataAreaId)
            await MoConfigService.open_plan_area(me, dataAreaId)
            await MoConfigService.delete_mo_all_datas(me, mocName, dataAreaId)
            res, err = await MoConfigService.commit_data(me, dataAreaId)
            if err:
                errList.append(str(err))
    if errList:
        return fail(errList)
    return success()


@router.post("/cell_expansion", summary="ITRAN制式开通扩容",
             description=description("实现RANCM模块下，小区管理的ITRAN制式开通扩容(NR)功能，增删改查NRCell下配置",
                                     "10255283"))
async def cell_manage_config(request: Request, paras: CellManageParas):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList, result = list(), list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        if paras.moOp.lower() == "query":
            res = await SmartCmMoConfig(me, dataAreaId).screen_cell_paras(paras.cellParas, paras.cellParas.get("subClass", "NRCell"),
                                                                          "cfgRadioNet_NR")
        else:
            res = await SmartCmMoConfig(me, dataAreaId).config_mo_by_template(paras.cellParas, paras.moOp)
        if not res:
            errList.append(res)
        if isinstance(res, list):
            result.append(res)

    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据", result)


@router.post("/adjust_cell_parameters", summary="ITRAN调整小区参数",
             description=description("实现RANCM模块下，小区管理的调整小区参数功能", "10255283"))
async def adjust_cell_parameters(request: Request, paras: AdjustCellParas):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        cellPara = await SmartCmMoConfig(me, dataAreaId).screen_cell_paras(paras.Primarykey, paras.MoNode)
        if not cellPara:
            return fail(msg="无满足条件过滤参数，请检查配置Primarykey！")
        cellparanum = len(cellPara[0])
        cellPara[0].update(paras.cellParas)
        if len(cellPara[0]) > cellparanum:
            return fail(msg="入参cellParas的键值不存在，请修改")
        res = await SmartCmMoConfig(me, dataAreaId).config_mo_by_template(cellPara[0], "update", "setcellpara",
                                                                          paras.MoNode)
        if res is not True:
            errList.append(res)
    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据")


@router.post("/delete_planned_area_datas", summary="清除规划区数据",
             description=description("实现清除规划区数据功能", "10255283"))
async def delete_planned_area(request: Request, paras: DelAreaParas):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        res, err = await MoConfigService.delete_plan_area_data(me, dataAreaId)
        if res is not True:
            errList.append(err)
    if errList:
        return fail(errList)
    return success("规划区数据删除成功")


@router.post("/nr_super_cell", summary="组合NR超级小区",
             description=description("实现RANCM模块下，组合Nr超级小区功能，需要在资源选择过滤主辅小区", "10255283"))
async def create_nr_super_cell(request: Request, paras: SuperCellParas):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create_by_nr(request)
    cells = await CellFactory.create(request)
    masterCellAlias = await CellFactory.create_by_alias(request, paras.masterCell)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    if len(masterCellAlias) != 1:
        return fail(msg="主小区只能填一个，请修改资源选择")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        res = await NrSuperCell(me, dataAreaId).create_super_cell(cells, masterCellAlias[0], paras.nrCarrierLdnList)
        if res is not True:
            errList.append(res)
    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据")


@router.post("/split_nr_super_cell", summary="拆分NR超级小区",
             description=description("实现RANCM模块下，拆分Nr超级小区功能，需要在资源选择过滤主小区", "10255283"))
async def split_nr_super_cell(request: Request, paras: SplitSuperCellParas):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create_by_nr(request)
    cells = await CellFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        for i in range(len(cells)):
            res = await NrSuperCell(me, dataAreaId).split_super_cell(cells[i])
            if res is not True:
                errList.append(res)
    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据")


@router.post("/nr_super_MIMO_cell", summary="组合SuperMIMO小区",
             description=description("实现RANCM模块下，组合SuperMIMO小区功能，需要在资源选择过滤主辅小区", "10255283"))
async def create_nr_super_MIMO_cell(request: Request, paras: SuperCellParas):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create_by_nr(request)
    cells = await CellFactory.create(request)
    masterCellAlias = await CellFactory.create_by_alias(request, paras.masterCell)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    if len(masterCellAlias) != 1:
        return fail(msg="主小区只能填一个，请修改资源选择")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        res = await NrSuperCell(me, dataAreaId).create_dmimo_super_cell(cells, masterCellAlias[0],
                                                                        paras.nrCarrierLdnList)
        if res is not True:
            errList.append(res)
    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据")


@router.post("/split_nr_super_MIMO_cell", summary="拆分SuperMIMO小区",
             description=description("实现RANCM模块下，组合SuperMIMO小区功能，需要在资源选择过滤主辅小区", "10255283"))
async def split_nr_super_MIMO_cell(request: Request, paras: SplitSuperMIMOCellParas):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create_by_nr(request)
    cells = await CellFactory.create(request)
    cellmeIds = set(cell.me.meId for cell in cells)
    mes = list(filter(lambda me: me.meId in cellmeIds, mes))
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        for i in range(len(cells)):
            res = await NrSuperCell(me, dataAreaId).split_dmimo_super_cell(cells[i], paras.independent)
            if res is not True:
                errList.append(res)
    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据")


@router.post("/replace_board", summary="ITRAN单板替换",
             description=description("实现RANCM模块下，ITRAN单板替换功能", "10255283"))
async def replace_board_ITRAN(request: Request, paras: ReplaceBoard):
    dataAreaId = paras.dataAreaId if paras.dataAreaId else ""
    mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    errList = list()
    for me in mes:
        dataAreaId = dataAreaId if dataAreaId else me.dataAreaId
        dataAreaId = dataAreaId if dataAreaId else await me.create_default_plan_area()
        GetPara = await SmartCmMoConfig(me, dataAreaId).screen_cell_paras(paras.DeviceId, "ReplaceableUnit",
                                                                           "replaceBoard_ITRAN")
        if not GetPara:
            return fail(msg="无满足条件过滤参数，请检查配置Primarykey！")
        cellparanum = len(GetPara[0])
        GetPara[0].update(paras.DeviceParas)
        if len(GetPara[0]) > cellparanum:
            return fail(msg="入参cellParas的键值不存在，请修改")
        res = await SmartCmMoConfig(me, dataAreaId).config_mo_by_template(GetPara[0], "update", "replaceBoard_ITRAN",
                                                                          "ReplaceableUnit")
        if res is not True:
            errList.append(res)
    if errList:
        return fail(errList)
    return success("小区修改成功，请使用{检查并激活配置}激活规划区数据")


@router.post("/export_ne_change_history", summary="导出网元变更历史",
             description=description("实现RANCM模块下，导出网元变更历史", "10270755"))
async def replace_board_ITRAN(request: Request, paras: NeChangeHistory):
    mes = await MeFactory.create(request)
    successFlag, result = True, {}
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    for me in mes:
        try:
            ret = await DataCheckService.export_ne_change_history(me, paras.period, paras.startTime, paras.endTime)
            result.update({me.meId: ret})
        except BaseException as err:
            Logger.error(f"网元：{me.meId} 导出网元变更历史失败，详情:", traceback.format_exc())
            successFlag = False
            result.update({me.meId: f"{err}"})
    if successFlag:
        return success(data=result, msg="导出网元变更历史成功")
    return fail(msg=f"导出网元变更历史失败", data=result)


@router.post("/query_ume_time", summary="获取网管时间",
             description=description("获取当前网管时间", "10270755"))
async def replace_board_ITRAN(request: Request):
    mes = await MeFactory.create(request)
    if len(mes) == 0:
        return fail(msg="无满足条件网元，请检查环境配置或meId！")
    try:
        ret = await DataCheckService.query_ume_time(mes[0])
        return success(data=ret, msg="获取当前网管时间成功")
    except BaseException as err:
        Logger.error(f"获取当前网管时间失败，详情:", traceback.format_exc())
        return fail(msg=f"获取当前网管时间失败")
