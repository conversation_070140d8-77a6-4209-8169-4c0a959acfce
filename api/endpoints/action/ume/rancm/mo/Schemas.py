import json
from pydantic import BaseModel, Field, validator, root_validator
from typing import Literal, TypeVar, Optional
from infrastructure.utils.DataDealer import str_to_list, str_to_dict
from datetime import datetime
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar("DataT")

PERIOD_MAP = {"最近一天": 1, "最近三天": 3, "最近一周": 7, "自定义": "自定义"}

class CreateInfo(BaseModel):
    mocName: str
    attrs: DataT = Field(..., type="jsonEditor", description='创建网元的参数，为json格式，例 {"moId": "1", "sd": 1} ')
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    keyMoPath: str | None = Field(None, description="关键路径，是parentLdn的子集，用于区分mo，格式为 k1=v1,k2=v2，举例:NRCellDU=1,CA=1")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")
    templateType: Literal['无', '密码编辑器'] | None = Field(None, description='通过模版创建节点')

    @validator('attrs', pre=True, always=False)
    def attrs_to_dict(cls, v):
        if type(v) == dict:
            return v
        try:
            json.loads(v)
            return v
        except:
            raise ValueError("attrs 不符合json规范，请检查")

    @validator('keyMoPath', pre=True, always=False)
    def key_mo_path_to_dict(cls, v):
        try:
            if v:
                str_to_dict(v)
            return v
        except:
            raise ValueError("keyMoPath 不符合规范，请检查")


class BatchCreateInfo(BaseModel):
    moDatas: DataT = Field([{"moId": "1"}, {"moId": "2"}], type="jsonEditor",
                           description='多条创建MO的数据，为json格式，例 '
                                       '[{"moId": "1", "k": "v1"}, {"moId": "2", "k": "v2"}] ')
    dataAreaId: Optional[str] = Field(None, description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    mocName: Optional[str] = Field(None, description='在同一个MO下创建时填写;'
                                                     '如果在多个MO下创建，需要在moDatas中每条数据中加上 "mocName": "xxx"')
    keyMoPath: Optional[str] = Field(None,
                                     description='在同一个MO下创建时可填写，是parentLdn的子集，例 NRCellDU=1;'
                                                 '如果在多个MO下创建且存在同名MO，需要在moDatas中每条数据中加上 "parentLdn": "xxx"')
    meId: Optional[str] = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")

    @validator('moDatas', pre=True)
    def validate_moDatas(cls, v):
        if isinstance(v, str):
            try:
                v = json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("moDatas 不符合 JSON 规范，请检查输入。")
        if not isinstance(v, list):
            raise ValueError("moDatas 必须是包含字典的列表。")
        return v

    @validator('keyMoPath', pre=False, always=False)
    def validate_keyMoPath(cls, v):
        if v:
            try:
                return str_to_dict(v)
            except Exception:
                raise ValueError("keyMoPath 不符合规范，请检查")
        return v

    @root_validator
    def check_mocName_in_moDatas(cls, values):
        mocName = values.get("mocName")
        moDatas = values.get("moDatas", [])

        # 如果 mocName 为空，确保每个 moData 都有 "mocName"
        if not mocName:
            for moData in moDatas:
                if "mocName" not in moData:
                    raise ValueError("如果未提供 mocName，则 moDatas 中的每个元素都必须包含 'mocName' 字段。")

        return values


class GetInfo(BaseModel):
    mocName: str
    attrNames: str = Field(..., description="需要的属性，格式为 k1;k2;k3 ，例 moId;ldn")
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    filter: str | None = Field(None, description='过滤网元的参数，为json格式，例 {"moId": "1"} ')
    keyMoPath: str | None = Field(None, description="关键路径，是ldn的子集，用于区分mo，格式为 k1=v1,k2=v2，举例:NRCellDU=1,CA=1")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")

    @validator('attrNames', pre=True, always=False)
    def attr_names_to_list(cls, v):
        try:
            if v:
                str_to_list(v)
            return v
        except:
            raise ValueError("attrNames 不符合规范，请检查")

    @validator('filter', pre=True, always=False)
    def filter_to_dict(cls, v):
        try:
            if v:
                json.loads(v)
            return v
        except:
            raise ValueError("filter 不符合json规范，请检查")

    @validator('keyMoPath', pre=True, always=False)
    def key_mo_path_to_dict(cls, v):
        try:
            if v:
                str_to_dict(v)
            return v
        except:
            raise ValueError("keyMoPath 不符合规范，请检查")


class GetCurrInfo(BaseModel):
    mocName: str
    attrNames: str = Field(..., description="需要的属性，格式为 k1;k2;k3 ，例 moId;ldn")
    filter: str | None = Field(None, description='过滤网元的参数，为json格式，例 {"moId": "1"} ')
    keyMoPath: str | None = Field(None, description="关键路径，是ldn的子集，用于区分mo，格式为 k1=v1,k2=v2，举例:NRCellDU=1,CA=1")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")

    @validator('attrNames', pre=True, always=False)
    def attr_names_to_list(cls, v):
        try:
            str_to_list(v)
            return v
        except:
            raise ValueError("attrNames 不符合规范，请检查")

    @validator('filter', pre=True, always=False)
    def filter_to_dict(cls, v):
        try:
            if v:
                json.loads(v)
            return v
        except:
            raise ValueError("filter 不符合json规范，请检查")

    @validator('keyMoPath', pre=True, always=False)
    def key_mo_path_to_dict(cls, v):
        try:
            if v:
                str_to_dict(v)
            return v
        except:
            raise ValueError("keyMoPath 不符合规范，请检查")


class UpdateInfo(BaseModel):
    mocName: str
    attrs: DataT = Field(..., type="jsonEditor", description='更新网元的参数，为json格式，例 {"moId": "1", "sd": 1} ')
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    filter: str | None = Field(None, description='过滤网元的参数，为json格式，例 {"moId": "1"} ')
    keyMoPath: str | None = Field(None, description="关键路径，是ldn的子集，用于区分mo，格式为 k1=v1,k2=v2，举例:NRCellDU=1,CA=1")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")
    templateType: Literal['无', '密码编辑器'] | None = Field(None, description='通过模版更新节点')

    @validator('attrs', pre=True, always=False)
    def attrs_to_dict(cls, v):
        if type(v) == dict:
            return v
        try:
            json.loads(v)
            return v
        except:
            raise ValueError("attrs 不符合json规范，请检查")

    @validator('filter', pre=True, always=False)
    def filter_to_dict(cls, v):
        try:
            if v:
                json.loads(v)
            return v
        except:
            raise ValueError("filter 不符合json规范，请检查")

    @validator('keyMoPath', pre=True, always=False)
    def key_mo_path_to_dict(cls, v):
        try:
            if v:
                str_to_dict(v)
            return v
        except:
            raise ValueError("keyMoPath 不符合规范，请检查")


class BatchUpdateInfo(BaseModel):
    moDatas: DataT = Field([{"moId": "1"}, {"moId": "2"}], type="jsonEditor",
                           description=(
            "<b style='color: #007BFF;'>多条要修改 MO 的数据，为 JSON 格式</b><br><br>"
            "<b>同一个 MO 下：</b><br>"
            "可以通过 moId 区分，可只填 moId，例如：<br>"
            "<pre>[{ \"moId\": \"1\"，\"k\": \"v1\" }, { \"moId\": \"2\"，\"k\": \"v2\" }]</pre><br>"
            "<b>多个 MO 下或无法通过 moId 区分：</b><br>"
            "需要填写 <code>mocName</code> 和 <code>ldn</code>，例如：<br>"
            "<pre>[{ \"mocName\": \"xxx\", \"ldn\": \"xxx\", \"k\": \"v1\" }, { \"mocName\": \"xxx\", \"ldn\": \"xxxx\", \"k\": \"v2\" }]</pre>"
        ))
    dataAreaId: Optional[str] = Field(None, description=(
            "<b style='color: #28A745;'>规划区 ID</b><br><br>"
            "不填则按默认规划区 ID 处理：<code>bizcore_auto_+meId</code>"
        ))
    mocName: Optional[str] = Field(None, description=(
            "<b style='color: #DC3545;'>MO 名称</b><br><br>"
            "在 <b>同一个 MO</b> 下修改时填写；<br>"
            "如果在 <b>多个 MO</b> 下修改，需要在 <code>moDatas</code> 中的每条数据中加上：<br>"
            "<code>{\"mocName\": \"xxx\"}</code>"
        ))
    keyMoPath: Optional[str] = Field(None, description=(
            "<b style='color: #FFC107;'>关键 MO 路径</b><br><br>"
            "在 <b>同一个 MO</b> 下修改时可填写，是 <code>parentLdn</code> 的子集，例如：<br>"
            "<pre>NRCellDU=1</pre><br>"
            "如果在 <b>多个 MO</b> 下修改，需要在 <code>moDatas</code> 中的每条数据中加上：<br>"
            "<code>{\"ldn\": \"xxx\"}</code>"
        ))
    meId: Optional[str] = Field(None, description=(
            "<b style='color: #6C757D;'>网元 ID</b><br><br>"
            "用于区分多网元环境中的网元，<b>不填</b>会对环境中所有网元操作"
        ))

    @validator('moDatas', pre=True)
    def validate_moDatas(cls, v):
        if isinstance(v, str):
            try:
                v = json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("moDatas 不符合 JSON 规范，请检查输入。")
        if not isinstance(v, list):
            raise ValueError("moDatas 必须是包含字典的列表。")
        return v

    @validator('keyMoPath', pre=False, always=False)
    def validate_keyMoPath(cls, v):
        if v:
            try:
                return str_to_dict(v)
            except Exception:
                raise ValueError("keyMoPath 不符合规范，请检查")
        return v

    @root_validator
    def check_mocName_in_moDatas(cls, values):
        mocName = values.get("mocName")
        moDatas = values.get("moDatas", [])

        # 如果 mocName 为空，确保每个 moData 都有 "mocName" 和 "ldn"
        if not mocName:
            for moData in moDatas:
                if "mocName" not in moData or "ldn" not in moData:
                    raise ValueError("如果未提供 mocName，则 moDatas 中的每个元素都必须包含 'mocName' 和 'ldn' 字段。")

        return values


class DeleteInfo(BaseModel):
    mocName: str
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    filter: str | None = Field(None, description='过滤网元的参数，为json格式，例 {"moId": "1"} ')
    keyMoPath: str | None = Field(None, description="关键路径，是ldn的子集，用于区分mo，格式为 k1=v1,k2=v2，举例:NRCellDU=1,CA=1")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")

    @validator('filter', pre=True, always=False)
    def filter_to_dict(cls, v):
        try:
            if v:
                json.loads(v)
            return v
        except:
            raise ValueError("filter 不符合json规范，请检查")

    @validator('keyMoPath', pre=True, always=False)
    def key_mo_path_to_dict(cls, v):
        try:
            if v:
                str_to_dict(v)
            return v
        except:
            raise ValueError("keyMoPath 不符合规范，请检查")


class BatchDeleteInfo(BaseModel):
    moDatas: DataT = Field([{"moId": "1"}, {"moId": "2"}], type="jsonEditor",
                           description=(
            "<b style='color: #007BFF;'>多条要删除 MO 的数据，为 JSON 格式</b><br><br>"
            "<b>同一个 MO 下：</b><br>"
            "可以通过 <code>moId</code> 区分，可只填 <code>moId</code>，例如：<br>"
            "<pre>[{ \"moId\": \"1\" }, { \"moId\": \"2\" }]</pre><br>"
            "<b>多个 MO 下或无法通过 moId 区分：</b><br>"
            "需要填写 <code>mocName</code> 和 <code>ldn</code>，例如：<br>"
            "<pre>[{ \"mocName\": \"xxx\", \"ldn\": \"xxx\" }, { \"mocName\": \"xxx\", \"ldn\": \"xxxx\" }]</pre>"
        ))
    dataAreaId: Optional[str] = Field(None, description=(
            "<b style='color: #28A745;'>规划区 ID</b><br><br>"
            "不填则按默认规划区 ID 处理：<code>bizcore_auto_+meId</code>"
        ))
    mocName: Optional[str] = Field(None, description=(
            "<b style='color: #DC3545;'>MO 名称</b><br><br>"
            "在 <b>同一个 MO</b> 下删除时填写；<br>"
            "如果在 <b>多个 MO</b> 下删除，需要在 <code>moDatas</code> 中的每条数据中加上：<br>"
            "<code>{\"mocName\": \"xxx\"}</code>"
        ))
    keyMoPath: Optional[str] = Field(None, description=(
            "<b style='color: #FFC107;'>关键 MO 路径</b><br><br>"
            "在 <b>同一个 MO</b> 下修改时可填写，是 <code>parentLdn</code> 的子集，例如：<br>"
            "<pre>NRCellDU=1</pre><br>"
            "如果在 <b>多个 MO</b> 下修改，需要在 <code>moDatas</code> 中的每条数据中加上：<br>"
            "<code>{\"ldn\": \"xxx\"}</code>"
        ))
    meId: Optional[str] = Field(None, description=(
            "<b style='color: #6C757D;'>网元 ID</b><br><br>"
            "用于区分多网元环境中的网元，<b>不填</b>会对环境中所有网元操作"
        ))

    @validator('moDatas', pre=True)
    def validate_moDatas(cls, v):
        if isinstance(v, str):
            try:
                v = json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("moDatas 不符合 JSON 规范，请检查输入。")
        if not isinstance(v, list):
            raise ValueError("moDatas 必须是包含字典的列表。")
        return v

    @validator('keyMoPath', pre=False, always=False)
    def validate_keyMoPath(cls, v):
        if v:
            try:
                return str_to_dict(v)
            except Exception:
                raise ValueError("keyMoPath 不符合规范，请检查")
        return v

    @root_validator
    def check_mocName_in_moDatas(cls, values):
        mocName = values.get("mocName")
        moDatas = values.get("moDatas", [])

        # 如果 mocName 为空，确保每个 moData 都有 "mocName" 和 "ldn"
        if not mocName:
            for moData in moDatas:
                if "mocName" not in moData or "ldn" not in moData:
                    raise ValueError("如果未提供 mocName，则 moDatas 中的每个元素都必须包含 'mocName' 和 'ldn' 字段。")

        return values


class CommitInfo(BaseModel):
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")
    waitTime: int | None = Field(120, description="等待时间(s)，默认为120")

    @validator('waitTime', pre=False, always=False)  # 适配旧action
    def validate_waitTime(cls, v):
        if not v:
            return 120
        return v


class ClearSpecifiedMocRecords(BaseModel):
    mocNames: str = Field(
        ..., description="一键删除Moc数据，格式为 MocName1,MocName2 , 例 nrcellrelation,InterFMeasObject")
    dataAreaId: str | None = None
    meId: str | None = Field(None, description="网元Id，用于区分多网元环境中的网元，不填会对环境中所有网元操作")


CELL_PARA = {"pLMNId": "460-01", "cellLocalId": "2", "userLabel": "2", "cityLabel": "guangxi", "asPSCellSwch": "true", \
             "cellPLMNIdList": "460-01:0", "NRPhysicalCellDU_moId": "2", "nrPhysicalCellDUId": "2", \
             "NRPhysicalCellDU_userLabel": "NRPhysicalCellDU-2", "masterOperatorId": "46001-3972-2", \
             "CellDefiningSSB_pci": "225", "tac": "512000", "tacSwch": "tacOn", "cellAtt": "sub6G", "duplexMode": "TDD", \
             "coverageType": "Macro", "qcellFlag": "0", "NRCarrier_moId": "2", "NRCarrierId": "2",
             "CarrierUL_frequencyBandList": "78", \
             "CarrierUL_frequency": "3451.5", "CarrierDL_frequencyBandList": "78", "CarrierDL_frequency": "3451.5", \
             "nrCellScene": "Normal", "NRSectorCarrier_moId": "2", "configuredMaxTxPower": "500",
             "powerPerRERef": "148", \
             "CarrierDL_nrbandwidth": "100", "TddConfig_dlULTransmissionPeriodicity1": "ms5", \
             "TddConfig_frameType1": "1;1;1;1;1;1;1;2;0;0", "TddConfig_frameType2Present": "0",
             "refBpPoolFunction": "BF_7", "refSectorFunction": "SF_2"}


class CellManageParas(BaseModel):
    cellParas: DataT = Field(CELL_PARA, type="jsonEditor",
                             description="字典格式，具体配置参数见网管配置")
    moOp: Literal['create', 'modify', 'delete', 'query'] = Field('create', description='创建-create/修改-modify/删除-delete/查询-query(可通过subClass指定节点)')
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")


class DelAreaParas(BaseModel):
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")


FILTERATE_PRIMARY_KEY = {"moId": "1"}
FILTERATE_DEVICE_ID = {"moId": "51"}
CELL_PARAS = {"tac": "512001"}
DEVICE_PARAS = {"targetName": "A9641A S35"}


class AdjustCellParas(BaseModel):
    MoNode: Literal['NRSectorCarrier', 'NRCarrier', 'NRPhysicalCellDU', 'PhyResourceConfigforBWPUL', \
        'PhyResourceConfigforBWPDL', 'NRCellCU', 'NRCellDU', 'PlmnGroupList'] = Field('NRCellDU',
                                                                                      description='需要修改的mocName')
    Primarykey: DataT = Field(FILTERATE_PRIMARY_KEY, type="jsonEditor",
                              description="字典格式，用于过滤小区信息")
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    cellParas: DataT = Field(CELL_PARAS, type="jsonEditor",
                             description="字典格式，修改参数")


class SuperCellParas(BaseModel):
    masterCell: str = Field("CELL_1",
                            description="主小区id别名，在资源选择中查看，默认为CELL_1，辅小区为同网元下未填写的所有小区")
    nrCarrierLdnList: str = Field(None, description="游离的NR载波ldn的List，示例：[]")
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")


class SplitSuperCellParas(BaseModel):
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")


class SplitSuperMIMOCellParas(BaseModel):
    independent: Literal[False, True] = Field(False, description='拆分时辅cp是否配置独立小区，默认为不配置')
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")


class ReplaceBoard(BaseModel):
    DeviceId: DataT = Field(FILTERATE_DEVICE_ID, type="jsonEditor",
                            description="字典格式，用于过滤设备唯一标识")
    dataAreaId: str | None = Field(description="规划区Id，不填则按默认规划区Id处理: bizcore_auto_+meId")
    DeviceParas: DataT = Field(DEVICE_PARAS, type="jsonEditor",
                               description="字典格式，修改参数")


class VirtualCellRealtion(BaseModel):
    func: Literal["nr-nr", "lte-lte", "nr-lte", "lte-nr"] = Field("nr-nr",
                                                                  description="虚配邻区关系类型")


class MoExportFile(BaseModel):
    mocNames: str = Field(
        ..., description="MO导出的Moc数据，格式为 MocName1,MocName2 , 例 NRCellCU,InterFMeasObject")
    dataAreaId: str | None = None
    onlyMandatory: bool = Field(False, description='是否只导必填参数')

class MoImportFile(BaseModel):
    dataAreaId: str | None = None
    filePath: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                            allowedFileType="*.xlsx;*.xlsm;*.xls", limit=1, params=[],
                            description="导入本地已制作好的MO编辑文件(仅支持一个文件)，当输入该参数时，表明以本地导入数据，"
                                        "如果action有依赖'MO编辑器导出文件',将以导出的模板导入数据")
    isActive: bool = Field(True, description='是否立即激活,True - 激活, False - 不激活,默认激活', ifEmptyUseDefault=False)

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        isActive_field = cls.__fields__["isActive"]
        if not values.get("isActive"):
            if not isActive_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["isActive"] = False
        return values

class NeChangeHistory(BaseModel):
    period: Literal["最近一天", "最近三天", "最近一周", "自定义"] = Field(default="最近一天", description='周期')
    startTime: datetime | None = Field(None, description='开始时间,默认不填,表示当前系统时间之前24小时为开始时间')
    endTime: datetime | None = Field(None, description='结束时间,默认不填,表示当前系统时间为结束时间')

    @validator("period")
    def update_period(cls, v):
        return PERIOD_MAP.get(v, 1)

    @classmethod
    def schema(cls):
        schema_map = [
            ({"period": ["最近一天"]}, {}),
            ({"period": ["最近三天"]}, {}),
            ({"period": ["最近一周"]}, {}),
            ({"period": ["自定义"]}, {"startTime": None, "endTime": None})
        ]
        dynamic_paras = ["startTime", "endTime"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

NeChangeHistory.schema()
