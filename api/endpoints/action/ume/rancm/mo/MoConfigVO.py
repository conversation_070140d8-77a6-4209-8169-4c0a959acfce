"""
@File: MoConfigVO.py
@Author: 许王禾子10333721
@Time: 2024/4/2 10:21
@License: Copyright 2022-2030
@Desc: None
"""
import json
from infrastructure.utils.DataDealer import str_to_list, str_to_dict

templateMap = {"密码编辑器": "curr_password"}


class CreateMO:
    def __init__(self, paras: dict):
        self.mocName = paras.get("mocName")
        self.attrs = paras.get("attrs") if type(paras.get("attrs")) == dict else json.loads(paras.get("attrs"))
        self.keyMoPath = str_to_dict(paras.get("keyMoPath", "")) if paras.get("keyMoPath") else {}
        self.dataAreaId = paras.get("dataAreaId", "")
        self.meId = paras.get("meId", "")
        self.templateType = templateMap.get(paras.get("templateType", ""))


class GetMO:
    def __init__(self, paras: dict, curr=False):
        self.mocName = paras.get("mocName")
        self.attrNames = str_to_list(paras.get("attrNames")) if paras.get("attrNames") else {}
        self.filter = json.loads(paras.get("filter", "")) if paras.get("filter") else {}
        self.keyMoPath = str_to_dict(paras.get("keyMoPath", "")) if paras.get("keyMoPath") else {}
        self.dataAreaId = paras.get("dataAreaId", "")
        self.meId = paras.get("meId", "")
        self.curr = curr  # 是否通过现网区查询


class UpdateMO:
    def __init__(self, paras: dict):
        self.mocName = paras.get("mocName")
        self.attrs = paras.get("attrs") if type(paras.get("attrs")) == dict else json.loads(paras.get("attrs"))
        self.filter = json.loads(paras.get("filter", "")) if paras.get("filter") else {}
        self.keyMoPath = str_to_dict(paras.get("keyMoPath", "")) if paras.get("keyMoPath") else {}
        self.dataAreaId = paras.get("dataAreaId", "")
        self.meId = paras.get("meId", "")
        self.templateType = templateMap.get(paras.get("templateType", ""))


class DeleteMO:
    def __init__(self, paras: dict):
        self.mocName = paras.get("mocName")
        self.filter = json.loads(paras.get("filter", "")) if paras.get("filter") else {}
        self.keyMoPath = str_to_dict(paras.get("keyMoPath", "")) if paras.get("keyMoPath") else {}
        self.dataAreaId = paras.get("dataAreaId", "")
        self.meId = paras.get("meId", "")
