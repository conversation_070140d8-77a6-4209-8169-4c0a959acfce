# -*- encoding: utf-8 -*-
'''
@File    :   Ue.py
@Time    :   2023/08/31 15:57:44
<AUTHOR>   何为10156505
@Version :   1.0
@Contact :   <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import traceback
import sys

from infrastructure.logger import Logger
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.factories.NdsFactory import NdsFactory
from domain.factories.MeFactory import MeFactory
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from api.endpoints.action.nds.Schemas import downloadData, combinedData, checkNdsData
from domain.models.ume.dcm.Dcm import Dcm

router = APIRouter(route_class=bizRoute)


@router.post("/get_datas", summary="获取NDS服务器CSV数据",
             description=description("从事件订阅任务关联的第三方NDS服务器获取CSV指定数据", "10263798;10270755"),
             openapi_extra={"refs": [{"actionName": "创建事件订阅任务",
                                      "path": "/v1/api/action/ume/dcm/event_subscription/create_event_sub_task",
                                      "refPattern": "0-*"}]}
             )
async def get_datas(request: Request, para: downloadData):
    taskInfo = await Refs(request).get_output("taskInfo")
    successFlag, result = True, {}
    meIds = [me.meId for me in await MeFactory.create(request)]
    if taskInfo != []:
        for meTask in taskInfo:
            for meId, info in meTask.items():
                if meId not in meIds: continue
                me = await MeFactory.create_by_meid(request, meId)
                if me is None: return fail(msg=f"当前事件订阅任务中选择的网元与环境信息不匹配，请检查：环境资源信息中是否存在{meId}网元!")
                try:
                    ndsIp = await Dcm(me).get_nds_ip_by_event_subscriptionId(info.get("subscriptionId"))
                    eventInfo = await Dcm(me).get_event_subscription_task_base_info(info.get("subscriptionId"))
                    nds = NdsFactory().create_by_ip(request, ndsIp)
                    if nds is None: return fail(msg=f"环境资源信息中不存在ip:{ndsIp} 的NDS服务器信息，请增加相应NDS属性配置!")
                    retTemp, rltInfo = await nds.get_csv_datas(meId,
                                                               eventInfo.get("taskTypeName").upper(),
                                                               para.fileFeature or "",
                                                               para.startTime or eventInfo.get("start"),
                                                               para.colInfos)
                    if not retTemp:
                        successFlag = False
                    result.update({meId: rltInfo})
                    if int(sys.getsizeof(rltInfo) / (1024 * 1024)) > 10:
                        return fail("ACTION 执行失败, 获取到的结果数据太多，请缩短查询时间")
                except Exception as err:
                    successFlag = False
                    Logger.error(f"任务:{info.get('subscriptionId')} 网元:{me.meId} 获取NDS数据异常，详情:", traceback.format_exc())
                    result.update({meId: str(err)})
    else:
        if not para.gnbId or not para.startTime:
            return fail(f'NDS获取数据执行失败', data=f'未选择依赖的情况下action未入参gnbId或开始时间')
        else:
            try:
                nds = NdsFactory().create(request)
                if len(nds) != 1: return fail(msg=f'环境资源未指明或环境资源中有多个NDS，请指明所需NDS')
                retTemp, rltInfo = await nds[0].get_csv_datas(para.gnbId,
                                                           para.taskTypeName,
                                                           para.fileFeature or "",
                                                           para.startTime,
                                                           para.colInfos)
                if not retTemp:
                    successFlag = False
                result.update({para.gnbId: rltInfo})
                if int(sys.getsizeof(rltInfo) / (1024 * 1024)) > 10:
                    return fail("ACTION 执行失败, 获取到的结果数据太多，请缩短查询时间")
            except Exception as err:
                successFlag = False
                Logger.error(f"网元:{para.gnbId} 获取NDS数据异常，详情:",
                             traceback.format_exc())
                result.update({para.gnbId: str(err)})
    if successFlag and result:
        return success(f'NDS获取数据执行成功', data=result)
    if not result:
        return fail(f'NDS获取数据执行失败', data=f"当前事件订阅任务中选择的网元{meIds}与环境信息不匹配，请检查!")
    return fail(f'NDS获取数据执行失败', data=result)


@router.post("/get_combined_data", summary="获取NDS服务器汇总数据",
             description=description("从事件订阅任务关联的第三方NDS服务器获取CSV指定数据", "10263798"),
             openapi_extra={"refs": [{"actionName": "创建事件订阅任务",
                                      "path": "/v1/api/action/ume/dcm/event_subscription/create_event_sub_task",
                                      "refPattern": "0-*"}]}
            )
async def get_combined_data(request: Request, para: combinedData):
    taskInfo = await Refs(request).get_output("taskInfo")
    meIds = [me.meId for me in await MeFactory.create(request)]
    localFilePath, sftpUrl, result = [], [], {}

    def fail_if_none(item, error_msg):
        if item is None:
            return fail(msg=error_msg)

    async def process_csv_files(nds, meId, eventInfo):
        csvFileList = await nds.find_csv_files(meId, eventInfo.get("taskTypeName").upper(), para.fileFeature or "",
                                               para.startTime or eventInfo.get("start"))
        if not csvFileList:
            return fail(f"在{nds.ndsIp}的NDS服务器中没有搜索到网元{meId}的文件")
        localFilePath.extend(await nds.write_csv_datas_to_local_path(csvFileList))
        combinedFilePath = await nds.combine_csv_datas(localFilePath)
        csvFilePath = await nds.submit_file(combinedFilePath)
        if csvFilePath:
            sftpUrl.append(csvFilePath)

    async def process_xml_files(nds, meId, eventInfo):
        xmlFileList = await nds.find_xml_files(para.fileFeature or "", para.startTime or eventInfo.get("start"))
        xmlFilePathList = await nds.write_xml_datas_to_local_path(meId, xmlFileList)
        if xmlFilePathList:
            sftpUrl.extend(xmlFileList)

    async def handle_me_task(meId, info):
        me = await MeFactory.create_by_meid(request, meId)
        fail_if_none(me, f"当前事件订阅任务中选择的网元与环境信息不匹配，请检查：环境资源信息中是否存在{meId}网元!")
        ndsIp = await Dcm(me).get_nds_ip_by_event_subscriptionId(info.get("subscriptionId"))
        eventInfo = await Dcm(me).get_event_subscription_task_base_info(info.get("subscriptionId"))
        nds = NdsFactory().create_by_ip(request, ndsIp)
        fail_if_none(nds, f"环境资源信息中不存在ip:{ndsIp} 的NDS服务器信息，请增加相应NDS属性配置!")
        if 'csv' in para.fileType:
            await process_csv_files(nds, meId, eventInfo)
        if 'xml' in para.fileType:
            await process_xml_files(nds, meId, eventInfo)

    if taskInfo:
        for meTask in taskInfo:
            for meId, info in meTask.items():
                if meId in meIds:
                    try:
                        await handle_me_task(meId, info)
                    except Exception as err:
                        Logger.error(f"任务:{info.get('subscriptionId')} 网元:{meId} 获取NDS数据异常，详情:",
                                     traceback.format_exc())
                        result.update({meId: str(err)})
    else:
        gnbIds = para.meId.split(',')
        nds = NdsFactory().create(request)
        fail_if_none(nds, "环境资源信息中不存在NDS服务器信息，请增加相应NDS属性配置!")
        for meId in gnbIds:
            try:
                if 'csv' in para.fileType:
                    csvFilePath = await nds[0].get_combined_csv_datas(gnbIds, para.taskTypeName, para.fileFeature or "",
                                                                      para.startTime)
                    if csvFilePath: sftpUrl.append(csvFilePath)
                if 'xml' in para.fileType:
                    xmlFileList = await nds[0].find_xml_files(para.fileFeature or "", para.startTime)
                    xmlFilePathList = await nds[0].write_xml_datas_to_local_path(meId, xmlFileList)
                    if xmlFilePathList: sftpUrl.extend(xmlFilePathList)
            except Exception as err:
                Logger.error(f"网元:{meId} 获取NDS数据异常，详情:", traceback.format_exc())
                result.update({meId: str(err)})
    if sftpUrl:
        return success('数据合并成功，已生成导出链接', data={'sftpUrls': sftpUrl})
    else:
        return fail(f'NDS获取数据执行失败:{result}')


@router.post("/check_data", summary="校验NDS服务器汇总数据",
             description=description("通过上传的规则表校验NDS服务器的数据", "10263798"),
             openapi_extra={"refs": [{"actionName": "获取NDS服务器汇总数据",
                                      "refPattern": "0-1"}]}
            )
async def check_data(request: Request, para: checkNdsData):
    # 获取 sftpUrls
    sftp_urls = await Refs(request).get_output("sftpUrls") or []
    sftp_urls = sftp_urls[0] if sftp_urls else []
    csv_url = [url for url in sftp_urls if '.csv' in url]
    xml_url = [url for url in sftp_urls if '.xml' in url]
    # 如果 sftp_urls 中没有文件，使用 para 中传入的文件路径
    csvNdsFile = csv_url or (para.ndsCsvFile[0] if para.ndsCsvFile else None)
    xmlNdsFile = xml_url or (para.ndsXmlFile[0] if para.ndsXmlFile else None)
    # 校验是否有文件
    if not (csvNdsFile or xmlNdsFile):
        return fail('依赖和上传的nds文件不能都为空')
    Logger.debug(f'等待校验的csv文件:{csvNdsFile} '
                 f'等待校验的xml文件:{xmlNdsFile}')
    checkFile = para.checkFile[0]
    # 获取 NDS 实例
    nds = NdsFactory().create(request)
    if nds is None:
        return fail("环境资源信息中不存在NDS服务器信息，请增加相应NDS属性配置!")
    retMsg, successFlag = [], True

    # 校验CSV文件
    async def check_csv_file(csv_file, check_file):
        ret, message = await nds[0].check_nds_data(csv_file, check_file)
        if not ret:
            return False, f'csv文件:{csv_file.split("/")[-1]} 校验失败，详情:{message}'
        return True, f'csv文件:{csv_file.split("/")[-1]} 校验成功，详情:{message}'

    # 校验XML文件
    async def check_xml_file(xml_file, check_file):
        ret, message = await nds[0].check_nds_xml_data(xml_file, check_file)
        if not ret:
            return False, f'xml文件校验失败，详情:{message}'
        return True, f'xml文件校验成功，详情:{message}'

    # 校验csv文件
    if csvNdsFile:
        rlt, csv_result = await check_csv_file(csvNdsFile[0], checkFile)
        retMsg.append(csv_result)
        if not rlt: successFlag = False
    # 校验xml文件
    if xmlNdsFile:
        rlt, xml_result = await check_xml_file(xmlNdsFile, checkFile)
        retMsg.append(xml_result)
        if not rlt: successFlag = False
    return success(msg=retMsg) if successFlag else fail(msg=retMsg)
