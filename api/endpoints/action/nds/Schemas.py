from typing import Literal, TypeVar
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

DataT = TypeVar('DataT')

class downloadData(BaseModel):
    fileFeature: str = Field(None, description='文件名特征值,用户可以（通过NDS已有数据经验）填已知指定特征值，若不填将不指定特征值，'
                                               '如：MRO')
    colInfos: str = Field("3-5", description="需要获取csv数据文件列号或者表头信息: 支持'1,2-4'模式;表头信息方式支持大小写;支持数值过滤(>、<、!=、==);"
                                             "如果没有满足过滤条件的数据，将只返回表头."
                                            "举例: 1-2,3==1,PCI>11,ssbARFCN, 表示取第3列等于1且PCI大于11,第1-2列,第3列,PCI列,ssbARFCN列数据",
                          regex=r"^(?:\s*((\w+\s*(<|>|-|!=|==)\s*\w+)|\w+)\s*,?)+$")
    gnbId: str = Field(None, description='网元ID，没有依赖创建任务action时此处必填')
    taskTypeName: Literal['MR', 'MDT', 'CDT', 'VONR'] = Field('MR', description='任务类型，依赖创建订阅任务时此处参数不生效')
    startTime: datetime | None = Field(None, description='用户获取数据开始时间，如果不填将使用事件订阅的开始时间作为数据获取开始时间')

class fileTypeEnum(str, Enum):
    csv = 'csv',
    xml = 'xml'

class combinedData(BaseModel):
    fileFeature: str = Field(None, description='文件名特征值,用户可以（通过NDS已有数据经验）填已知指定特征值，若不填将不指定特征值，'
                                               '如：MRO')
    meId: str = Field(None, description='网元ID，没有依赖创建任务action时此处必填,可填多个，例如:1234,5678')
    fileType: list[fileTypeEnum] = Field([fileTypeEnum.csv, fileTypeEnum.xml], description='需要校验的文件类型')
    taskTypeName: Literal['MR', 'MDT', 'CDT', 'VONR'] = Field('MR', description='任务类型，依赖创建订阅任务时此处参数不生效')
    startTime: datetime | None = Field(None, description='用户获取数据开始时间，如果不填将使用事件订阅的开始时间作为数据获取开始时间')


class checkNdsData(BaseModel):
    ndsCsvFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                        allowedFileType=".csv", params=[], description='待校验的csv文件，当没有引用的action时此处上传的文件才会生效')
    ndsXmlFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                           allowedFileType=".xml", params=[], description='待校验的xml文件，当没有引用的action时此处上传的文件才会生效')
    checkFile: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                        allowedFileType=".csv", limit=1, params=[], description='校验文件的规则表')