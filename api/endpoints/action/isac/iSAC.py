"""
@File: iSAC.py
@Author: 岳昊冉10263798
@Time: 2024-09-10 19:02:46
@License: Copyright 2022-2030
@Desc: None
"""
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import success, fail
from api.endpoints.action.isac.Schemas import CheckItems
from domain.platform.ActionInfo import description
from domain.factories.MeFactory import MeFactory
from service.action.isac.iSACService import iSACService
from infrastructure.logger import Logger
from service.action.ume.rancm.MoConfigService import MoConfigService

router = APIRouter(route_class=bizRoute)


@router.post("/check_isac_data", summary="校验通感地图数据", description=description("校验地图数据是否为直线且连续", "岳昊冉10263798"))
async def check_isac_data(request: Request, paras: CheckItems):
    errList = []
    mapUrl = f'wss://{paras.mapIp}:{paras.mapPort}/api/isac/v1/ws/shake-hand?scene=channel&uid=&addr={paras.neIp}'
    mes = await MeFactory.create(request)
    for me in mes:
        ret, err = await MoConfigService.query_curr_mo(me, "NRSensingFunction", ["gNBId"], None)
        if err: return fail(msg=f"未查询到网元{me.meId}的感知节点,请确认是否配置了感知.")
        gNBId = ret[0].get('gNBId')
        coordinates = await iSACService.getCoordinates(mapUrl, paras.time)
        points = await iSACService.calculate_the_coordinates(coordinates, gNBId)
        if len(points) == 0:
            return fail(msg=f"未从地图上获取到网元{me.meId}的感知gnb:{gNBId}的轨迹")
        Logger.debug(f"获取到点{len(points)}个")
        maxDistance = paras.maxDistance if "直线" in paras.checkItem else None
        tolerance = paras.tolerance if "连续" in paras.checkItem else None
        if maxDistance and not tolerance:
            return fail(msg="不可单独校验连续，校验连续的前提为直线")
        is_linear, is_continuous = await iSACService.check_linearity_and_continuity(points, maxDistance, tolerance)
        if "最大移动距离" in paras.checkItem:
            maxMovingDis = await iSACService.get_max_moving_distance(points)
            if maxMovingDis >= float(paras.maxDistance):
                errList.append(f"目标点移动的最大距离超出校验范围")
        if not is_linear or not is_continuous:
            errList.append(f"网元：{me.meId}校验失败，地图上的点是直线:{is_linear}，地图上的点连续:{is_continuous}")
    if errList:
        return fail(msg=errList)
    return success(msg="轨迹符合通过准则")
