"""
@File: Schemas.py
@Author: 许王禾子10333721
@Time: 2024/1/8 上午11:11
@License: Copyright 2022-2030
@Desc: None
"""
from typing import TypeVar
from enum import Enum
from pydantic import BaseModel, Field, validator

DataT = TypeVar('DataT')  # 创建一个 TypeVar 的实例


class checkItemEnum(str, Enum):
    StraightLine = "直线"
    Continual = "连续"
    MaxMovingDistance = "最大移动距离"

class CheckItems(BaseModel):
    mapIp: str | None = Field(None, description="地图的地址，类似*************")
    mapPort: str | None = Field("30008", description="地图的端口")
    neIp: str | None = Field(None, description="地图上的NEip")
    time: str | None = Field("30", description="采集时间,单位为秒")
    checkItem: list[checkItemEnum] = Field([checkItemEnum.StraightLine, checkItemEnum.Continual, checkItemEnum.MaxMovingDistance], description='选择校验场景')
    tolerance: str | None = Field('1e-8', description="科学计数法表示，例如1e-8。空间向量夹角的cos值,直线则此值无线趋近于0，1e-8表示0.00000001,偏离1°约为4e-3，大于此阈值时视为不笔直。")
    maxDistance: str | None = Field('0.2', description="相邻两点的最大距离阈值，超过此距离视为断点")
    maxMovingDistance: str | None = Field('20', description="轨迹的最长距离，一般为综测仪的最大距离-目标距离")