from typing import Literal, TypeVar
from pydantic import BaseModel, Field

DataT = TypeVar('DataT')
TEMP_JSON = {}

class SeCmd(BaseModel):
    func: Literal["自定义"] = Field(default="自定义", description='命令名')
    method: Literal["getAllData", "get", "action", "delete", "add", "set", "mod"]= Field("get", description="操作类型")
    path: str = Field(..., description='命令操作路径，可参考RF底层,举例: Device.X_ZTE_5G.')
    objs: DataT = Field(dafault = TEMP_JSON, type="jsonEditor", description="通用命令参数,请参考json格式")
    isIgnoreError : bool = Field(False, description='是否忽略命令异常, 默认不忽略')

class StartPdnPingBox(BaseModel):
    sourcePdn: str = Field("PDN_1", description="源侧PDN id别名，在资源选择中选择与查看，默认为PDN_1")
    targetPdn: str = Field("PDN_2", description="目标侧PDN id别名，在资源选择中选择与查看，默认为PDN_2")
    packageSize: str = Field('1460', description='包长，会根据包长选择合适工具,当ping包的包长大于1460bytes时，建议ping包延时大于发包所需的时间')
    pingNum: str = Field('10', description='包数')
    interval: str = Field('1000', description='包间隔，单位为ms，默认为1000，此参数只在UE连在linux pc环境下有效。')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')


class StartPdnUdpPerfBox(BaseModel):
    sourcePdn: str = Field("PDN_1", description="源侧PDN id别名，在资源选择中选择与查看，默认为PDN_1")
    targetPdn: str = Field("PDN_2", description="目标侧PDN id别名，在资源选择中选择与查看，默认为PDN_2")
    perfPort: str = Field(None, description='Udp灌包端口，不传默认自动生成')
    bandwidth: str = Field('100', description='Udp灌包带宽(单位M)，默认100M')
    threadCount: str = Field('1', description='Udp收灌包线程数，默认为1')
    secs: str = Field('999', description='Udp灌包时长,默认为999')
    length: str = Field('1024', description='Udp灌包包长，默认为1024Byte')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')


class StartPdnTcpPerfBox(BaseModel):
    sourcePdn: str = Field("PDN_1", description="源侧PDN id别名，在资源选择中选择与查看，默认为PDN_1")
    targetPdn: str = Field("PDN_2", description="目标侧PDN id别名，在资源选择中选择与查看，默认为PDN_2")
    perfPort: str = Field(None, description='Tcp灌包端口，不传默认自动生成')
    winSize: str = Field('1024K', description='TCP灌包窗口尺寸，默认为1024K')
    duration: str = Field('999', description='Tcp灌包时长,默认为999（单位：秒）')
    isKeepAlive: bool = Field(False, description='Tcp灌包保活开关，TCP任务在断开时是否自动尝试连接')
    parallel: str = Field('5', description='Tcp灌包并发线程数，默认为5')
    length: str = Field('8K', description='下行Tcp灌包包长，默认为8K')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')


