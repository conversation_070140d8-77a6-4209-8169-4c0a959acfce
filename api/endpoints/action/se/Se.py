# -*- encoding: utf-8 -*-
'''
@File    :   Se.py
@Time    :   2025/03/10 15:57:44
<AUTHOR>   10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import traceback
import re

from domain.factories.PdnFactory import PdnFactory
from infrastructure.logger import Logger
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.factories.SeFactory import SeFactory
from domain.platform.ActionInfo import description
from api.endpoints.action.se.Schemas import SeCmd, StartPdnPingBox, StartPdnUdpPerfBox, StartPdnTcpPerfBox
from infrastructure.resource.service.Refs import Refs

router = APIRouter(route_class=bizRoute)


@router.post("/se_cmd", summary="SE执行命令", description=description("SE前端执行命令", "10270755"))
async def se_cmd(request: Request, paras: SeCmd):
    try:
        ses = SeFactory.create(request)
        ueRltDict = {}
        for se in ses:
            result = await se.se_cmd(paras.func, dict(paras))
            rlt, rltInfo = result if isinstance(result, tuple) else result, result
            if not result:
                return fail(f'该SE: {repr(se.id)},执行{paras.func}失败', data=ueRltDict)
            ueRltDict[se.id] = rltInfo
        return success(f'SE执行{paras.func}命令执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("SE执行命令失败", data=str(e))

def _get_pdn_from_request(request, paras:StartPdnPingBox | StartPdnUdpPerfBox | StartPdnTcpPerfBox):
    sourcePdns = PdnFactory.create_by_alias(request, paras.sourcePdn)
    targetPdns = PdnFactory.create_by_alias(request, paras.targetPdn)
    if not sourcePdns or not targetPdns:
        return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！')
    targetPdnIp = targetPdns[0].ipv6 if paras.ipFlag == "IPV6" else targetPdns[0].cnIp
    return sourcePdns[0], targetPdns[0], targetPdnIp


@router.post("/start_pdn_ping", summary="PDN启动Ping", description=description("两PDN间启动ping,NE使用", "10270755"))
async def start_pdn_ping(request: Request, startPdnPing: StartPdnPingBox):
    try:
        sourcePdn, targetPdn, targetPdnIp=  _get_pdn_from_request(request, startPdnPing)
        trafficInfoList, trafficInfo = [], {}
        rlt, rltInfo = await sourcePdn.start_ping(targetPdnIp, startPdnPing.packageSize, startPdnPing.pingNum,
                                                  startPdnPing.interval, startPdnPing.ipFlag)
        if rlt:
            trafficInfo.update({"taskId": rltInfo, "serviceMgrPort": sourcePdn.serviceMgrPort, "id": sourcePdn.id, "ip": sourcePdn.ip})
            Logger.info(trafficInfo)
            trafficInfoList.append(trafficInfo)
        else:
            return fail(f'PDN: {repr(sourcePdn.id)} 启动Ping执行失败', data=rltInfo)
        return success(f'PDN: {repr(sourcePdn.id)}启动Ping,执行成功', data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        Logger.error(traceback.format_exc())
        return fail(f"PDN启动Ping,执行失败", data=str(e))


@router.post("/start_pdn_udp_perf", summary="PDN启动UDP收灌包", description=description("两PDN间启动UDP收灌包,NE使用", "10263798"))
async def start_pdn_udp_perf(request: Request, startPdnUdpPerfBox: StartPdnUdpPerfBox):
    try:
        sourcePdn, targetPdn, targetPdnIp=  _get_pdn_from_request(request, startPdnUdpPerfBox)
        trafficInfoList, trafficInfo = [], {}
        rlt, rltInfo = await sourcePdn.send_udp(targetPdnIp, startPdnUdpPerfBox.perfPort, startPdnUdpPerfBox.bandwidth,
                                              startPdnUdpPerfBox.secs, startPdnUdpPerfBox.length,
                                              startPdnUdpPerfBox.threadCount, None,
                                              startPdnUdpPerfBox.ipFlag)
        if rlt:
            trafficInfo.update({"taskId": rltInfo, "serviceMgrPort": sourcePdn.serviceMgrPort, "id": sourcePdn.id, "ip": sourcePdn.ip})
            Logger.info(trafficInfo)
            trafficInfoList.append(trafficInfo)
        else:
            return fail(f'PDN: {repr(sourcePdn.id)} 启动UDP收灌包执行失败', data=rltInfo)
        return success(f'PDN: {repr(sourcePdn.id)}启动UDP收灌包,执行成功', data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail(f"PDN启动UDP收灌包,执行失败", data=str(e))


@router.post("/start_pdn_tcp_perf", summary="PDN启动TCP收灌包", description=description("两PDN间启动TCP收灌包,NE使用", "10263798"))
async def start_pdn_tcp_perf(request: Request, startPdnTcpPerfBox: StartPdnTcpPerfBox):
    try:
        sourcePdn, targetPdn, targetPdnIp=  _get_pdn_from_request(request, startPdnTcpPerfBox)
        trafficInfoList, trafficInfo = [], {}
        rlt, rltInfo = await targetPdn.recv_tcp(startPdnTcpPerfBox.perfPort, startPdnTcpPerfBox.parallel,
                                          startPdnTcpPerfBox.winSize, startPdnTcpPerfBox.ipFlag)
        if rlt:
            trafficInfo.update({"taskId": rltInfo, "serviceMgrPort": targetPdn.serviceMgrPort, "id": targetPdn.id, "ip": targetPdn.ip})
            trafficInfoList.append(trafficInfo)
        else:
            return fail(data=rltInfo, msg=f"目标PDN:{repr(sourcePdn.id)}启动收TCP灌包失败")
        rlt, rltInfo = await sourcePdn.send_tcp(targetPdnIp, startPdnTcpPerfBox.perfPort, startPdnTcpPerfBox.winSize,
                                          startPdnTcpPerfBox.duration, startPdnTcpPerfBox.parallel,
                                          startPdnTcpPerfBox.isKeepAlive, startPdnTcpPerfBox.length, startPdnTcpPerfBox.ipFlag)
        if rlt:
            trafficInfo.update({"taskId": rltInfo, "serviceMgrPort": sourcePdn.serviceMgrPort, "id": sourcePdn.id, "ip": sourcePdn.ip})
            trafficInfoList.append(trafficInfo)
        else:
            return fail(data=rltInfo, msg=f"源侧PDN:{repr(sourcePdn.id)}启动TCP灌包失")
        Logger.info(trafficInfoList)
        return success(f'PDN: {repr(sourcePdn.id)}启动TCP收灌包,执行成功', data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        Logger.error(traceback.format_exc())
        return fail(f"PDN启动TCP收灌包,执行失败", data=str(e))


@router.post("/query_pdn_task_info", summary="PDN查询业务信息", description=description("两PDN间查询业务信息,NE使用", "10263798"),
             openapi_extra={"refs": [{"actionName": "PDN启动.*", "path": "/v1/api/action/se", "refPattern": "1-*"}]})
async def query_pdn_task_info(request: Request):

    try:
        trafficInfoLists = await Refs(request).get_output("trafficInfoList")
        rltList = []
        Logger.info(trafficInfoLists)
        for trafficInfos in trafficInfoLists:
            for trafficInfo in trafficInfos:
                rltDict = {}
                pdn = PdnFactory.create_by_id(request, trafficInfo['id'])
                if not pdn:
                    return fail(f"PDN {trafficInfo['id']}资源不存在, 请输入检查pdn资源配置！！！")
                rlt = await pdn.query_task_info(trafficInfo['taskId'])
                if rlt:
                    Logger.info(rltList)
                    trafficInfo.update({"trafficInfo": trafficInfo, "queryResult": rlt, "id": pdn.id})
                    rltList.append(rltDict)
        return success(f'PDN查询业务信息 执行成功', data=rltList)
    except Exception as e:
        traceback.print_exc()
        Logger.error(traceback.format_exc())
        return fail(f"PDN查询业务信息 执行失败", data=str(e))


@router.post("/stop_pdn_tasks", summary="PDN结束业务", description=description("结束两PDN间的业务,NE使用", "10263798"),
             openapi_extra={"refs": [{"actionName": "PDN启动.*", "path": "/v1/api/action/se", "refPattern": "1-*"}]})
async def stop_pdn_tasks(request: Request):
    try:
        trafficInfoLists = await Refs(request).get_output("trafficInfoList")
        sucRltList, failRltList = [], []
        Logger.info(trafficInfoLists)
        for trafficInfos in trafficInfoLists:
            for trafficInfo in trafficInfos:
                rltDict = {}
                pdn = PdnFactory.create_by_id(request, trafficInfo['id'])
                if not pdn:
                    return fail(f"PDN {trafficInfo['id']}资源不存在, 请输入检查pdn资源配置！！！")
                rlt, rltInfo = await pdn.stop_task(trafficInfo['taskId'])
                rltDict['trafficInfo'] = trafficInfo
                rltDict['queryResult'] = rlt, rltInfo
                failRltList.append(rltDict) if not rlt else sucRltList.append(rltDict)
        if failRltList: return fail(f'PDN结束业务 执行失败', data=failRltList)
        return success(f'PDN结束业务 执行成功', data=sucRltList)
    except Exception as e:
        traceback.print_exc()
        Logger.error(traceback.format_exc())
        return fail(f"PDN结束业务 执行失败", data=str(e))


