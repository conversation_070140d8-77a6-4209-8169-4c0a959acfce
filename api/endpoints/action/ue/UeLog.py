# -*- encoding: utf-8 -*-
'''
@File    :   Ue.py
@Time    :   2023/08/31 15:57:44
<AUTHOR>   何为10156505
@Version :   1.0
@Contact :   <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import traceback
import re

from infrastructure.logger import Logger
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.factories.UeFactory import UeFactory
from domain.models.ue.mue.Mtue import Mtue
from infrastructure.device.CmdResult import CmdResult
from domain.factories.PdnFactory import PdnFactory
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from api.endpoints.action.ue.Schemas import startPingBox, startUlUdpPerfBox, stopLogTypeDict, \
    startLogCollection, startDlUdpPerfBox, startLogTypeDict, StartUlTcpPerfBox, stopLogCollection, \
    RecvUlTcpPerfBox, StartDlTcpPerfBox, recvDlTcpPerfBox, PduCreate, PduRelease, \
    GroupParameter, GroupServiceConfiguration, GroupReset, configUe, volteVoiceCall, enterLogIntoIds, ftpUpLoad, \
    McpUeIds, McpPsl, ftpDownLoad, startSignalLogPrase

router = APIRouter(route_class=bizRoute)


@router.post("/start_log_collection_function", summary="开启UE日志采集", description=description("开启UE日志采集", "10263798"))
async def start_log_collection_function(request: Request, para: startLogCollection):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.start_log_collection(startLogTypeDict[para.logType])
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))



@router.post("/clear_signal", summary="清除UE信令", description=description("清除UE信令", "10263798"))
async def clear_signal(request: Request):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.clear_signal()
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rlt, rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/start_signal_log_parse", summary="开启UE信令解析", description=description("开启UE信令解析,【使用方法："
                                                                                    "1、BizcodeSlave，LMT需部署在同一台电脑；"
                                                                                    "2、仅支持TUE、CPE，请下载master分支最新版本；"
                                                                                    "3、解析前需要先停止采集，5-10s后启动解析, 避免文件被占用；"
                                                                                    "4、控制测试时间，接收1000条信令后，文件会自动新增。】", "10263798"),
             openapi_extra={"refs": [{"actionName": "开启UE日志采集", "path": "/v1/api/action/ue", "refPattern": "1-*"}]})
async def start_log_collection_function(request: Request, para: startSignalLogPrase):
    try:
        ues = UeFactory.create(request)
        fileDicts = await Refs(request).get_output()
        Logger.debug(fileDicts)
        ueRltDict = {}
        for fileDict in fileDicts:
            for ue in ues:
                Logger.debug(fileDict)
                if ue.ueType in ['TUE', 'CPE'] and ue.id in fileDict:
                    Logger.debug(ue.id)
                    filePath = fileDict.get(ue.id).get("filePath")
                    fileName = fileDict.get(ue.id).get("fileName").replace('_', '-NR')
                    if re.search("\.signal", fileName):
                        fileFullPath = filePath + "\\" + fileName
                    else:
                        fileFullPath = filePath + "\\" + fileName + '.signal'
                    Logger.debug(fileFullPath)
                    rlt, rltInfo = await ue.start_signal_log_parse(para, fileFullPath)
                    if not rlt:
                        return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
                    ueRltDict[ue.id] = rltInfo
                    Logger.debug(ueRltDict)
            if ueRltDict:
                return success(f'ACTION 执行成功', data=ueRltDict)
        return fail(f'ACTION 执行失败', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/stop_log_collection_function", summary="结束UE日志采集", description=description("结束UE日志采集", "10263798"))
async def stop_log_collection_function(request: Request, para: stopLogCollection):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.stop_log_collection(stopLogTypeDict[para.logType])
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/enter_log_into_ids", summary="UE日志解析入库---（待完善）", description=description("UE日志解析入库---（待完善）", '10263798'),
             openapi_extra={"refs": [{"actionName": "开启UE日志采集"}]})
async def enter_log_into_ids(request: Request, para: enterLogIntoIds):
    try:
        refsPaths = await Refs(request).get_output()
        Logger.info("UE日志解析入库，filePathList:{0}".format(refsPaths))
        if not refsPaths:
            return fail(msg="获取不到UE日志采集的文件{0}".format(refsPaths))
        resource = request.state.resource
        Logger.info(resource)
        taskId = resource.get("taskId", '')
        taskName = resource.get("taskName", '')
        tldTaskDict = {"taskId": taskId, "taskName": taskName, "cases": []}
        Logger.info(tldTaskDict)
        ues = UeFactory.create(request)
        Logger.info(refsPaths)
        ueRltDict = {}
        for ue in ues:
            for refsPathDict in refsPaths:
                if ue.id in refsPathDict:
                    rlt, rltInfo = await ue.import_log_into_database(para.logType, tldTaskDict, refsPathDict)
                    if not rlt:
                        return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
                    ueRltDict[ue.id] = rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))