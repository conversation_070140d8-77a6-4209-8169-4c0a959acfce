# -*- encoding: utf-8 -*-
'''
@File    :   Ue.py
@Time    :   2023/08/31 15:57:44
<AUTHOR>   何为10156505
@Version :   1.0
@Contact :   <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import traceback
import re

from domain.platform.artifact import Artifact
from infrastructure.logger import Logger
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.factories.UeFactory import UeFactory
from domain.factories.PdnFactory import PdnFactory
from domain.models.ue.mue.Mtue import Mtue
from infrastructure.resource.service.Refs import Refs
from infrastructure.device.CmdResult import CmdResult
from domain.platform.ActionInfo import description
from api.endpoints.action.ue.Schemas import GroupParameter, GroupServiceConfiguration, GroupReset, \
    McpUeIds, McpPsl, McpCmd

router = APIRouter(route_class=bizRoute)


@router.post("/createUes", summary="创建多UE",
             description=description("在LMT里创建组及UE，用之前先删除所有组，使用'删除所有UE'action(仅支持MTUE);"
                                     "对于MCP，该action仅用于创建虚拟ue分组，以便下文依赖",
                                     "10255283;10270755"))
async def create_groups(request: Request, groupparameter:GroupParameter):
    ues = UeFactory.create(request)
    groupNameList = [x for x in groupparameter.groups.split(",")]
    ids = [x for x in groupparameter.ues.replace("-", "~").split(",")]
    states = {}
    for ue in ues:
        ueStates = []
        ueType = type(ue).__name__.upper()
        if ueType in ["MTUE"] and len(groupNameList) != len(ids):
            return fail(msg="!!请保证groups和ues数相同!!")
        if ueType == "MCP": states.update({"MCP":groupparameter.ues})
        if ueType != "MTUE": continue
        for i in range(len(groupNameList)):
            rlt = await ue.create_cpe_group(groupNameList[i], ids[i])
            if not rlt.result:
                return fail(f'{groupNameList[i]} UE创建失败，请先删除UE')
            ueStates.append(rlt)
        states.update({ue.id: ueStates})
        rll = await ue.change_imsi(ids)
        if not rll.result:
            return fail(f'{ue.id}加载imsi号失败')
    return success(data=states)

@router.post("/deleteUes", summary="删除所有UE", description=description("在LMT里删除group实例(仅支持MTUE)", "10255283"))
async def delete_groups(request: Request):
    ues = UeFactory.create(request)
    stateList = []
    for ue in ues:
        rlt = await ue.delete_all_cpe_group()
        if not rlt:
            return fail(f'删除所有UE失败，原因是:{rlt}')
        stateList.append(rlt)
    return success(f'删除所有UE成功')

@router.post("/setGroupServiceConfig", summary="多UE下发业务配置",
             description=description("在LMT里下发Traffic config,并添加相对应的路由，如果ping/TCP/UDP的desipaddr为空，"
                                     "自动使用config的pdnip;\r\n对于MUE,通过导入模型修改配置(bizcore必须在MUE电脑部署)", "10255283"))
async def elivering_group_service_configurations(request: Request, groupserviceconfiguration:GroupServiceConfiguration):
    ues = UeFactory.create(request)
    pdns = PdnFactory.create(request)
    stateList = []
    for value in groupserviceconfiguration.trafficConfig.values():
        routeIp = re.findall(r"\b(?:\d{1,3}\.){3}\d{1,3}\b", value)
        if routeIp:
            break
    for ue in ues:
        ueType = type(ue).__name__.upper()
        if ueType in ["MTUE"]:
            groupserviceconfiguration = _update_mtue_trafficConfig(pdns, groupserviceconfiguration)
            rlt = await ue.cpe_trafficcfg(groupserviceconfiguration.group, groupserviceconfiguration.trafficConfig)
            if not rlt.result:
                return fail(f'{ueType}[{ue.id}]下发业务配置失败', data=stateList)
            stateList.append(rlt)
            if routeIp:
                routeIList = ["serverroute1", routeIp[0]]
            else:
                routeIList = ["serverroute1", pdns[0]._cnIp]
            await ue.configure(routeIList, str(groupserviceconfiguration.group))
        elif ueType in ["MUE"]:
            try:
                await ue.mue_detach()
            finally:
                if not groupserviceconfiguration.importCfg:
                    return fail(f'{ueType}[{ue.id}]下发业务模型，必需上传模型文件', data=stateList)
                filePath = Artifact.download_file_form_url(groupserviceconfiguration.importCfg[0])
                rlt, msg = await ue.import_mue_xlsx(filePath)
                if not rlt:
                    return fail(f'{ueType}[{ue.id}]下发业务模型失败,{msg}', data=stateList)
                else: stateList.append(f'{ueType}[{ue.id}]下发业务模型成功')
        else: continue
    return success(f'下发业务配置成功', data=stateList)


def _update_mtue_trafficConfig(pdns, groupserviceconfiguration: GroupServiceConfiguration):
    if groupserviceconfiguration.trafficConfig["selservice"] == "2" or groupserviceconfiguration.trafficConfig["selservice"] == "1":
        if groupserviceconfiguration.trafficConfig["desipaddr"] == "":
            if len(pdns) != 1:
                return fail(f'请保证资源选择只选择一个pdn,或者填写参数里的desipaddr的值不为空')
            else:
                groupserviceconfiguration.trafficConfig["desipaddr"] = pdns[0].cnIp
    return groupserviceconfiguration


@router.post("/mueReset", summary="多UE接入",
             description=description("MTUE、MUE的reset功能/MCP的接入功能(MCP可通过依赖'创建多UE'，获取目标ues)", "10255283,10270755"),
             openapi_extra={"refs": [{"actionName": "创建多UE", "refPattern": "0-*"}]})
async def elivering_group_service_configurations(request: Request, groupreset: GroupReset):
    ues = UeFactory.create(request)
    states = []
    for ue in ues:
        ueType = type(ue).__name__.upper()
        try:
            if ueType in ["MCP", "MTUE", "MUE"]:
                ueids = groupreset.ues
                if ueType == "MCP" and not groupreset.ues:
                    ueids = ",".join([ueinfo for ueinfo in await Refs(request).get_output("MCP") if ueinfo])
                if ueType in ["MTUE", "MUE"] and not groupreset.ues: ueids = 'allue'
                rlt = await ue.mue_attach(groupreset.groups, ueids)
            else:
                rlt = f"{ueType}类型UE无法使用'多UE接入'action，请通过资源选择MCP、MTUE、MUE"
                return fail(msg=f'{rlt}')
        except BaseException as err:
            Logger.error(f"{ueType}[{ue.id}]接入出现异常,详情:", traceback.format_exc())
            return fail(msg=f'{ueType}[{ue.id}]接入出现异常,详情:{err}')
        states.append({ue.id: rlt})
        reultTemp = rlt.result if isinstance(rlt, CmdResult) else rlt
        reultTemp = 'connected' in reultTemp if isinstance(reultTemp, dict) else reultTemp
        if not reultTemp:
            return fail(f'{ueType}[{ue.id}]接入失败', data=states)
    return success("多UE接入成功", data=states)

@router.post("/mueStop", summary="多UE去附着",
             description=description("多ue停止业务(MCP可通过依赖'创建多UE'，获取目标ues)", "10255283,10270755"),
             openapi_extra={"refs": [{"actionName": "创建多UE", "refPattern": "0-*"}]})
async def elivering_group_service_configurations(request: Request, groupreset: GroupReset):
    ues = UeFactory.create(request)
    stateList = []
    for ue in ues:
        ueType = type(ue).__name__.upper()
        try:
            if ueType in ["MCP", "MTUE", "MUE"]:
                ueids = groupreset.ues
                if ueType == "MCP" and not groupreset.ues:
                    ueids = ",".join([ueinfo for ueinfo in await Refs(request).get_output("MCP") if ueinfo])
                if ueType in ["MTUE", "MUE"] and not groupreset.ues: ueids = 'allue'
                rlt = await ue.mue_detach(groupreset.groups, ueids)
            else:
                rlt = f"{ueType}类型UE无法使用'多UE去附着'action，请通过资源选择MCP、MTUE、MUE"
                return fail(msg=f'{rlt}')
        except BaseException as err:
            Logger.error(f"{ueType}[{ue.id}]去附着出现异常,详情:", traceback.format_exc())
            return fail(msg=f'{ueType}[{ue.id}]去附着出现异常,详情:{err}')
        stateList.append({ue.id: rlt})
        reultTemp = rlt.result if isinstance(rlt, CmdResult) else rlt
        reultTemp = 'connected' not in reultTemp if isinstance(reultTemp, dict) else reultTemp
        if not reultTemp:
            return fail(f'{ueType}[{ue.id}]去附着失败', data=stateList)
    return success("多UE去附着成功", data=stateList)

@router.post("/mcp_psl_start", summary="MCP启动运行脚本",
             description=description("MCP启动运行脚本(可通过依赖'创建多UE'，获取目标ues)", "10270755"),
             openapi_extra={"refs": [{"actionName": "创建多UE", "refPattern": "0-*"}]})
async def mcp_psl_start(request: Request, mspparas: McpPsl):
    ues = UeFactory.create(request)
    stateList = []
    for ue in ues:
        try:
            if ue.ueType == "MCP":
                if not mspparas.ues:
                    mspparas.ues = ",".join([ueinfo for ueinfo in await Refs(request).get_output("MCP") if ueinfo])
                if mspparas.path:
                    await ue.upload_psl_to_mpc_from_service(mspparas.path, mspparas.parameters)
                    await ue.set_psl(mspparas.ues, mspparas.path)
                rlt = await ue.run_psl(mspparas.ues)
                stateList.append({ue.id: f"UEID:{rlt}"})
            else:
                rlt = f"{type(ue).__name__.upper()}类型UE无法使用'MCP启动运行脚本'action，请通过资源选择MCP"
                stateList.append({ue.id: f"{rlt}"})
                Logger.warning(rlt)
        except BaseException as err:
            Logger.error(f"MCP[{ue.id}]启动运行脚本出现异常,详情:", traceback.format_exc())
            return fail(msg=f'MCP[{ue.id}]启动运行脚本出现异常,详情:{err}', data=stateList)
    return success("MCP启动运行脚本成功", data=stateList)

@router.post("/mcp_psl_stop", summary="MCP停止运行脚本",
             description=description("MCP停止运行脚本(可通过依赖'创建多UE'，获取目标ues)", "10270755"),
             openapi_extra={"refs": [{"actionName": "创建多UE", "refPattern": "0-*"}]})
async def mcp_psl_stop(request: Request, mspparas: McpUeIds):
    ues = UeFactory.create(request)
    stateList = []
    for ue in ues:
        try:
            if ue.ueType == "MCP":
                if not mspparas.ues:
                    mspparas.ues = ",".join([ueinfo for ueinfo in await Refs(request).get_output("MCP") if ueinfo])
                rlt = await ue.stop_run_psl(mspparas.ues)
                stateList.append({ue.id: f"UEID:{rlt}"})
            else:
                rlt = f"{type(ue).__name__.upper()}类型UE无法使用'MCP停止运行脚本'action，请通过资源选择MCP"
                Logger.warning(rlt)
                stateList.append({ue.id: f"{rlt}"})
        except BaseException as err:
            Logger.error(f"MCP[{ue.id}]停止运行脚本出现异常,详情:", traceback.format_exc())
            return fail(msg=f'MCP[{ue.id}]停止运行脚本出现异常,详情:{err}', data=stateList)
    return success("MCP停止运行脚本成功", data=stateList)

@router.post("/mcp_cmd", summary="MCP执行命令",
             description=description("MCP执行通用命令，具体命令可参考MCP使用说明书(可通过依赖'创建多UE'，获取目标ues)", "10270755"),
             openapi_extra={"refs": [{"actionName": "创建多UE", "refPattern": "0-*"}]})
async def mcp_run_cmd(request: Request, mspparas: McpCmd):
    ues = UeFactory.create(request)
    states = []
    for ue in ues:
        try:
            if ue.ueType == "MCP":
                if not mspparas.ues:
                    mspparas.ues = ",".join([ueinfo for ueinfo in await Refs(request).get_output("MCP") if ueinfo])
                ueIds, rlt = await ue.send_cmd(mspparas.ues, mspparas.commandLine, mspparas.expected)
                if rlt.result:
                    states.append({ue.id: f"UEID:{ueIds},结果:命令执行成功,回显:{rlt.return_string}"})
                else:
                    states.append({ue.id: f"UEID:{ueIds},结果:命令执行失败,回显:{rlt.return_string},与预期不一致"})
                    return fail(msg=f'MCP执行命令失败', data=states)
            else:
                rlt = f"{type(ue).__name__.upper()}类型UE无法使用'MCP执行命令'action，请通过资源选择MCP"
                states.append({ue.id: f"{rlt}"})
                Logger.warning(rlt)
        except BaseException as err:
            Logger.error(f"MCP[{ue.id}]执行命令出现异常,详情:", traceback.format_exc())
            return fail(msg=f'MCP[{ue.id}]执行命令出现异常,详情:{err}', data=states)
    return success("MCP执行命令成功", data=states)

