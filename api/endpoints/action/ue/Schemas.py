from typing import Literal, TypeVar
from pydantic import BaseModel, Field, root_validator
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema


DataT = TypeVar('DataT')
MTUE_PARA = {"selservice": "0", "traffictotaldur": "99999", "attmaintaindur": "5000", "idledur": "5000", "attinterval": "200", "attuesonce": "1"}


class startPingBox(BaseModel):
    pdnIp: str = Field(None, description='ping包，目的地址, PDN的控制Ip, 选填,如果不填会从环境信息获取')
    packageSize: str = Field('1460',
                             description='包长，会根据包长选择合适工具,当ping包的包长大于1460bytes时，建议ping包延时大于发包所需的时间')
    pingNum: str = Field('10', description='包数')
    interval: str = Field('1000', description='包间隔，单位为ms，默认为1000，此参数只在UE连在linux pc环境下有效。')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')


class trafficIdList(BaseModel):
    tIdList: list


class PduCreate(BaseModel):
    pdusessionid: Literal['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'] = Field('6',
                                                                                               description='PDU会话ID，配置范围5~15')
    SD: str = Field('1118481', description='SD(slice differentiator，切片微分器)，补充SST类型，以区分相同SST类型的多个网络切片，可\
                                                       选配置，可使用默认值，修改需要从核心网侧获取配置')
    dnn: str = Field('cmnet', description='根据IMSI及核心网支持情况填写，例如cmnet')
    pdusessionType: Literal["IPV4", "IPV6", "IPV4IPV6", "ETH"]
    sscMode: str = Field('1', description='会话和服务连续模式,可使用默认值1(SSCmode1可以应用到任何PDU Session类型和任何接入类型)，\
                                                        修改需要从核心网侧获取配置')
    snssaiFlag: str = Field('1', description='可使用默认值1')
    sst: str = Field('1', description='它指的是在功能和服务方面的预期网络切片行为,取值含义：1:eMBB，2:URLLC，3:MIoT')
    sdflag: str = Field('1', description='可使用默认值,修改需要从核心网侧获取配置')
    mappedcfgsstflag: str = Field('1', description='可使用默认值,修改需要从核心网侧获取配置')
    mappedcfgsst: str = Field('0', description='可使用默认值,修改需要从核心网侧获取配置')
    mappedcfgsdflag: str = Field('1', description='可使用默认值,修改需要从核心网侧获取配置')
    mappedcfgsd: str = Field('0', description='可使用默认值,修改需要从核心网侧获取配置')


class PduRelease(BaseModel):
    pdusessionid: Literal['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'] = Field('6',
                                                                                               description='释放的PDU会话ID，配置范围5~15')


class startUlUdpPerfBox(BaseModel):
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    pdnIp: str = Field(None, description='上行Udp灌包pdn控制ip地址,选填,不填默认从环境信息中获取')
    perfPort: str = Field(None, description='上行Udp灌包目的端口，不传默认自动生成')
    bandwidth: str = Field('100', description='上行Udp灌包带宽(单位M)，默认100M')
    threadCount: str = Field('1', description='上行Udp收灌包线程数，默认为1')
    secs: str = Field('999', description='上行Udp灌包时长,默认为999')
    length: str = Field('1024', description='上行Udp灌包包长，默认为1024Byte')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')

class recvUlUdpPerfBox(BaseModel):
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    pdnIp: str = Field(None, description='上行Udp灌包pdn控制ip地址,选填,不填默认从环境信息中获取')
    perfPort: str = Field(None, description='上行Udp收灌包端口，不传默认自动生成')

class startDlUdpPerfBox(BaseModel):
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    pdnIp: str = Field(None, description='下行Udp灌包pdn控制ip地址,选填,不填默认从环境信息中获取')
    perfPort: str = Field(None, description='下行Udp灌包目的端口，不传默认自动生成')
    bandwidth: str = Field('100', description='下行Udp灌包带宽（单位M），默认100M')
    threadCount: str = Field('1', description='下行Udp收灌包线程数，默认为1')
    secs: str = Field('999', description='下行Udp灌包时长,默认为999')
    length: str = Field('1024', description='下行Udp灌包包长，默认为1024Byte')
    specifiedPath: str = Field(None, description='下行Udp收灌包文件的路径，默认是None，用iperf的默认路径文件')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')


class recvDlUdpPerfBox(BaseModel):
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    pdnIp: str = Field(None, description='下行Udp灌包pdn控制ip地址,选填,不填默认从环境信息中获取')
    perfPort: str = Field(None, description='下行Udp收灌包端口，不传默认自动生成')


class StartUlTcpPerfBox(BaseModel):
    pdnIp: str = Field(None, description='上行Tcp灌包PDN的Ip,选填,不填默认从环境信息中获取')
    perfPort: str = Field(None, description='上行Tcp灌包端口，TCP业务需要灌包和收包端口相同')
    winSize: str = Field('1024K', description='上行TCP灌包窗口尺寸，默认为1024K')
    duration: str = Field('999', description='上行Tcp灌包时长,默认为999（单位：秒）')
    isKeepAlive: bool = Field(False, description='上行Tcp灌包保活开关，TCP任务在断开时是否自动尝试连接')
    parallel: str = Field('5', description='上行Tcp灌包并发线程数，默认为5')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')


class RecvUlTcpPerfBox(BaseModel):
    pdnIp: str = Field(None, description='上行Tcp灌包PDN的Ip,选填,不填默认从环境信息中获取')
    perfPort: str = Field(None, description='上行Tcp收包端口，TCP业务需要灌包和收包端口相同')
    recvWin: str = Field('1024K', description='上行TCP灌包窗口尺寸，默认为1024K')
    isKeepAlive: bool = Field(False, description='上行Tcp灌包保活开关，TCP任务在断开时是否自动尝试连接')
    parallel: str = Field('5', description='上行Tcp灌包并发线程数，默认为5')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')


class StartDlTcpPerfBox(BaseModel):
    pdnIp: str = Field(None, description='下行Tcp灌包PDN的Ip,选填,不填默认从环境信息中获取')
    perfPort: str = Field(None, description='下行Tcp灌包端口，TCP业务需要灌包和收包端口相同')
    winSize: str = Field('1024K', description='下行TCP灌包窗口尺寸，默认为1024K')
    duration: str = Field('999', description='下行Tcp灌包时长,默认为999（单位：秒）')
    isKeepAlive: bool = Field(False, description='下行Tcp灌包保活开关，TCP任务在断开时是否自动尝试连接')
    parallel: str = Field('1', description='下行Tcp灌包并发线程数，默认为1')
    length: str = Field('8K', description='下行Tcp灌包包长，默认为8K')
    specifiedPath: str = Field(None, description='下行Tcp灌包文件的路径，默认是None，用iperf的默认路径文件')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')


class recvDlTcpPerfBox(BaseModel):
    perfPort: str = Field(None, description='下行Tcp收包端口，TCP业务需要灌包和收包端口相同')
    recvWin: str = Field('1024K', description='上行TCP灌包窗口尺寸，默认为1024K')
    recvThread: str = Field('10', description='下行Tcp收灌包并发线程数，默认为10')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')

class configUe(BaseModel):
    ueConfigUrl: DataT = Field(None, type="label",  viewType="url", urlName="UE配置操作指南", url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/a687df5133af45eea4b17777cce0581b/view")
    commandLine: str = Field(None, type='textarea')
    parameters: DataT = Field(default=None, type="jsonEditor")

class GroupParameter(BaseModel):
    groups: str = Field("BasicGroup,Group1,Group2,Group3,Group4", description='组名称，可以创建多个用","号隔开，包括BasicGroup\
                 和其他，组名称填写包括BasicGroup表示创建beseUe,填写其他如group1(可以随便命名)表示创建名为group1的uegroup(非basegroup),备注：\
                 ids 0和1预留给BasicGroup1和asicGroup2使用;对于MCP/MUE，该参数不生效')
    ues: str = Field("0-0,2-6,7-20,21-148,149-149",
                     description='ue id范围,多个用","号隔开,请保证环境配置里对应的ueAttr->imsi'
                                 '数大于所分的id数;对于MCP，该参数用于创建虚拟ue组，以便下文依赖；对于MUE，该参数不生效')

class GroupServiceConfiguration(BaseModel):
    group: int = Field(0, description='测试组id，对应创建group的id值，0就表示给Basegroup下发业务配置，2表示group1下发业务配置(仅MTUE生效)')
    trafficConfig: DataT = Field(MTUE_PARA, type="jsonEditor",
                            description="下发配置的操作指令，输入为jason格式，具体含义见指导书(仅MTUE生效)")
    importCfg: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                            allowedFileType=".xlsx", limit=1, params=[],
                            description="MUE导入的业务模型文件,只有MUE才生效")
    instructions: DataT = Field(None, type="label", viewType="url", urlName="trafficConfig指导书",
                               url="https://i.zte.com.cn/#/space/98d17a46b2b749c490b0a2f5b5926ae5/wiki/page/6c95d23cb7634d0583296408d9ee0a78/view")


class GroupReset(BaseModel):
    groups: str = Field("0,2", description='操作UE的组号,针对MTUE一般basic group 为0或者1，下面的group依次加1(该参数对MCP/MUE不生效)')
    ues: str | None = Field("allue", description='单独选择UE Group下面的ue实例做业务，allue代表所有ue实例，'
                                          '要操作某几个需要写表达式,如4-5;对于MCP，表示做业务的UE编号，也可通过依赖"创建多UE"，获取目标ues;对于MUE，该参数不生效',
                     regex=r"^((\s*\d+\s*)(-\s*\d+\s*)?(,\s*\d+\s*)?,?)*$|allue", ifEmptyUseDefault=False)

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        ues_field = cls.__fields__["ues"]
        if not values.get("ues"):
            if not ues_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["ues"] = ""
        return values

class McpUeIds(BaseModel):
    ues: str | None = Field("allue", description='MCP表示做业务的UE编号,支持压缩模式：1-3,6,8-9,也可通过依赖"创建多UE"，获取目标ues',
                     regex=r"^((\s*\d+\s*)(-\s*\d+\s*)?(,\s*\d+\s*)?,?)*$|allue", ifEmptyUseDefault=False)

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        ues_field = cls.__fields__["ues"]
        if not values.get("ues"):
            if not ues_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["ues"] = ""
        return values


class McpPsl(BaseModel):
    ues: str | None = Field("allue", description='MCP表示做业务的UE编号,支持压缩模式：1-3,6,8-9，也可通过依赖"创建多UE"，获取目标ues',
                     regex=r"^((\s*\d+\s*)(-\s*\d+\s*)?(,\s*\d+\s*)?,?)*$|allue", ifEmptyUseDefault=False)
    path: DataT = Field(None, type="url", source="tdl", returnType="array", viewType="tree", params=[],
                           url="https://zxmte.zte.com.cn:3303/tdl/other/sftp_dir_tree?path=/sftpuser/mcp_psl",
                           description='服务器上psl脚本存放路径，注意:请先确保psl已经上传到服务器;如果不选择psl,将会直接运行mcp上已加载的psl')
    parameters: DataT = Field(default=None, type="jsonEditor", description='原始psl脚本中的local属性，可通过本参数进行修改'
                                                                           '(注意:无法对mcp上已加载的psl修改属性)')

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        ues_field = cls.__fields__["ues"]
        if not values.get("ues"):
            if not ues_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["ues"] = ""
        return values


class McpCmd(BaseModel):
    ues: str | None= Field("allue", description='MCP表示做业务的UE编号,支持压缩模式：1-3,6,8-9，也可通过依赖"创建多UE"，获取目标ues',
                     regex=r"^((\s*\d+\s*)(-\s*\d+\s*)?(,\s*\d+\s*)?,?)*$|allue", ifEmptyUseDefault=False)
    commandLine: str = Field(..., type='textarea', description='通用命令，具体命令可参考MCP使用说明书')
    expected: str | None = Field("~End~", description='命令期望回显值(支持正则),不填表示任意回显值', ifEmptyUseDefault=False)

    @root_validator(pre=True)
    def handle_empty_values_ues(cls, values):
        ues_field = cls.__fields__["ues"]
        if not values.get("ues"):
            if not ues_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["ues"] = ""
        return values

    @root_validator(pre=True)
    def handle_empty_values_expected(cls, values):
        expected_field = cls.__fields__["expected"]
        if not values.get("expected"):
            if not expected_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["expected"] = ""
        return values


class volteVoiceCall(BaseModel):
    callingUeAlias: str = Field(..., description='主叫UE别名')
    calledUeAlias: str = Field(..., description='被叫UE别名')
    callType: Literal['voice', 'video'] = Field('voice', description='默认语音呼叫')

startLogTypeDict = {'SIGNAL': '100', 'KPI': '17'}
stopLogTypeDict = {'SIGNAL': '102', 'KPI': '17'}

UE_SIGNAL_PRASE_TEMPLATE = {
    "Name": "SystemInformation",
    "Direction": "DOWNLINK",
    "MsgType": "RRC_MSG",
    "RRCProtocolVer": 0,
    "NasProtocolVer": 1,
    "SccModeAndPduType": 0,
    "ChannelId": 96
}

class startLogCollection(BaseModel):
    logType: Literal['SIGNAL', 'KPI'] = Field('SIGNAL', description='默认开启信令日志采集')

class startSignalLogPrase(BaseModel):
    filterDict: DataT = Field(default=UE_SIGNAL_PRASE_TEMPLATE, type="jsonEditor", description="信令解析模板，按照标准格式填写 任务参数")
    logPraseType: Literal['SINGLE', 'MULTIPLE'] = Field('SINGLE', description='信令解析，可选：SINGLE、MULTIPLE。'
                                                                              '默认【SINGLE】：过滤并解析其中一条，如果信令比较多，使用。'
                                                                              '【MULTIPLE】：过滤指定信令，符合要求的信令都进行解析，适用于需要通过解析结果来进行判定的情况')

class stopLogCollection(BaseModel):
    logType: Literal['SIGNAL', 'KPI'] = Field('SIGNAL', description='默认关闭信令日志采集')

class enterLogIntoIds(BaseModel):
    logType: Literal['KPI'] = Field('KPI', description='选择UE日志入库类型，默认KPI')

class ftpUpLoad(BaseModel):
    upLoadFileName: str = Field(None, description='设置FTP目标文件名，不填该值，默认自动生成，本地不用存储文件')
    upLoadSize: Literal['10G', '20G', '30G', '40G', '50G'] =  Field('40G', description='指定上传文件大小，默认40G')
    isKeepAlive: bool = Field(False, description='保活开关，链路断开时，判断是否自动尝试连接')
    threadCount: Literal['1', '2', '3', '5', '10'] = Field('1', description='并发线程数，默认为1')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='UEIP, 默认IPV4')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')

class ftpDownLoad(BaseModel):
    downLoadFileName: str = Field(None, description='指定FTP下载的源文件名，本地不用存储文件。'
                                                    '填写时：1、单文件名，如：Bizcode_UE1_upLoadTest.txt；2、带路径的文件名：如：/home/<USER>/Bizcode_UE1_upLoadTest.txt。'
                                                    '不填时：按downLoadSize在FTP用户根目录随机选择文件下载。')
    downLoadSize: Literal['10G', '20G', '30G', '40G', '50G'] = Field('40G', description='指定下载文件大小，默认40G')
    isKeepAlive: bool = Field(False, description='保活开关，链路断开时，判断是否自动尝试连接')
    threadCount: Literal['1', '2', '3', '5', '10'] = Field('1', description='并发线程数，默认为1')
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='UEIP, 默认IPV4')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')

class startSpeedTestBox(BaseModel):
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    pdnIp: str = Field(None, description='SpeedTest时pdn控制ip地址,选填,不填默认从环境信息中获取')
    url: str = Field('http://*************/speedtest-4.5.5_1/', description='SpeedTest的URL选填,默认http://*************/speedtest-4.5.5_1/')
    count: str = Field('2', description='SpeedTest的次数，默认1')
    start_time: str = Field('0', description='SpeedTest的开始等待时间，默认1（单位：秒）')
    end_time: str = Field('20', description='SpeedTest的结束等待时间，默认20（单位：秒）')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')

class startE500TestBox(BaseModel):
    script_files: str = Field('d:/scripts/1.txt;d:/scripts/2.txt', description='E500执行脚本，默认d:/scripts/1.txt')
    log_path: str = Field('d:/tm500_log', description='E500执行log的存放路径,默认（d:/tm500_log）')
    telnet_host: str = Field('**************', description='E500直连PC的IP,默认（**************）')
    telnet_port: str = Field('5003', description='E500端口,默认（5003）')
    start_time_flag: str = Field('CONNECT', description='E500业务开始文件标识,默认()')
    end_time_flag: str = Field('C: DISCONNECT 0x00 OK', description='E500业务结束文件标识,默认(C: DISCONNECT 0x00 OK)')
    remark: str = Field('', description='标志,默认无')

class startLockCellPciBox(BaseModel):
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    pdnIp: str = Field(None, description='E500Test时pdn控制ip地址,选填,不填默认从环境信息中获取')
    perl_file: str = Field('perl D:/ai-2024.5/lock_file_service/copy_pc_to_phone.pl', description='perl及copy_pc_to_phone.pl文件路径，默认perl D:/ai-2024.5/lock_file_service/copy_pc_to_phone.pl')
    pc_file: str = Field('D:/ai-2024.5/lock_file_service/pc/cell_lock_list', description='锁频文件的存放路径,默认（D:/ai-2024.5/lock_file_service/pc/cell_lock_list）')
    phone_file: str = Field('/nv/item_files/modem/lte/rrc/efs/cell_lock_list', description='ue中cell_lock_list的存储位置,默认（/nv/item_files/modem/lte/rrc/efs/cell_lock_list）')
    ipIndex: Literal['0', '1', '2', '3'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')

class touchOperation(BaseModel):
    action: Literal['点击', '上滑', '下滑', '左滑', '右滑', '截屏', '电源键', 'HOME键'] = Field('点击',
                                                                                                description='手机操作')
    x: int = Field(None, description='x坐标，action参数选择点击生效，手机坐标具体见下方指导手册')
    y: int = Field(None, description='y坐标，action参数选择（点击）生效，手机坐标具体见下方指导手册')
    slidingDistance: int = Field(500,
                                 description='手指滑动距离,action参数选择（上滑）（下滑）（左滑）（右滑）生效，具体距离跟位移坐标有关')
    duration: int = Field(100,
                          description='手指滑动速度，单位ms,action参数选择（上滑）（下滑）（左滑）（右滑）生效')
    instructions: DataT = Field(None, type="label", viewType="url", urlName="获取坐标指导手册",
                               url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/31b38e64684c4debb8505d57e2210972/view")


class AppOperate(BaseModel):
    appName: Literal['系统', '抖音', '快手', '微信', '爱奇艺', '腾讯会议'] = Field('抖音', description='app名称,如有增量需求,请反馈')
    operate: Literal['滑动手机屏幕', '关闭最近打开的所有应用', '打开抖音', '打开快手', '打开微信', '打开微信钱包', '微信语音呼叫',
    '微信视频呼叫', '接听微信音视频呼叫', '挂断微信音视频通话', '播放长视频', '打开会议', '开始快速会议', '结束会议', '离开会议',
    '获取会议号', '加入会议']  = Field("打开抖音", description='app操作选项')
    calledName: str  = Field("张三", description='被叫名称,微信上的被叫昵称,如果有设置备注,请传入备注名称')
    meetingNo: str  = Field(..., description='加入的腾讯会议会议号')
    swipeType: Literal['上滑', '下滑', '左滑', '右滑']  = Field("上滑", description='滑动操作选项')
    @classmethod
    def schema(cls):
        schema_map = [
            ({"appName": ["系统"], "operate": ["滑动手机屏幕"]},
             {"operate": ["滑动手机屏幕", "关闭最近打开的所有应用"], "swipeType": ['上滑', '下滑', '左滑', '右滑']}),
            ({"appName": ["系统"], "operate": ["关闭最近打开的所有应用"]},
             {"operate": ["关闭最近打开的所有应用", "滑动手机屏幕"]}),
            ({"appName": ["系统"]}, {"operate": ["滑动手机屏幕", "关闭最近打开的所有应用"]}),
            ({"appName": ["抖音"]}, {"operate": ["打开抖音"]}),
            ({"appName": ["快手"]}, {"operate": ["打开快手"]}),
            ({"appName": ["微信"], "operate": ["微信视频呼叫"]},
             {"operate": ["微信视频呼叫", "打开微信", "打开微信钱包", "微信语音呼叫","接听微信音视频呼叫", "挂断微信音视频通话"],
              "calledName": "张三"}),
            ({"appName": ["微信"], "operate": ["微信语音呼叫"]},
             {"operate": ["微信语音呼叫", "打开微信", "打开微信钱包", "微信视频呼叫", "接听微信音视频呼叫", "挂断微信音视频通话"],
              "calledName": "张三"}),
            ({"appName": ["微信"]}, {"operate": ["打开微信", "打开微信钱包", "微信语音呼叫", "微信视频呼叫", "接听微信音视频呼叫",
                            "挂断微信音视频通话"]}),
            ({"appName": ["爱奇艺"]}, {"operate": ["播放长视频"]}),
            ({"appName": ["腾讯会议"], "operate": ["加入会议"]},
             {"operate": ["加入会议", "打开会议", "开始快速会议", "结束会议", "离开会议", "获取会议号"], "meetingNo": ""}),
            ({"appName": ["腾讯会议"]},
             {"operate": ["打开会议", "开始快速会议", "结束会议", "离开会议", "获取会议号", "加入会议"]})

        ]
        dynamic_paras = ["operate", "calledName", "swipeType", "meetingNo"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

AppOperate.schema()


class startUploadAlatBox(BaseModel):
    ume_ip: str = Field('************', description='UME的ip地址，默认************')
    site_id: str = Field('8198', description='站点编号，默认8198')
    alat_ip: str = Field('**************', description='ALAT的ip地址，默认**************')
    start_time: str = Field('2024-06-01 00:00', description='采集开始时间，默认2024-06-01 00:00')
    end_time: str = Field('2024-06-01 00:15', description='采集结束时间，默认2024-06-01 00:15')
    time_resolution: Literal['5 分钟', '15 分钟', '30 分钟', '1 小时'] = Field('5 分钟', description='5 分钟')
    remark: str = Field('test', description='采集用例描述，默认test')

class startAddServiceMgrBox(BaseModel):
    ipFlag: Literal['IPV4', 'IPV6'] = Field('IPV4', description='默认IPV4')
    src_path: str = Field('D:\ServiceMgrV1.2.7-linux', description='ServiceMgrBox的源位置，默认D:\ServiceMgrV1.2.7-linux')
    dst_path: str = Field('/home/<USER>/ServiceMgrV1.2.7-linux-1/', description='ServiceMgrBox的目标位置，默认/home/<USER>/ServiceMgrV1.2.7-linux-*/')
    udp_port: str = Field('9955', description='ServiceMgrBox的UDP端口，默认9955')
    tcp_port: str = Field('9956', description='ServiceMgrBox的TCP端口，默认9956')
    act: Literal['copy', 'modify'] = Field('copy', description='默认复制')
    ipIndex: Literal['0', '1', '2', '3', '4', '5', '6', '7'] = Field('0', description='通过ueIp序号控制业务行为，默认序号0，即第一个IP')

class startPrismaTestBox(BaseModel):
    py_file: str = Field('examplepuuee_testrun_rest_minimal.py', description='Prisma的ini文件,默认examplepuuee_testrun_rest_minimal.py')
    ssh_host: str = Field('**************', description='ssh直连PC的IP,默认（**************）')
    start_time_flag: str = Field('enable AMM for current scenario', description='开始业务标志,默认enable AMM for current scenario')
    end_time_flag: str = Field('INFO Results located', description='结束业务员标志,默认INFO Results located')
    remark: str = Field('', description='标志,默认无')


