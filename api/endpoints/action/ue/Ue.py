# -*- encoding: utf-8 -*-
'''
@File    :   Ue.py
@Time    :   2023/08/31 15:57:44
<AUTHOR>   何为10156505
@Version :   1.0
@Contact :   <EMAIL>
@License :   (C)Copyright 2022-2030
@Desc    :   None
'''
import traceback
import re

from infrastructure.logger import Logger
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import fail, success
from domain.factories.UeFactory import UeFactory
from domain.factories.PdnFactory import PdnFactory
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from domain.models.ue.qualcomm_ue.Android import Android
from api.endpoints.action.ue.Schemas import startPingBox, startUlUdpPerfBox, stopLogTypeDict, \
    startLogCollection, startDlUdpPerfBox, startLogTypeDict, StartUlTcpPerfBox, stopLogCollection, \
    RecvUlTcpPerfBox, StartDlTcpPerfBox, recvDlTcpPerfBox, PduCreate, PduRelease, \
    configUe, volteVoiceCall, ftpUpLoad, ftpDownLoad, startSpeedTestBox, startE500TestBox, startLockCellPciBox, \
    touchOperation, startUploadAlatBox, startAddServiceMgrBox, startPrismaTestBox, AppOperate
from service.action.android.AndroidAppService import AndroidAppService

router = APIRouter(route_class=bizRoute)


@router.post("/reset", summary="UE同步", description=description("UE同步", "10263798"))
async def reset(request: Request):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.reset()
            if not rlt:
                return fail(f'该UE: {repr(ue.id)}，执行失败', data=rltInfo)
            ueRltDict[ue.id] = rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/ueAttach", summary="UE接入", description=description("UE接入（支持高频）", "10263798"))
async def attach(request: Request):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.attach()
            if not rlt:
                return fail(f'该UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/detach", summary="UE去附着", description=description("UE去附着（支持高频）", "10263798"))
async def detach(request: Request):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            ret, rltInfo = await ue.detach()
            if not ret:
                return fail(f'该UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/get_imsi", summary="获取UE的IMSI号", description=description("获取UE的IMSI号", "10263798"))
async def get_imsi(request: Request):
    try:
        ues = UeFactory.create(request)
        imsiDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.get_imsi()
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            imsiDict[ue.id] = rltInfo
        return success("ACTION 执行成功", data=imsiDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/query_state", summary="获取当前UE的信息", description=description("获取当前UE的信息（支持高频）", "10263798"))
async def query_state(request: Request):
    try:
        ues = UeFactory.create(request)
        ueStateDict = {}
        for ue in ues:
            rlt = await ue.query_state()
            ueStateDict[ue.id] = rlt
        return success("ACTION 执行成功", data=ueStateDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/start_ul_ping", summary="启动上行Ping", description=description("启动上行Ping（支持高频）", "10263798"))
async def start_ul_ping(request: Request, startPingBox: startPingBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startPingBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startPingBox.pdnIp)
            pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            pdnDesIp = pdn.cnIp
            if startPingBox.ipFlag == "IPV6":
                pdnDesIp = pdn.ipv6
            for ue in ues:
                trafficInfo = {}
                rlt, rltInfo = await ue.start_ping(pdnDesIp, startPingBox.packageSize, startPingBox.pingNum,
                                        startPingBox.interval, startPingBox.ipFlag, startPingBox.ipIndex)
                if rlt:
                    trafficInfo["taskId"] = rltInfo
                    trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                    trafficInfo['id'] = ue.id
                    trafficInfo['actionType'] = 'UE'
                    Logger.info(trafficInfo)
                    trafficInfoList.append(trafficInfo)
                else:
                    return fail(f'该UE: {repr(ue.id)} 执行失败', data=rltInfo)
        return success(f'ACTION 执行成功', data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/start_dl_ping", summary="启动下行Ping", description=description("启动下行Ping（支持高频）", "10263798"))
async def start_dl_ping(request: Request, startPingBox: startPingBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startPingBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startPingBox.pdnIp)
            pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            for ue in ues:
                trafficInfo = {}
                ueIp = await ue.get_ip(startPingBox.ipFlag, startPingBox.ipIndex)
                if ueIp and ueIp != '0.0.0.0':
                    rlt, rltInfo = await pdn.start_ping(ueIp, startPingBox.packageSize, startPingBox.pingNum,
                                             startPingBox.interval, startPingBox.ipFlag)
                    if rlt:
                        trafficInfo["taskId"] = rltInfo
                        trafficInfo['serviceMgrPort'] = pdn.serviceMgrPort
                        trafficInfo['id'] = pdn.id
                        trafficInfo['actionType'] ='PDN'
                        trafficInfoList.append(trafficInfo)
                    else:
                        return fail(f'该UE: {repr(ue.id)} 执行失败', data=rltInfo)
                else:
                    return fail(f"根据UE:{repr(ue.id)}, IP过滤类型：{repr(startPingBox.ipFlag)}, IP序号：{repr(startPingBox.ipIndex)}，获取IP地址:{repr(ueIp)}, 所以执行失败")
        return success(data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/stop_tasks", summary="UE结束业务", description=description("UE结束业务（支持高频）", "10263798"),
             openapi_extra={"refs": [{"actionName": "启动.*", "path": "/v1/api/action/ue", "refPattern": "1-*"}]})
async def stop_tasks(request: Request):
    try:
        ues = UeFactory.create(request)
        pdns = PdnFactory.create(request)
        trafficInfoLists = await Refs(request).get_output("trafficInfoList")
        sucRltList = []
        failRltList = []
        Logger.info(trafficInfoLists)
        for pdn in pdns:
            for trafficInfos in trafficInfoLists:
                for trafficInfo in trafficInfos:
                    rltDict = {}
                    if trafficInfo['actionType'] == 'PDN' and trafficInfo['id'] == pdn.id \
                            and trafficInfo['serviceMgrPort'] == pdn.serviceMgrPort:
                        rlt, rltInfo = await pdn.stop_task(trafficInfo['taskId'])
                        if not rlt:
                            rltDict['trafficInfo'] = trafficInfo
                            rltDict['queryResult'] = rlt, rltInfo
                            failRltList.append(rltDict)
                        rltDict['trafficInfo'] = trafficInfo
                        rltDict['queryResult'] = rlt, rltInfo
                        sucRltList.append(rltDict)
        for ue in ues:
            for trafficInfos in trafficInfoLists:
                for trafficInfo in trafficInfos:
                    rltDict = {}
                    if trafficInfo['actionType'] == 'UE' and trafficInfo['id'] == ue.id \
                            and trafficInfo['serviceMgrPort'] == ue.serviceMgrPort:
                        rlt, rltInfo = await ue.stop_task(trafficInfo['taskId'])
                        if not rlt:
                            rltDict['trafficInfo'] = trafficInfo
                            rltDict['queryResult'] = rlt, rltInfo
                            failRltList.append(rltDict)
                        rltDict['trafficInfo'] = trafficInfo
                        rltDict['queryResult'] = rlt, rltInfo
                        sucRltList.append(rltDict)
        if len(failRltList) > 0:
            return fail('ACTION 执行失败', data=failRltList)
        return success('ACTION 执行成功', data=sucRltList)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/createPDUsession", summary="UE创建PDUsession", description=description("UE创建PDUsession_5GNR，支持TUE（支持高频），高频必填参数：pdusessionid、dnn、pdusessionType其余默认", "10255283"))
async def create_PDUsession(request: Request, pduCreate:PduCreate):
    try:
        ues = UeFactory.create(request)
        PduPara = dict(pduCreate)
        pduType = {"IPV4": "1", "IPV6": "2", "IPV4IPV6": "3", "ETH": "4"}
        PduPara.update({'pdusessionType': pduType[PduPara['pdusessionType']]})
        stateList = []
        for ue in ues:
            rlt, rltInfo = await ue.change_pdu_config(PduPara)
            if not rlt:
                return fail(f'该UE: {repr(ue.id)} 执行失败', data=rltInfo)
            stateList.append(rlt)
        return success("ACTION 执行成功", data=stateList)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/releasePDUsession", summary="UE释放PDUsession", description=description("UE释放PDUsession_5GNR，支持TUE（支持高频）", "10255283"))
async def release_PDUsession(request: Request, pduRelease:PduRelease):
    try:
        ues = UeFactory.create(request)
        PduPara = {"pdurelease":pduRelease.pdusessionid}
        stateList = []
        for ue in ues:
            rlt, rltInfo = await ue.change_pdu_config(PduPara)
            if not rlt:
                return fail(f'该UE: {repr(ue.id)} 执行失败', data=rltInfo)
            stateList.append(rlt)
        return success("ACTION 执行成功", data=stateList)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/start_ul_udp_perf", summary="启动上行UDP灌包", description=description("启动上行UDP灌包（支持高频）", "10263798"))
async def start_ul_udp_perf(request: Request, startUlUdpPerfBox: startUlUdpPerfBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startUlUdpPerfBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startUlUdpPerfBox.pdnIp)
            pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        totalBandwidth = (str(float(startUlUdpPerfBox.bandwidth)*int(startUlUdpPerfBox.threadCount)))+"M"
        trafficInfoList = []
        for pdn in pdns:
            pdnDesIp = pdn.cnIp
            if startUlUdpPerfBox.ipFlag == "IPV6":
                pdnDesIp = pdn.ipv6
            for ue in ues:
                trafficInfo = {}
                rlt, rltInfo = await ue.start_udp_perf(pdnDesIp, startUlUdpPerfBox.perfPort, totalBandwidth, startUlUdpPerfBox.threadCount,
                                                 startUlUdpPerfBox.secs, startUlUdpPerfBox.length, startUlUdpPerfBox.ipFlag, startUlUdpPerfBox.ipIndex)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                trafficInfo['id'] = ue.id
                trafficInfo['actionType'] = 'UE'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
            Logger.info(trafficInfoList)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_dl_udp_perf", summary="启动下行UDP灌包", description=description("启动下行UDP灌包（支持高频）", "10263798"))
async def start_dl_udp_perf(request: Request, startDlUdpPerfBox: startDlUdpPerfBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startDlUdpPerfBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startDlUdpPerfBox.pdnIp)
            pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        totalBandwidth = (str(float(startDlUdpPerfBox.bandwidth)*int(startDlUdpPerfBox.threadCount)))+"M"
        trafficInfoList = []
        for pdn in pdns:
            for ue in ues:
                trafficInfo = {}
                ueIp = await ue.get_ip(startDlUdpPerfBox.ipFlag, startDlUdpPerfBox.ipIndex)
                if ueIp and ueIp != '0.0.0.0':
                    rlt, rltInfo = await pdn.send_udp(ueIp, startDlUdpPerfBox.perfPort, totalBandwidth,
                                             startDlUdpPerfBox.secs, startDlUdpPerfBox.length,
                                             startDlUdpPerfBox.threadCount,
                                             startDlUdpPerfBox.specifiedPath,
                                             startDlUdpPerfBox.ipFlag)
                    if not rlt:
                        return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                    trafficInfo["taskId"] = rltInfo
                    trafficInfo['serviceMgrPort'] = pdn.serviceMgrPort
                    trafficInfo['id'] = pdn.id
                    trafficInfo['actionType'] = 'PDN'
                    Logger.info(trafficInfo)
                    trafficInfoList.append(trafficInfo)
                else:
                    return fail(f"根据UE:{repr(ue.id)}, IP过滤类型：{repr(startDlUdpPerfBox.ipFlag)}, IP序号：{repr(startDlUdpPerfBox.ipIndex)}，获取IP地址:{repr(ueIp)}, 所以执行失败")
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_ul_tcp_perf", summary="启动上行TCP灌包", description=description("启动上行TCP灌包（支持高频）", "10263798"))
async def start_ul_tcp_perf(request: Request, startUlTcpPerfBox: StartUlTcpPerfBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startUlTcpPerfBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startUlTcpPerfBox.pdnIp)
            if not pdn:
                return fail(f"TCP业务需要填写的PDN资源在环境资源中，{startUlTcpPerfBox.pdnIp}不在环境资源中！！！")
            else:
                pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            pdnDesIp = pdn.cnIp
            if startUlTcpPerfBox.ipFlag == "IPV6":
                pdnDesIp = pdn.ipv6
            for ue in ues:
                trafficInfo = {}
                rlt, rltInfo = await ue.start_tcp_perf(pdnDesIp, startUlTcpPerfBox.perfPort, startUlTcpPerfBox.winSize,
                                              startUlTcpPerfBox.duration, startUlTcpPerfBox.isKeepAlive,
                                              startUlTcpPerfBox.parallel, startUlTcpPerfBox.ipFlag, startUlTcpPerfBox.ipIndex)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                trafficInfo['id'] = ue.id
                trafficInfo['actionType'] = 'UE'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/recv_ul_tcp_perf", summary="启动上行TCP收灌包", description=description("启动上行TCP收灌包（支持高频）", "10263798"))
async def recv_ul_tcp_perf(request: Request, recvUlTcpPerfBox: RecvUlTcpPerfBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if recvUlTcpPerfBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, recvUlTcpPerfBox.pdnIp)
            if not pdn:
                return fail(f"TCP业务需要填写的PDN资源在环境资源中，{recvUlTcpPerfBox.pdnIp}不在环境资源中！！！")
            else:
                pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            for ue in ues:
                trafficInfo = {}
                rlt, rltInfo = await pdn.recv_tcp(recvUlTcpPerfBox.perfPort, recvUlTcpPerfBox.parallel, recvUlTcpPerfBox.recvWin,
                                                  recvUlTcpPerfBox.ipFlag)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = pdn.serviceMgrPort
                trafficInfo['id'] = pdn.id
                trafficInfo['actionType'] = 'PDN'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_dl_tcp_perf", summary="启动下行TCP灌包", description=description("启动下行TCP灌包（支持高频）", "10263798"))
async def start_dl_tcp_perf(request: Request, startDlTcpPerfBox: StartDlTcpPerfBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startDlTcpPerfBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startDlTcpPerfBox.pdnIp)
            if not pdn:
                return fail(f"TCP业务需要填写的PDN资源在环境资源中，{startDlTcpPerfBox.pdnIp}不在环境资源中！！！")
            else:
                pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            for ue in ues:
                trafficInfo = {}
                ueIp = await ue.get_ip(startDlTcpPerfBox.ipFlag, startDlTcpPerfBox.ipIndex)
                if ueIp and ueIp != '0.0.0.0':
                    rlt, rltInfo = await pdn.send_tcp(ueIp, startDlTcpPerfBox.perfPort, startDlTcpPerfBox.winSize,
                                             startDlTcpPerfBox.duration, startDlTcpPerfBox.parallel,
                                             startDlTcpPerfBox.isKeepAlive,
                                             startDlTcpPerfBox.length,
                                             startDlTcpPerfBox.ipFlag,
                                             startDlTcpPerfBox.specifiedPath)
                    if not rlt:
                        return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                    trafficInfo["taskId"] = rltInfo
                    trafficInfo['serviceMgrPort'] = pdn.serviceMgrPort
                    trafficInfo['id'] = pdn.id
                    trafficInfo['actionType'] = 'PDN'
                    Logger.info(trafficInfo)
                    trafficInfoList.append(trafficInfo)
                else:
                    return fail(f"根据UE:{repr(ue.id)}, IP过滤类型：{repr(startDlTcpPerfBox.ipFlag)}, IP序号：{repr(startDlTcpPerfBox.ipIndex)}，获取IP地址:{repr(ueIp)}, 所以执行失败")
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/recv_dl_tcp_perf", summary="启动下行TCP收灌包", description=description("启动下行TCP收灌包（支持高频）", "10263798"))
async def recv_dl_tcp_perf(request: Request, recvDlTcpPerfBox: recvDlTcpPerfBox):
    try:
        ues = UeFactory.create(request)
        trafficInfoList = []
        for ue in ues:
            trafficInfo = {}
            rlt, rltInfo = await ue.recv_tcp_perf(recvDlTcpPerfBox.perfPort, recvDlTcpPerfBox.recvThread, recvDlTcpPerfBox.recvWin,
                                            recvDlTcpPerfBox.ipFlag, recvDlTcpPerfBox.ipIndex)
            if not rlt:
                return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
            trafficInfo["taskId"] = rltInfo
            trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
            trafficInfo['id'] = ue.id
            trafficInfo['actionType'] = 'UE'
            Logger.info(trafficInfo)
            trafficInfoList.append(trafficInfo)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/query_ue_task_info", summary="查询UE业务信息", description=description("查询UE业务信息（支持高频）", "10263798"),
             openapi_extra={"refs": [{"actionName": "启动.*", "path": "/v1/api/action/ue", "refPattern": "1-*"}]})
async def query_ue_task_info(request: Request):
    try:
        ues = UeFactory.create(request)
        pdns = PdnFactory.create(request)
        trafficInfoLists = await Refs(request).get_output("trafficInfoList")
        rltList = []
        Logger.info(trafficInfoLists)
        for pdn in pdns:
            for trafficInfos in trafficInfoLists:
                for trafficInfo in trafficInfos:
                    rltDict = {}
                    if trafficInfo['actionType'] == 'PDN' and trafficInfo['id'] == pdn.id \
                            and trafficInfo['serviceMgrPort'] == pdn.serviceMgrPort:
                        rlt = await pdn.query_task_info(trafficInfo['taskId'])
                        Logger.info(rlt)
                        if rlt:
                            Logger.info(rltList)
                            rltDict['trafficInfo'] = trafficInfo
                            rltDict['queryResult'] = rlt
                            rltList.append(rltDict)
        Logger.info(rltList)
        for ue in ues:
            for trafficInfos in trafficInfoLists:
                for trafficInfo in trafficInfos:
                    rltDict = {}
                    if trafficInfo['actionType'] == 'UE' and trafficInfo['id'] == ue.id \
                            and trafficInfo['serviceMgrPort'] == ue.serviceMgrPort:
                        rlt = await ue.query_task_info(trafficInfo['taskId'])
                        Logger.info(rlt)
                        if rlt:
                            rltDict['trafficInfo'] = trafficInfo
                            rltDict['queryResult'] = rlt
                            rltList.append(rltDict)
        return success('ACTION 执行成功', data=rltList)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/config_ue", summary="UE配置", description=description("UE配置", "10263798"))
async def config_ue(request: Request, configUe: configUe):
    try:
        ues = UeFactory.create(request)
        rltList = []
        for ue in ues:
            rltDict = {}
            Logger.info(configUe)
            rlt, rltInfo = await ue.config_ue(configUe)
            if not rlt:
                return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
            rltDict['id'] = ue.id
            rltDict['ueType'] = ue.ueType
            rltDict['configResult'] = rlt
            rltList.append(rltDict)
        return success('ACTION 执行成功', data=rltList)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/ueVonrCall", summary="UE语音视频呼叫", description=description("触发UE语音视频呼叫,callingUeAlias：主叫UE别名, calledUeAlias：被叫UE别名", "10263798"))
async def vonr_voice_video_call(request: Request, vcPara:volteVoiceCall):
    try:
        callingdUes = UeFactory.create_by_alias(request, vcPara.callingUeAlias)
        calledUes = UeFactory.create_by_alias(request, vcPara.calledUeAlias)
        if len(callingdUes) != 1:
            return fail('主叫UE只允许一个')
        if len(calledUes) != 1:
            return fail('被叫UE只允许一个')
        callingdUe = callingdUes[0]
        calledUe = calledUes[0]
        if callingdUe.telphoneNumber:
            rlt, rltInfo = await callingdUe.vonr_voice_call(calledUe.telphoneNumber, vcPara.callType)
            if not rlt:
                return fail(f'主叫UE: {repr(callingdUe.telphoneNumber)} --》 呼叫 --》 被叫UE: {repr(calledUe.telphoneNumber)} 失败', data=rltInfo)
            return success(f'ACTION 执行成功', data=rltInfo)
        else:
            return fail('UE配置中的电话号码为空')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/vonr_voice_call_connect", summary="UE语音视频呼叫接通", description=description("UE语音视频呼叫接通,callingUeAlias：主叫UE别名, calledUeAlias：被叫UE别名", "10263798"))
async def vonr_voice_call_connected(request: Request):
    try:
        ues = UeFactory.create(request)
        for ue in ues:
            rlt, rltInfo = await ue.ip_call_connect()
            if not rlt:
                return fail(f'UE: {repr(ue.telphoneNumber)} 执行失败', data=rltInfo)
        return success(f'ACTION 执行成功', data=rltInfo)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/vonr_voice_call_handUp", summary="UE语音视频呼叫挂断", description=description("UE语音视频呼叫挂断", "10263798"))
async def vonr_voice_call_release(request: Request):
    try:
        ues = UeFactory.create(request)
        for ue in ues:
            rlt, rltInfo = await ue.ip_call_release()
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
        return success(f'ACTION 执行成功', data=rltInfo)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/open_data_connection_function", summary="打开UE数据连接", description=description("打开UE数据连接", "10263798"))
async def open_data_connection_function(request: Request):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.open_data_connection()
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rlt, rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/close_data_connection_function", summary="关闭UE数据连接", description=description("关闭UE数据连接", "10263798"))
async def close_data_connection_function(request: Request):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo = await ue.close_data_connection()
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rlt, rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/ftp_upload", summary="UE启动FTP上传文件", description=description("UE启动FTP上传文件", "10263798"))
async def ftp_upload(request: Request, para: ftpUpLoad):
    try:
        ues = UeFactory.create(request)
        pdns = PdnFactory.create(request)
        Logger.debug(para)
        trafficInfoList = []
        for ue in ues:
            for pdn in pdns:
                trafficInfo = {}
                rlt, rltInfo = await ue.ftp_upload(pdn, para.upLoadFileName, para.upLoadSize, para.isKeepAlive, para.threadCount, para.ipFlag, para.ipIndex)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，pdnId: {1}, ACITON执行失败, 失败信息：{2}".format(ue.id, pdn.id, rltInfo))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                trafficInfo['id'] = ue.id
                trafficInfo['actionType'] = 'UE'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/ftp_download", summary="UE启动FTP下载文件", description=description("UE启动FTP上传文件", "10263798"))
async def ftp_download(request: Request, para: ftpDownLoad):
    try:
        ues = UeFactory.create(request)
        pdns = PdnFactory.create(request)
        Logger.debug(para)
        trafficInfoList = []
        for ue in ues:
            for pdn in pdns:
                trafficInfo = {}
                rlt, rltInfo = await ue.ftp_download(pdn, para.downLoadFileName, para.downLoadSize, para.isKeepAlive, para.threadCount, para.ipFlag, para.ipIndex)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，pdnId: {1}, ACITON执行失败, 失败信息：{2}".format(ue.id, pdn.id, rltInfo))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                trafficInfo['id'] = ue.id
                trafficInfo['actionType'] = 'UE'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_speedtest", summary="启动SPEEDTEST", description=description("启动SPEEDTEST（目前只支持高频）", "10007835"))
async def start_speedtest(request: Request, startSpeedTestBox: startSpeedTestBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startSpeedTestBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startSpeedTestBox.pdnIp)
            pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            pdnDesIp = pdn.cnIp
            for ue in ues:
                trafficInfo = {}
                rlt, rltInfo = await ue.start_speedtest(pdnDesIp, startSpeedTestBox.url, startSpeedTestBox.count,
                                                 startSpeedTestBox.start_time, startSpeedTestBox.end_time, startSpeedTestBox.ipFlag, startSpeedTestBox.ipIndex)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                trafficInfo['id'] = ue.id
                trafficInfo['actionType'] = 'UE'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
            Logger.info(trafficInfoList)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_e500", summary="启动E500", description=description("启动E500(目前只支持高频)", "10007835"))
async def start_e500(request: Request, startE500TestBox: startE500TestBox):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo =  await ue.start_e500(startE500TestBox.script_files, startE500TestBox.log_path,\
                                                startE500TestBox.telnet_host, startE500TestBox.telnet_port,\
                                                startE500TestBox.start_time_flag, startE500TestBox.end_time_flag,\
                                                startE500TestBox.remark)
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rlt, rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))
    

@router.post("/start_lockFreqPci", summary="启动lockFreqPci", description=description("启动lockFreqPci（目前只支持高频）", "10007835"))
async def start_lockFreqPci(request: Request, startLockCellPciBox: startLockCellPciBox):
    try:
        ues = UeFactory.create(request)
        print('1000000')
        print('ues:', ues)
        pdns = []
        if startLockCellPciBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startLockCellPciBox.pdnIp)
            print('1000001')
            print('pdn:', pdn)
            pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        print('10000002')
        print('pdns:', pdns)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            pdnDesIp = pdn.cnIp
            for ue in ues:
                trafficInfo = {}
                rlt, rltInfo = await ue.start_lockFreqPci(pdnDesIp, startLockCellPciBox.perl_file, startLockCellPciBox.pc_file,
                                                 startLockCellPciBox.phone_file, startLockCellPciBox.ipFlag, startLockCellPciBox.ipIndex)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                trafficInfo['id'] = ue.id
                trafficInfo['actionType'] = 'UE'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
            Logger.info(trafficInfoList)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/ue_touch_operation", summary="手机模拟触摸操作",
             description=description(
                 "手机按坐标触摸点击，上下滑动操作，只适用安卓终端，需要bizcore和手机放在一台电脑,如果电脑有多个商用终端，请在UE下填写deviceId属性，在电脑adb device查询",
                 "10255283"))
async def ue_touch_operation_function(request: Request, paras: touchOperation):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            if "QUALCOMM" not in ue.ueType:
                return fail("只支持商用终端，请在资源选择过滤")
            rlt = await Android().simulate_touch_operation(paras.action, paras.x, paras.y, paras.slidingDistance,
                                                           paras.duration, ue.deviceId)
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rlt)
            ueRltDict[ue.id] = rlt
        return success(f'ACTION 执行成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_uploadAlat", summary="启动上传ALAT", description=description("启动上传ALAT(目前只支持高频)", "10007835"))
async def start_uploadAlat(request: Request, startUploadAlatBox: startUploadAlatBox):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo =  await ue.start_uploadAlat(startUploadAlatBox.ume_ip, startUploadAlatBox.site_id,
                                                 startUploadAlatBox.alat_ip, startUploadAlatBox.start_time, startUploadAlatBox.end_time,
                                                         startUploadAlatBox.time_resolution, startUploadAlatBox.remark)
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rlt, rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_addServiceMgr", summary="启动增加ServiceMgr", description=description("启动增加ServiceMgr（目前只支持高频）", "10007835"))
async def start_addServiceMgr(request: Request, startAddServiceMgrBox: startAddServiceMgrBox):
    try:
        ues = UeFactory.create(request)
        pdns = []
        if startAddServiceMgrBox.pdnIp:
            pdn = PdnFactory.create_by_pdnIp(request, startAddServiceMgrBox.pdnIp)
            pdns.append(pdn)
        else:
            pdns = PdnFactory.create(request)
        if not pdns:
            return fail(f'pdn资源为空，action执行失败，请输入pdn资源或者在环境中配置！！！', data=pdns)
        trafficInfoList = []
        for pdn in pdns:
            pdnDesIp = pdn.cnIp
            for ue in ues:
                trafficInfo = {}
                rlt, rltInfo = await ue.start_addServiceMgr(pdnDesIp, startAddServiceMgrBox.src_path, startAddServiceMgrBox.dst_path,
                                                           startAddServiceMgrBox.udp_port, startAddServiceMgrBox.tcp_port, startAddServiceMgrBox.act,
                                                         startAddServiceMgrBox.ipFlag, startAddServiceMgrBox.ipIndex)
                if not rlt:
                    return fail(data=rltInfo, msg="ueId:{0}，ACITON执行失败".format({ue.id}))
                trafficInfo["taskId"] = rltInfo
                trafficInfo['serviceMgrPort'] = ue.serviceMgrPort
                trafficInfo['id'] = ue.id
                trafficInfo['actionType'] = 'UE'
                Logger.info(trafficInfo)
                trafficInfoList.append(trafficInfo)
            Logger.info(trafficInfoList)
        return success("ACTION 执行成功", data={"trafficInfoList": trafficInfoList})
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))

@router.post("/start_prisma", summary="启动Prisma", description=description("启动Prisma(目前只支持高频)", "10007835"))
async def start_prisma(request: Request, startPrismaTestBox: startPrismaTestBox):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            rlt, rltInfo =  await ue.start_prisma(startPrismaTestBox.py_file, startPrismaTestBox.ssh_host,
                                                  startPrismaTestBox.start_time_flag, startPrismaTestBox.end_time_flag, startPrismaTestBox.remark)
            if not rlt:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rltInfo)
            ueRltDict[ue.id] = rlt, rltInfo
        return success(f'ACTION 执行成功', data=ueRltDict)
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail("ACTION 执行失败", data=str(e))


@router.post("/ue_operate_app", summary="操作手机APP",
             description=description(
                 "操作手机APP,仅支持商用终端(注意:请在UE下填写deviceId属性,可在终端电脑adb device查询)",
                 "10270755"))
async def ue_touch_operation_function(request: Request, paras: AppOperate):
    try:
        ues = UeFactory.create(request)
        ueRltDict = {}
        for ue in ues:
            if "QUALCOMM" not in ue.ueType:
                return fail("只支持商用终端，请在资源选择过滤")
            if not ue.deviceId:
                return fail(f"{ue.id} 缺少deviceId属性,请在UE下填写deviceId属性,可在终端电脑adb device查询")
            rlt = await AndroidAppService().app_operator(ue, paras.operate, dict(paras))
            if rlt is False:
                return fail(f'UE: {repr(ue.id)} 执行失败', data=rlt)
            ueRltDict[ue.id] = rlt
        return success(f'操作手机APP{paras.appName}{paras.operate}成功')
    except Exception as e:
        traceback.print_exc()
        logE = traceback.format_exc()
        Logger.error(logE)
        return fail(f"操作手机APP'{paras.appName}'指令'{paras.operate}'执行失败,原因:{e}")

