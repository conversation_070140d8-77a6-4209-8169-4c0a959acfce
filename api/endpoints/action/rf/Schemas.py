"""
@File: Schemas.py
@Author: 许王禾子10333721
@Time: 2024/1/8 上午11:11
@License: Copyright 2022-2030
@Desc: None
"""
from typing import TypeVar
from pydantic import BaseModel, Field

DataT = TypeVar('DataT')  # 创建一个 TypeVar 的实例


class ExecRFInfo(BaseModel):
    isUpdateScript: bool = Field(False, description="是否更新代码")
    caseId: str | None = Field(None, description="用例Id，例 RAN-123456，不填则用action绑定的用例Id")
    configPath: str | None = Field(None, description="config文件地址，例 D:\\RF\\config.py，不填则用默认地址: 5GNR\\testlib5g\infrastructure\\envconfig\\config.py")
    localScriptPath: str | None = Field(None, description="本地代码库地址，如 D:\\script_v3\\, 不填会自动拉取最新代码")

    # localPythonPath: str | None = Field(None, description="本地python地址，如 C:\\python\\, 必须在config中配置本地bizcode")
    # rfInfo: DataT = Field(..., description="RF用例", type="func", source="tdl", returnType="array", viewType="select", params=[],
    #                       func="service.platform.param_filler.TestcaseService.TestcaseService.query_para_file_paths")
