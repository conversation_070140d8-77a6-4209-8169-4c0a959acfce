"""
@File: RF.py
@Author: 许王禾子10333721
@Time: 2024/1/8 上午11:06
@License: Copyright 2022-2030
@Desc: None
"""
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import success, fail
from api.endpoints.action.rf.Schemas import ExecRFInfo
from domain.platform.ActionInfo import description
from domain.models.rf.script.basemodel.EnvInfo import EnvInfo
from domain.models.rf.script.basemodel.ExecInfo import ExecInfo
from domain.platform.slave.Slave import Slave
from service.action.rf.RFService import RFService

router = APIRouter(route_class=bizRoute)


@router.post("/exec_test_case", summary="执行RF用例", description=description("执行RF用例,必须配置本地bizcode", "许王禾子 10333721"))
async def exec_test_case(request: Request, paras: ExecRFInfo):
    if not Slave().is_local():
        return fail(msg="执行失败，本action必须在本地bizcode执行")

    caseId = paras.caseId if paras.caseId else request.state.resource.get("caseId", None)
    if not caseId:
        return fail(msg="执行失败，未填写用例Id且当前action未绑定用例")

    envInfo = EnvInfo.parse_obj(paras.dict())
    execInfo = ExecInfo.parse_obj(paras.dict())
    execInfo.execType = "scriptcase"
    execInfo.caseId = caseId

    res, msg = await RFService().exec_script_case(envInfo, execInfo)

    if not res:
        return fail(msg=f"执行失败，{msg}")
    return success(msg=f"执行成功，msg: {msg}")




