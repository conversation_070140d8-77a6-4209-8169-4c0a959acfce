# -*- encoding: utf-8 -*-
"""
@File    :   Schemas.py
@Time    :    2023/11/2 15:22:44
<AUTHOR>   潘耀10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

from pydantic import BaseModel, Field
from enum import Enum
from typing import TypeVar

DataT = TypeVar('DataT')


#VSW testcase
LOWCODE_TEMPLATE = '''
            dockerName = 'cos'
            res = await SEND_CMD({f'cmd': 'docker ps -a | grep ' + dockerName, 'expected': '#', 'timeout': 3})
            returnStr = res.RETURN_STRING
            print('res>>', returnStr)
            containerId = []
            findList = re.findall('.*', returnStr)
            for findI in findList:
                if re.search('Up', findI) and re.search('(\\s{0}|[0-9a-z]+ *[0-9\\.:]+/{0}:)'.format(dockerName), findI,
                                                        re.I):
                    containerId.append(re.split(' ', findI)[0])
            print('containerId>>', containerId)
            returnStrs = await SEND_CMD({'cmd': 'docker exec -it ' + containerId[0] + ' sh', 'expected': '#', 'timeout': 3})
            print('returnStrs',returnStrs.RETURN_STRING)
            await ENTER_PROC(dockerName)
            returnStrs = await SEND_CMD({'cmd': 'DmimoShow', 'expected': '', 'timeout': 3})
            result= {'code':0,'data':returnStrs.RETURN_STRING ,'message':'xxx execution completed！'}
            SET_LOW_CODE_RESULT(result)
            '''

#AAU testcase
LOWCODE_TEMPLATE1 = '''
    process = 'MGR.EXE'
    await ENTER_PROC(process)
    returnStrs = await SEND_CMD({'cmd': 'dbtr 3', 'expected': 'end to excel fun:dbtr', 'timeout': 3, 'failedRetried': 3})
    SET_DATA(returnStrs.RETURN_STRING)
'''

#RANCLI testcase
LOWCODE_TEMPLATE2 = '''
    command = 'eac queryUeNumGnodeB GNBCUCPFunction=460-01_19234'
    returnStrs = await SEND_CMD(command)
    SET_DATA(returnStrs)
'''

LOWCODE_TEMPLATE3 = '''
dockerName = 'lccm'
res = await SEND_CMD({f'cmd': 'docker ps -a | grep ' + dockerName, 'expected': '#', 'timeout': 3})
returnStr = res.RETURN_STRING
containerId = []
findList = re.findall('.*', returnStr)
for findI in findList:
    if re.search('Up', findI) and re.search('(\\s{0}|[0-9a-z]+ *[0-9\\.:]+/{0}:)'.format(dockerName), findI,
                                            re.I):
        containerId.append(re.split(' ', findI)[0])
returnStrs = await SEND_CMD({'cmd': 'docker restart ' + containerId[0], 'expected': '#', 'timeout': 3})
result= {'code':0,'data':returnStrs,'message':'xxx execution completed！'} 
SET_LOW_CODE_RESULT(result)
'''


LOWCODE_TEMPLATE4 = '''
dockerName = 'lrrm-hf|lrrm_hf'
cmd = "showCellAllUes"
expectedInfo = "end to excel fun:showCellAllUes"
await ENTER_PROC(dockerName)
returnStrs = await SEND_CMD({'cmd': cmd, 'expected': expectedInfo, 'timeout': 3})
SET_DATA(returnStrs.RETURN_STRING)
match = re.search(r'allCellUeNum\s*=\s*(\d+)', returnStrs.RETURN_STRING)
num = int(match.group(1))
result = 0
if num == 6:
    result = 1

SET_LOW_CODE_RESULT(result)'''


class OptionEnum(str, Enum):
    value = "VSW"
    value1 = "VBP"
    value2 = "AAU"
    value3 = "RRU"
    value4 = "RANCLI"
    value5 = "VGC"
    value6 = "SE"
    value7 = "UCLI"


class LowCodeContractCommand(BaseModel):
    guidebook: DataT = Field(None, type="label", viewType="url", urlName="敲桩命令指南",
                                       url="https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/"
                                           "wiki/page/66555b4a145f49679dd713dc46d7f783/view")
    deviceType: OptionEnum
    lowCode: DataT = Field('', type="jsonEditor", viewType="text",
                               description="低代码编辑平台，支持指令发送: await SEND_CMD(命令)//结果解析:结果.RETURN_STRING//"
                                           "设置action返回值：SET_LOW_CODE_RESULT（返回值）//进入进程：await ENTER_PROC（进程）//"
                                           "支持python代码用正则过滤数据，详细文档见guidebook链接中的示例")
