# -*- encoding: utf-8 -*-
"""
@File    :   ForeGround.py
@Time    :   2023/11/2 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

from fastapi import APIRouter, Request
from api.Response import fail, success
from api.endpoints.action.foreground.Schemas import LowCodeContractCommand
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from service.action.contract.ForegroundService import ForegroundService
from infrastructure.resource.service.DeviceManager import DeviceManager
from domain.factories.MeFactory import MeFactory

router = APIRouter(route_class=bizRoute)

@router.post("/executeLowCodeForeGround", summary="前台发送低代码指令",
             description=description("用户通过低代码模块，编写简单的python代码实现不同进程的功能，该功能等价与契约的FUNC功能", "10263798"),
             openapi_extra={
                 "refs": [{"actionName": "低代码", "refPattern": "0-*"}]})
async def send_low_code_command_to_foreground(request: Request, paras: LowCodeContractCommand):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    taskInfo, errList, devices = [], [], []
    if paras.deviceType == 'RANCLI':
        devices = await MeFactory.create(request)
    elif paras.deviceType == 'UCLI':
        mes = await MeFactory.create(request)
        devices, umes = [], set()
        for me in mes:
            if me.umeDevice.ip not in umes:
                devices.append(me)
                umes.add(me.umeDevice.ip)

    elif paras.deviceType == 'CPE':
        return fail(data={"taskInfo": taskInfo}, msg="当前不支持CPE设备敲桩，如有需求请提交TDL需求！")
    elif paras.deviceType in ['VSW', 'VBP','AAU', 'RRU', 'VGC', "SE"]:
        devices = deviceManager.find_devices(paras.deviceType)
    if not devices: return fail(msg=f"找不到{paras.deviceType}设备，请检查环境配置！！！")
    for device in devices:
        ans = await ForegroundService().execute_stub(device, paras, deviceManager, request)
        if ans.code != 0:
            errList.append(ans.message)
        if ans.data:
            taskInfo.append(ans.data)
    if errList:
        return fail(data={"taskInfo": taskInfo}, msg=errList)
    return success(data={"taskInfo": taskInfo}, msg="Execute foreground stub successful.")