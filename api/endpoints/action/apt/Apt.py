"""
@File: Apt.py
@Author: 侯小飞10270755
@Time: 2025-06-23 19:02:46
@License: Copyright 2022-2030
@Desc: None
"""
from fastapi import APIRouter, Request
from api.route.BizRoute import bizRoute
from api.Response import success, fail
from api.endpoints.action.apt.Schemas import DataToApt
from domain.factories.CellFactory import CellFactory
from domain.platform.ActionInfo import description
from domain.factories.MeFactory import MeFactory
from infrastructure.resource.domain.db.ActionFilter import ActionFilter
from infrastructure.resource.service.Refs import Refs
from service.action.apt.AptService import AptService
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/upload_data_to_apt", summary="上传数据至零点系统", description=description("上传数据至零点系统(仅支持5G)",
                                                                                          "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "导出MTS采集任务", "refPattern": "0-1"},
                                     {"actionName": "导出网元xml配置文件", "refPattern": "0-1"}]
                            })
async def upload_data_to_apt(request: Request, paras: DataToApt):
    cells = await CellFactory.create(request)
    if ActionFilter().is_filtered_cell(request.state.resource.get("pipelineStageId")):
        cell = cells[0]
        if not hasattr(cell, 'gnb'):
            fail(msg=f"上传数据至零点系统不支持4G小区,请修改资源过滤")
        me = await MeFactory.create_by_meid(request, cell.me.meId)
    else:
        mes = await MeFactory.create_by_nr(request)
        if not mes:
            fail(msg=f"上传数据至零点系统仅支持5G,请修改资源过滤")
        me = mes[0]
        cell = list(filter(lambda cellt: cellt.me.meId == me.meId and hasattr(cellt, 'gnb'), cells))[0]
    queryRlts = await Refs(request).get_output()
    Logger.debug(f'依赖的action输出:{queryRlts}')
    Logger.debug(f'当前上传数据网元:{cell.me.meId}, 小区信息: {cell.cellId}')
    try:
        ret, err = await AptService.upload_data(request, me, cell, paras, queryRlts)
        if ret:
            return success(msg="上传数据至零点系统成功")
        return fail(msg=f"上传数据至零点系统失败: {err}")
    except Exception as err:
        return fail(msg=f"上传数据至零点系统异常: {err}")
