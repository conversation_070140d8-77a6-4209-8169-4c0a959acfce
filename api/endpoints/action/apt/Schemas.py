"""
@File: Schemas.py
@Author: 许王禾子10333721
@Time: 2024/1/8 上午11:11
@License: Copyright 2022-2030
@Desc: None
"""
from typing import TypeVar, Literal
from pydantic import BaseModel, Field
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema
LTE_DATA_TYPE = ['lte_throughput', 'lte_attach', 'lte_idleFirstPing', 'lte_paging', 'lte_ping', 'lte_handover']
ULDEMODULATION = ['uldemodulation']
DATA_FILE_TYPES=['WXDATA', 'MTS', 'UELOG', 'XML']

DataT = TypeVar('DataT')  # 创建一个 TypeVar 的实例
TEMP_JSON = {
    "UETYPE":"X55",
    "CASESCENE":"SA峰值流量",
    "EQUIPMENTID": "RT-T02-RF-3168",
    "NETYPE": "SA",
    "TESTGROUP": "深圳测试二队"
}

class DataToApt(BaseModel):
    dataType:  str = Field("ping", description='数据类型名称')
    wxData: DataT = Field(None, type="uploadFile", returnType="array", viewType="uploadFile", fileMaxSize="100",
                            allowedFileType="*.csv", limit=1, params=[],
                            description="csv格式WXDATA文件")
    otherDataJson: DataT = Field(default=TEMP_JSON,
                             type="jsonEditor", description='其他属性,可参考使用说明进行入参')
