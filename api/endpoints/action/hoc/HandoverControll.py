from fastapi import APIRouter, Request
from api.Response import fail, success
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from domain.models.hoc.signalcontroller.HandoverController import HandoverController
from api.endpoints.action.hoc.Schemas import AttenuateSignal, PullFarAndClose
from domain.factories.HocFactory import HocFactory

router = APIRouter(route_class=bizRoute)


@router.post("/set", summary="设置HOC信号衰减",
             description=description("通过端口调节衰减器信号衰减,目前需要白色衰减器接入专网环境或者部署本地bizcode","杨先恒10255283"))
async def set_hoc_Signal_attenuation(request: Request,attenuateSignal: AttenuateSignal):
    hocs = HocFactory.create(request)
    errList = []
    hocPortLists = attenuateSignal.hocPorts.split("&")
    hocValueLists = attenuateSignal.hocValues.split("&")
    for hoc in hocs:
        hocValueList = hocValueLists[0].split(",")
        hocPortList = hocPortLists[0].split(",")
        msg = await hoc.signal_attenuation_by_port(hocValueList, hocPortList)
        if msg is not None:
            errList.append(f"!!!衰减器{hoc.id}没有设置成功，原因是：{msg}！！！")
    if errList:
        return fail(msg=errList)
    return success(data="所有衰减设置成功")

@router.post("/pull", summary="HOC调节拉远拉近",
             description=description("在当前端口按照配置速率拉远拉近","杨先恒10255283"))
async def pullfar_and_pushin_by_port(request: Request,pullFarAndClose: PullFarAndClose):
    hocs = HocFactory.create(request)
    farValueList = [int(x) for x in pullFarAndClose.farTargetValue.split(",")]
    farValueSourceList = [int(x) for x in pullFarAndClose.farSourceValue.split(",")]
    farPortList = [int(x) for x in pullFarAndClose.pullFarPort.split(",")]
    stepList = [int(x) for x in pullFarAndClose.step.split(",")]
    errList = []
    for hoc in hocs:
        msg = await hoc.pullfar_and_close_by_port(farPortList, farValueList, farValueSourceList, stepList, pullFarAndClose.interval)
        if msg is not None:
            errList.append(f"!!!衰减器{hoc.id}没有设置成功，原因是：{msg}！！！")
    if errList:
        return fail(msg=errList)
    return success(data="所有衰减设置成功")


