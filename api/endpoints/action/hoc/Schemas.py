#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/8/31 14:02
# <AUTHOR> 10255283
from enum import Enum
from typing import Annotated, List
from pydantic import BaseModel, Field
from typing_extensions import Literal

class AttenuateSignal(BaseModel):

    hocPorts: str = Field("1,2,3,4", description="调节多个端口','隔开,多个衰减器用'&'隔开,如'1,2,3,4&3,8',表示衰减器\
                                                             1调试1234端口，衰减器2调试38端口")
    hocValues: str = Field("25,30,31,30", description="调节多个端口','隔开,多个衰减器用'&'隔开,如'1,2,3,4&3,8',表示衰减器\
                                                             1调试1234端口值，衰减器2调试38端口值")

class PullFarAndClose(BaseModel):

    pullFarPort: str = Field("1,2,3,4,5,6,7,8", description="调节多个端口拉远")
    farSourceValue: str = Field("0,0,0,0,30,30,30,30", description="拉远端口的初始值，填写0，默认所有端口调节到0dBm，如多个端口初始值不一致，\
                                       填写0，1，2，0表示目标端口分别调节至0，1，2，0")
    farTargetValue: str = Field("30,30,30,30,0,0,0,0", description="拉远端口的目标值，填写30，默认所有端口调节到30dBm，如多个端口目标值不一致，\
                                       填写27，26，25，30表示目标端口分别调节至27，26，25，30")

    step: str = Field("1", description="单位dBm，间隔时间内拉远dB数，只能输入整数,如果各个端口速率不同,用逗号隔开，示例：1，1，1，1，1，1，1，2")
    interval: int = Field("1000", description="单位ms,间隔时间，只能输入整数，默认值1000")








