#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/09/06 15:00
# <AUTHOR> 10263798
from typing import TypeVar
from pydantic import BaseModel, Field, validator
from typing_extensions import Literal

DataT = TypeVar("DataT")


DEFAULT_ZSA_Template = {
    "目标距离": "50",
    "最大距离": "1500",
    "速度": "16",
    "单位": "m/s",
    "RCS": "1.00"
}

ZSA_MAP = {
    "目标距离": "targetDistance",
    "最大距离": "maxDistance",
    "速度": "speed",
    "单位": "unit",
    "RCS": "rcs"
}

class SetTargetStatus(BaseModel):
    targetType: Literal["静止", "运动"] = Field("运动")
    targetStatus: DataT = Field(DEFAULT_ZSA_Template, type="jsonEditor",
                               description='按照需求填写')

    @validator("targetStatus")
    def validate_targetStatus(cls, targetStatus):
        rlt = {}
        for key, value in targetStatus.items():
            rlt[ZSA_MAP.get(key, key)] = value
        rlt['unit'] = 0 if rlt.get('unit') == 'm/s' else 1
        return rlt
