from fastapi import APIRouter, Request
from api.Response import fail, success
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from api.endpoints.action.zsa.Schemas import SetTargetStatus
from domain.factories.ZsaFactory import ZsaFactory

router = APIRouter(route_class=bizRoute)


@router.post("/set_zsa", summary="设置综测仪参数",
             description=description("设置综测仪各种参数,目前需要综测仪接入专网环境或者部署本地bizcode","岳昊冉10263798"))
async def set_zsa_attenuation(request: Request,targetStatus: SetTargetStatus):
    errList = []
    zsas = ZsaFactory.create(request)
    zsa = zsas[0]
    targetType = 1 if targetStatus.targetType == "运动目标" else 0
    await zsa.set_zsa_radar_target_type(targetType)
    method_mapping = {
        'targetDistance': zsa.set_zsa_radar_distance,
        'maxDistance': zsa.set_zsa_radar_max_distance,
        'speed': zsa.set_zsa_radar_target_speed,
        'unit': zsa.set_zsa_radar_speed_unit,
        'rcs': zsa.set_zsa_radar_rcs
    }
    for key, value in targetStatus.targetStatus.items():
        method = method_mapping.get(key)
        if method:
            rlt = await method(value)
            if not rlt:
                errList.append(f"综测仪的{key}没有设置成功")
    if errList:
        return fail(msg=errList)
    return success(data="综测仪的所有项设置成功")