# -*- encoding: utf-8 -*-
"""
@File    :   ChSim.py
@Time    :   2024/10/18 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import json

from pydantic import BaseModel, Field, validator, root_validator
from typing import Literal
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

class ChsimLoadFile(BaseModel):
    func: Literal["方案加载", "模型系数加载"] = Field(default="方案加载", description='加载类型')
    project: str = Field(None, description="MIMO方案文件路径（模拟仪测试机路径），若设置非全路径，将使用配置信息中'prjDir'的目录作为根目录", regex=r"^.*?.mimo$")
    sceneId: str = Field(None, description="场景id,默认为空，表示自动获取当前模拟仪中已有场景id,支持多个用逗号连接")
    coefficient: str = Field(None, description="模型系数文件路径（模拟仪测试机路径），若设置非全路径，将使用配置信息中'coeDir'的目录作为根目录", regex=r"^.*?.xml")
    updwType: Literal["上行", "下行", "上下行"] = Field(default="上下行", description='链路类型。上行/下行/上下行')
    mode: Literal["正向", "反向", "往返"] = Field(default="往返", description='加载模式, 0-正向，1-反向，2-往返')
    isLargeScale: bool = Field(False, description='是否启用大尺度模式标记，默认不启用')

    @classmethod
    def schema(cls):
        schema_map = [
            ({"func": ["方案加载"]}, {"project": ""}),
            ({"func": ["模型系数加载"]}, {"sceneId": None, "coefficient": "", "updwType": "上下行", "mode": "往返", "isLargeScale": False})
        ]
        dynamic_paras = ["project", "sceneId", "coefficient", "updwType", "mode", "isLargeScale"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

ChsimLoadFile.schema()

class QueryChsimAttr(BaseModel):
    func: Literal["查询加载方案", "查询运行系数", "查询场景ID", "查询基站ID", "查询终端ID", "查询基站通道列表", "查询终端通道列表"] = (
        Field(default="查询加载方案", description='模拟仪配置查询类型'))
    sceneId: str = Field(None, description="场景id,默认为空，表示自动获取当前模拟仪中已有场景id,支持多个用逗号连接")
    updwType: Literal["上行", "下行"] = Field(default="上行", description='链路类型。上行/下行')
    channelType: Literal["接收通道", "发射通道"] = Field(default="接收通道", description='通道类型。0-接收通道; 1-发射通道')
    @classmethod
    def schema(cls):
        schema_map = [
            ({"func": ["查询加载方案"]}, {}),
            ({"func": ["查询运行系数"]}, {"sceneId": None, "updwType": "上行"}),
            ({"func": ["查询场景ID"]}, {}),
            ({"func": ["查询基站ID"]}, {"sceneId": None}),
            ({"func": ["查询终端ID"]}, {"sceneId": None}),
            ({"func": ["查询基站通道列表"]}, {"sceneId": None, "channelType": "接收通道"}),
            ({"func": ["查询终端通道列表"]}, {"sceneId": None, "channelType": "接收通道"}),
        ]
        dynamic_paras = ["sceneId", "updwType", "baseStationId", "channelType"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

QueryChsimAttr.schema()


class QueryChsimStatus(BaseModel):
    func: Literal[ "获取频率状态", "获取模型播放速度", "获取慢衰落仿真进度"] = (
        Field(default="获取模型播放速度", description='模拟仪状态查询类型'))
    sceneId: str = Field(None, description="场景id,默认为空，表示自动获取当前模拟仪中已有场景id")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"func": ["获取频率状态"]}, {}),
            ({"func": ["获取模型播放速度"]}, {"sceneId": None}),
            ({"func": ["获取慢衰落仿真进度"]}, {"sceneId": None})
        ]
        dynamic_paras = ["sceneId"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

QueryChsimStatus.schema()


class SetChsim(BaseModel):
    func: Literal["设置所有通道输入参数", "设置所有通道输出参数", "设置模型播放速度", "设置慢衰落基本参数", "设置慢衰落详细参数",
    "设置噪声基本参数", "设置噪声详细参数", "设置PWM脉冲噪声波形"] = (Field(default="设置模型播放速度", description='模拟仪状态查询类型'))

    sceneId: str = Field(None, description="场景id,默认为空，表示自动获取当前模拟仪中已有场景id,支持多个用逗号连接")

    updwType: Literal["上行", "下行"] = Field(default="上行", description='链路类型。上行/下行')
    power: int = Field(10, description="输入功率，单位 dBm")
    par: int = Field(15, description="输入峰均比，单位 dB")
    phase: str = Field("0", description="通道相位，单位度")
    loss: str = Field("0", description="输入缆损，单位 dB")

    speedKMh: str = Field("0.01", description="移动速度(单位 Km/h)，速度必须大于 0")

    slowFadType: Literal["基站", "终端", "基站与终端"] = Field(default="基站与终端", description='慢衰落设备类型，0,基站;1,终端;2,基站与终端')
    steplimit: int = Field(60, description="步进次数,一次仿真的控制计数，与步进一起决定了仿真的动态范围")
    timeInterval: int = Field(3000, description="间隔时间,单位ms")
    loopcnt: int = Field(2, description="循环次数，-1: 无限循环; > 0: 循环次数")
    loopmode: Literal["重复循环", "往返循环"] = Field(default="往返循环", description='循环模式。0 - 重复循环; 1 - 往返循环')

    stationKind: Literal["基站", "终端", "基站与终端"] = Field(default="基站与终端", description='基站或终端类型。0-基站;1-终端;2-基站与终端')
    startValue: int = Field(-30, description="起始值。单位 dBm(范围-120~0)")
    step: int = Field(1, description="步进。单位 dB(大于 0 表示递增; 小于 0 表示递减")

    noiseMode: Literal["固定信噪比", "固定噪声功率"] = Field(default="固定噪声功率", description='噪声模式。 0 - 固定信噪比;1 - 固定噪声功率')
    noiseBand: str = Field("100", description="高斯白噪声带宽， 单位MHz")
    signalBand: str = Field("100", description="信号带宽， 单位MHz")
    freqOffset: str =Field("0", description="频偏， 单位MHz")

    isNoiseEnable: bool = Field(True, description='噪声使能标记，true - 启用； false - 禁用', ifEmptyUseDefault=False)
    noiseSnr: int = Field(-10, description="信噪比， 单位 dB。(仅仅对信噪比模式有效)(范围: -24~40)", ifEmptyUseDefault=False)
    noiseAtten: int = Field(5, description="噪声衰减， 单位dB。(仅仅对固定噪声功率模式有效)(范围: 0~80)", ifEmptyUseDefault=False)

    @root_validator(pre=True)
    def handle_empty_values_noise_enable(cls, values):
        field = cls.__fields__["isNoiseEnable"]
        if not values.get("isNoiseEnable"):
            if not field.field_info.extra.get("ifEmptyUseDefault", True):
                values["isNoiseEnable"] = False
        return values

    @root_validator(pre=True)
    def handle_empty_values_noise_snr(cls, values):
        field = cls.__fields__["noiseSnr"]
        if not values.get("noiseSnr"):
            if not field.field_info.extra.get("ifEmptyUseDefault", True):
                values["noiseSnr"] = 0
        return values

    @root_validator(pre=True)
    def handle_empty_values_noise_atten(cls, values):
        field = cls.__fields__["noiseAtten"]
        if not values.get("noiseAtten"):
            if not field.field_info.extra.get("ifEmptyUseDefault", True):
                values["noiseAtten"] = 0
        return values

    @classmethod
    def schema(cls):
        schema_map = [
            ({"func": ["设置所有通道输入参数"]}, {"updwType": "上行", "power": 10, "par": 15, "phase": "0", "loss": "0"}),
            ({"func": ["设置所有通道输出参数"]}, {"updwType": "上行", "power": -30, "phase": "0", "loss": "0"}),
            ({"func": ["设置模型播放速度"]}, {"speedKMh": "0.01"}),
            ({"func": ["设置慢衰落基本参数"]}, {"slowFadType": "上行", "steplimit": 60, "timeInterval": 3000, "loopcnt": 2, "loopmode": "往返循环"}),
            ({"func": ["设置慢衰落详细参数"]}, {"stationKind": "基站与终端", "startValue": -30, "step": 1}),
            ({"func": ["设置噪声基本参数"]}, {"noiseMode": "固定噪声功率", "noiseBand": "100", "signalBand": "100", "freqOffset": "0"}),
            ({"func": ["设置噪声详细参数"]}, {"updwType": "上行", "isNoiseEnable": True, "noiseSnr": -10, "noiseAtten": 5}),
            ({"func": ["设置PWM脉冲噪声波形"]}, {})
        ]
        dynamic_paras = ["updwType", "power", "par", "phase", "loss",
                         "speedKMh",
                         "slowFadType", "steplimit", "timeInterval", "loopcnt", "loopmode",
                         "stationKind", "startValue", "step",
                         "noiseMode", "noiseBand", "signalBand", "freqOffset",
                         "isNoiseEnable", "noiseSnr", "noiseAtten"
                         ]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

SetChsim.schema()


class OperateChsim(BaseModel):
    func: Literal[
        "启动加载方案", "关闭方案", "模型播放控制", "启动大尺度系数下载", "慢衰落启停控制", "发送通用命令"] = \
        (Field(default="模型播放控制", description='模拟仪控制操作类型'))
    sceneId: str = Field(None, description="场景id,默认为空，表示自动获取当前模拟仪中已有场景id")
    coeStatus: Literal["开始", "暂停", "继续", "停止"] = Field(default="开始", description='模型播放控制模式。0:开始;1:暂停;2:继续;3:停止')
    fadStatus: Literal["停止", "开始"] = Field(default="开始", description='慢衰落控制模式。0:停止;1:开始')
    mode: Literal["正向", "反向", "往返"] = Field(default="往返", description='加载模式, 0-正向，1-反向，2-往返')
    commandLine: str = Field(None, description='通用命令，具体命令可参考模拟仪使用说明书')
    queryStatusTime: int = Field(18, description='状态查询次数，默认18次')
    expected: str = Field(None, description='命令期望回显值(支持正则),不填表示任意回显值')

    @classmethod
    def schema(cls):
        schema_map = [
            ({"func": ["启动加载方案"]}, {}),
            ({"func": ["关闭方案"]}, {}),
            ({"func": ["模型播放控制"]}, {"sceneId": None, "coeStatus": "开始"}),
            ({"func": ["启动大尺度系数下载"]}, {"sceneId": None, "mode": "往返"}),
            ({"func": ["发送通用命令"]}, {"commandLine": None, "queryStatusTime": 18, "expected":None}),
            ({"func": ["慢衰落启停控制"]}, {"sceneId": None, "fadStatus": "开始"})
        ]
        dynamic_paras = ["sceneId", "mode", "coeStatus", "fadStatus", "commandLine"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)


OperateChsim.schema()