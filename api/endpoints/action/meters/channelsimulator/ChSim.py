# -*- encoding: utf-8 -*-
"""
@File    :   ChSim.py
@Time    :   2024/10/18 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import traceback
import json

from fastapi import APIRouter, Request
from api.Response import fail, success
from api.endpoints.action.meters.channelsimulator.Schemas import ChsimLoadFile, QueryChsimAttr, QueryChsimStatus, \
    SetChsim, OperateChsim
from api.route.BizRoute import bizRoute
from domain.factories.ChSimFactory import ChSimFactory
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/load_ksw_config_file", summary="加载KSW信道模拟仪配置文件",
             description=description("加载KSW信道模拟仪配置文件", "侯小飞10270755"))
async def load_ksw_config_file(request: Request, paras: ChsimLoadFile):
    ret, resultInfo = True, {}
    chsims = ChSimFactory.create(request)
    if not chsims: return fail(msg=f"环境中无可用信道模拟仪，请检查相关环境信息是否正确")
    for chsim in chsims:
        try:
            Logger.info(f"{chsim.id}加载中...")
            retTemp, msg= await chsim.load_config_file(paras.func, paras)
            if not retTemp:
                resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{msg}"})
                ret = False
            else: resultInfo.update({chsim.id: f"{paras.func} 成功"})
        except BaseException as err:
            ret = False
            Logger.error(f"[{chsim.id}]加载KSW信道模拟仪配置文件出现异常,详情:", traceback.format_exc())
            resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{err}"})
    if not ret:
        return fail(data=resultInfo, msg=f"加载KSW信道模拟仪配置文件{paras.func},存在失败")
    return success(data=resultInfo, msg=f"加载KSW信道模拟仪配置文件{paras.func}成功")

@router.post("/query_ksw_attr", summary="查询KSW信道模拟仪属性",
             description=description("查询KSW信道模拟仪属性", "侯小飞10270755"))
async def query_ksw_attr(request: Request, paras: QueryChsimAttr):
    ret, resultInfo = True, {}
    chsims = ChSimFactory.create(request)
    if not chsims: return fail(msg=f"环境中无可用信道模拟仪，请检查相关环境信息是否正确")
    for chsim in chsims:
        try:
            Logger.info(f"{chsim.id}查询中...")
            retTemp, msg = await chsim.query_chsim_attr(paras.func, paras)
            if not retTemp:
                resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{msg}"})
                ret = False
            else:
                resultInfo.update({chsim.id: msg})
        except BaseException as err:
            ret = False
            Logger.error(f"[{chsim.id}]查询KSW信道模拟仪属性出现异常,详情:", traceback.format_exc())
            resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{err}"})
    if not ret:
        return fail(data=resultInfo, msg=f"查询KSW信道模拟仪属性 {paras.func},存在失败")
    return success(data=resultInfo, msg=f"查询KSW信道模拟仪属性 {paras.func}成功")

@router.post("/query_ksw_status", summary="查询KSW信道模拟仪状态",
             description=description("查询KSW信道模拟仪状态", "侯小飞10270755"))
async def query_ksw_status(request: Request, paras: QueryChsimStatus):
    ret, resultInfo = True, {}
    chsims = ChSimFactory.create(request)
    if not chsims: return fail(msg=f"环境中无可用信道模拟仪，请检查相关环境信息是否正确")
    for chsim in chsims:
        try:
            Logger.info(f"{chsim.id}查询中...")
            retTemp, msg = await chsim.query_chsim_status(paras.func, paras)
            if not retTemp:
                resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{msg}"})
                ret = False
            else:
                resultInfo.update({chsim.id: msg})
        except BaseException as err:
            ret = False
            Logger.error(f"[{chsim.id}]查询KSW信道模拟仪状态出现异常,详情:", traceback.format_exc())
            resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{err}"})
    if not ret:
        return fail(data=resultInfo, msg=f"查询KSW信道模拟仪状态 {paras.func},存在失败")
    return success(data=resultInfo, msg=f"查询KSW信道模拟仪状态 {paras.func}成功")

@router.post("/set_ksw_attr", summary="下发KSW信道模拟仪配置",
             description=description("下发KSW信道模拟仪配置", "侯小飞10270755"))
async def set_ksw_attr(request: Request, paras: SetChsim):
    ret, resultInfo = True, {}
    chsims = ChSimFactory.create(request)
    if not chsims: return fail(msg=f"环境中无可用信道模拟仪，请检查相关环境信息是否正确")
    for chsim in chsims:
        try:
            Logger.info(f"{chsim.id}下发配置中...")
            retTemp, msg = await chsim.set_chsim_attr(paras.func, paras)
            if not retTemp:
                resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{msg}"})
                ret = False
            else:
                resultInfo.update({chsim.id: f"{paras.func} 成功"})
        except BaseException as err:
            ret = False
            Logger.error(f"[{chsim.id}]下发KSW信道模拟仪配置出现异常,详情:", traceback.format_exc())
            resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{err}"})
    if not ret:
        return fail(data=resultInfo, msg=f"下发KSW信道模拟仪配置 {paras.func},存在失败")
    return success(data=resultInfo, msg=f"下发KSW信道模拟仪配置 {paras.func}成功")

@router.post("/operate_ksw", summary="控制KSW信道模拟仪",
             description=description("控制KSW信道模拟仪", "侯小飞10270755"))
async def operate_ksw_chsim(request: Request, paras: OperateChsim):
    ret, resultInfo = True, {}
    chsims = ChSimFactory.create(request)
    if not chsims: return fail(msg=f"环境中无可用信道模拟仪，请检查相关环境信息是否正确")
    for chsim in chsims:
        try:
            Logger.info(f"{chsim.id}操作中...")
            retTemp, msg = await chsim.operate_chsim(paras.func, paras)
            if not retTemp:
                Logger.error(f"[{chsim.id}]控制KSW信道模拟仪出现异常,详情:", traceback.format_exc())
                resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{msg}"})
                ret = False
            else:
                if paras.func == "发送通用命令":
                    resultInfo.update({chsim.id: msg})
                else :
                    resultInfo.update({chsim.id: f"{paras.func} 成功"})
        except BaseException as err:
            ret = False
            resultInfo.update({chsim.id: f"{paras.func} 失败,原因:{err}"})
    if not ret:
        return fail(data=resultInfo, msg=f"控制KSW信道模拟仪 {paras.func},存在失败")
    return success(data=resultInfo, msg=f"控制KSW信道模拟仪 {paras.func}成功")


