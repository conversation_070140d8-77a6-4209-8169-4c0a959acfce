from typing import TypeVar

from pydantic import BaseModel, Field

DataT = TypeVar("DataT")


class DataAnalysis(BaseModel):
    space: DataT = Field(default="64ee9dca0c0f8d4dab36f25f", type="func", returnType="optional", params=[],
                         viewType="select", priority=1,
                         func="service.platform.ids_interact.IdsInteractService.IdsInteractService.get_space")
    page: DataT = Field(..., type="func", returnType="optional", params=["space"], viewType="select", priority=2,
                        func="service.platform.ids_interact.IdsInteractService.IdsInteractService.get_page")
    label: DataT = Field(default=None, type="func", returnType="paras", params=["page"], viewType="select", priority=3,
                         func="service.platform.ids_interact.IdsInteractService.IdsInteractService.get_label")
    class Config:
        extra = "allow"  # 允许额外字段