from fastapi import APIRouter, Request

from api.Response import success
from api.endpoints.action.ids.Schemas import DataAnalysis
from api.route.BizRoute import bizRoute
from domain.platform.ActionInfo import description
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/data_analysis", summary="数据分析(调试)",
             description=description("IDS数据分析打桩action", "侯小飞10270755"),
             openapi_extra={"schema_type": "dynamic"})
async def data_analysis(request: Request, para: DataAnalysis):
    Logger.info(f"para: {para.dict()}")
    return success()
