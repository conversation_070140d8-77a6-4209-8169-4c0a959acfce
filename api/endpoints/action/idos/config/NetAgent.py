# -*- encoding: utf-8 -*-
"""
@File    :   NetAgent.py
@Time    :   2024/11/04 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import traceback

from fastapi import APIRouter, Request
from api.Response import fail, success
from api.endpoints.action.idos.config.Schemas import NetAgentVersionTask, NetAgentVersionManage
from api.route.BizRoute import bizRoute
from domain.factories.NetAgentFactory import NetAgentFactory
from domain.platform.ActionInfo import description
from infrastructure.resource.service.Refs import Refs
from service.action.idos.netagent.IDosNetAgentService import IDosNetAgentService
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/create_net_agent_version_task", summary="IDOS创建网元版本任务",
             description=description("IDOS里创建网元版本管理任务", "侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "IDOS创建网元版本任务", "refPattern": "0-1"}]})
async def create_net_agent_version_task(request: Request, paras: NetAgentVersionTask):
    ret, resultInfo = True, {}
    netAgents = await NetAgentFactory.create(request)
    taskInfos = await Refs(request).get_output()
    if not netAgents: return fail(msg=f"环境中无IDOS网元，请检查相关环境信息是否正确")
    for netAgent in netAgents:
        try:
            Logger.info(f"{netAgent.name}创建网元任务中...")
            if taskInfos and paras.func == "恢复":
                paras.verDirectory = taskInfos.get(netAgent.name, {}).get("BackupName")
            retTemp, msg= await IDosNetAgentService.create_version_task(netAgent, paras)
            if not retTemp:
                resultInfo.update({netAgent.name: f"{paras.func} 失败,原因:{msg}"})
                ret = False
            else: resultInfo.update({netAgent.name: retTemp})
        except BaseException as err:
            ret = False
            Logger.error(f"[{netAgent.name}]IDOS创建网元版本任务,详情:", traceback.format_exc())
            resultInfo.update({netAgent.name: f"{paras.func} 失败,原因:{err}"})
    if not ret:
        return fail(data=resultInfo, msg=f"IDOS创建网元版本{paras.func}任务,存在失败")
    return success(data=resultInfo, msg=f"IDOS创建网元版本{paras.func}任务成功")

@router.post("/manage_net_agent_version_task", summary="IDOS网元版本任务管理",
             description=description("管理IDOS里创建网元版本任务", "侯小飞10270755"))
async def manage_net_agent_version_task(request: Request, paras: NetAgentVersionManage):
    ret, resultInfo = True, {}
    netAgents = await NetAgentFactory.create(request)
    if not netAgents: return fail(msg=f"环境中无IDOS网元，请检查相关环境信息是否正确")
    for netAgent in netAgents:
        try:
            Logger.info(f"{netAgent.name}管理网元任务中...")
            retTemp, msg= await IDosNetAgentService.management_version_task(netAgent, paras.func)
            if not retTemp:
                resultInfo.update({netAgent.name: f"{paras.func} 失败,原因:{msg}"})
                ret = False
            else: resultInfo.update({netAgent.name: retTemp})
        except BaseException as err:
            ret = False
            Logger.error(f"[{netAgent.name}]IDOS网元版本任务管理,详情:", traceback.format_exc())
            resultInfo.update({netAgent.name: f"{paras.func} 失败,原因:{err}"})
    if not ret:
        return fail(data=resultInfo, msg=f"IDOS网元版本任务{paras.func},存在失败")
    return success(data=resultInfo, msg=f"IDOS网元版本任务{paras.func}成功")

