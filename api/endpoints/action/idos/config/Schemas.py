# -*- encoding: utf-8 -*-
"""
@Time    :   2024/11/04 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""


import json

from pydantic import BaseModel, Field, validator, root_validator
from typing import Literal

from enum import Enum

from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

class RestoreObject(str, Enum):
    cnf = "网元"
    tcf = "TCF"

class NetAgentVersionTask(BaseModel):
    func: Literal["升级", "回退", "备份", "恢复"] = Field(default="升级", description='任务类型')
    verDirectory: str = Field(None, description="升级/回退根目录，默认不填，表示自动获取sftp第一个目录")
    upgradeObject:  Literal["网元", "TCF"] = Field(default="网元", description='升级/回退操作对象')
    restoreObject:  list[RestoreObject] = Field(default=["网元"], description='恢复操作对象')

    @classmethod
    def schema(cls):
        schema_map = [
            ({"func": ["升级"]}, {"verDirectory": "", "upgradeObject": "网元"}),
            ({"func": ["回退"]}, {"upgradeObject": "网元"}),
            ({"func": ["备份"]}, {}),
            ({"func": ["恢复"]}, {"restoreObject": ["网元"]})
        ]
        dynamic_paras = ["verDirectory", "upgradeObject", "restoreObject"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

NetAgentVersionTask.schema()

class NetAgentVersionManage(BaseModel):
    func: Literal["重试", "等待完成", "查询详情", "查询备份列表"] = Field(default="等待完成", description='任务类型')

