# -*- encoding: utf-8 -*-
"""
@File    :   CoreNet.py
@Time    :   2023/08/24 10:47:44
<AUTHOR>   侯小飞10270755,李双红10258068
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import traceback
import json

from fastapi import APIRouter, Request
from api.Response import fail, success
from api.endpoints.action.corenet.Schemas import PcfModifyPara, SmfCreatePara, SmfModifyPara
from api.endpoints.action.corenet.Schemas import XgwCreatePara, XgwModifyPara, XgwReleasePara
from api.endpoints.action.corenet.Schemas import MmlCmd, CornetCmd
from api.route.BizRoute import bizRoute
from domain.factories.UeFactory import UeFactory
from domain.platform.ActionInfo import description
from service.action.corenet.PcfService import PcfService
from service.action.corenet.SmfService import SmfService
from service.action.corenet.XgwService import XgwService
from service.action.corenet.MmlService import MmlService
from domain.models.cornet.Pcf import Pcf
from domain.models.cornet.Smf import Smf
from domain.models.cornet.Xgw import Xgw
from domain.models.cornet.Amf import Amf
from domain.factories.MmlFactory import MmlFactory
from infrastructure.resource.service.DeviceManager import DeviceManager
from infrastructure.resource.service.Refs import Refs
from infrastructure.logger import Logger

router = APIRouter(route_class=bizRoute)


@router.post("/pcfRoleModify", summary="PCF修改PDUSESSION",
             description=description("修改终端PCF规则，触发PDUSESSION修改(使用action前，请自行确保环境信息中PCF信息和自己环境匹配)", "侯小飞10270755;李双红10258068"))
async def pcf_modify(request: Request, paras: PcfModifyPara):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices("PCF"):
        return fail(msg="环境中无PCF资源信息,请参考链接配置:https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/796c413979c34665bb346883b31b8c1d/view")
    pcf = Pcf(deviceManager.find_devices("PCF")[0].get("attr"))
    try:
        imsi = await XgwService.update_imsi(paras.imsi, request)
        if await PcfService.modify_pcf_role(pcf, imsi, paras.dnn, paras.sessqos, paras.fiveqilist):
            return success(msg="PCF修改成功")
        return fail(msg="PCF修改失败，\r\n"
                        f"1.请确认UE接入的是否是环境信息中PCF({pcf.ip})对应的核心网,\r\n"
                        f"2.请确认输入的imsi:[{imsi}]+dnn:[{paras.dnn}]和实际接入的是否一致,\r\n"
                        f"3.其他异常，请联系当地核心网同事定位")
    except BaseException as err:
        Logger.error(f"PCF修改在执行过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'{err}')


@router.post("/smfCreateBearer", summary="SMF创建专用承载",
             description=description("核心网SMF创建专用承载(使用action前，请自行确保环境信息中SMF信息和自己环境匹配)", "侯小飞10270755;李双红10258068"))
async def smf_create_bearer(request: Request, paras: SmfCreatePara):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices("SMF"):
        return fail(
            msg="环境中无SMF资源信息,请参考链接配置:https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/734468dbc00b44379fd042888fdc8d4e/view")
    smf = Smf(deviceManager.find_devices("SMF")[0].get("attr"))
    try:
        imsi = await XgwService.update_imsi(paras.imsi, request)
        pdnip = XgwService.update_pdnip(paras.pdnip, request)
        gbrConfig = json.loads(paras.gbrConfig) if paras.gbrConfig else dict()
        preConfig = json.loads(paras.preConfig) if paras.preConfig else dict()
        createPara, result = await SmfService.create_bearer(
            smf, imsi, paras.fiveqi, pdnip, paras.dnn, gbrConfig, preConfig)
        if result:
            return success(data=createPara, msg="SMF创建专用承载成功")
        await SmfService.delete_bearer(smf, createPara)
        await SmfService.clear_config(smf, createPara)
        return fail(msg="SMF创建专用承载失败：\r\n"
                        f"1.请确认UE接入的是否是环境信息中SMF:[{smf.ip}]对应的核心网，\r\n"
                        f"2.请确认输入的imsi:[{imsi}]+dnn[{paras.dnn}]和实际接入的是否一致，\r\n"
                        f"3.确认GBR承载gbrConfig属性需要填，NGBR承载gbrConfig不能填，\r\n"
                        f"4.其他异常，请联系当地核心网同事定位")
    except BaseException as err:
        Logger.error(f"SMF创建专用承载在执行过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'{err}')


@router.post("/smfModifyBearer", summary="SMF修改专用承载",
             description=description("核心网SMF修改专用承载", "侯小飞10270755;李双红10258068"),
             openapi_extra={"refs": [{"actionName": "SMF创建专用承载"}]})
async def smf_modify_bearer(request: Request, paras: SmfModifyPara):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices("SMF"):
        return fail(
            msg="环境中无SMF资源信息,请参考链接配置:https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/734468dbc00b44379fd042888fdc8d4e/view")
    smf = Smf(deviceManager.find_devices("SMF")[0].get("attr"))
    createPara = (await Refs(request).get_output())[0]
    try:
        pdnip = XgwService.update_pdnip(paras.pdnip, request)
        gbrConfig = json.loads(paras.gbrConfig) if paras.gbrConfig else dict()
        preConfig = json.loads(paras.preConfig) if paras.preConfig else dict()
        createPara, result = await SmfService.update_bearer(smf, createPara, pdnip, gbrConfig, preConfig)
        if result:
            return success(data=createPara, msg="SMF修改专用承载成功")
        return fail(msg=f"SMF修改专用承载失败：\r\n"
                        f"1.修改承载时，注意mbr和gbr参数只支持在之前的基础上增加新设置的数值，\r\n"
                        f"2.确认GBR承载gbrConfig属性需要填，NGBR承载gbrConfig不能填，\r\n"
                        f"3.其他异常，请联系当地核心网同事定位")
    except BaseException as err:
        Logger.error(f"SMF修改专用承载在执行过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'{err}')


@router.post("/smfDeleteBearer", summary="SMF删除专用承载",
             description=description("核心网SMF删除专用承载", "侯小飞10270755;李双红10258068"),
             openapi_extra={"refs": [{"actionName": "SMF创建专用承载"}]})
async def smf_delete_bearer(request: Request):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices("SMF"):
        return fail(
            msg="环境中无SMF资源信息,请参考链接配置:https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/734468dbc00b44379fd042888fdc8d4e/view")
    smf = Smf(deviceManager.find_devices("SMF")[0].get("attr"))
    createPara = (await Refs(request).get_output())[0]
    try:
        result = await SmfService.delete_bearer(smf, createPara)
        await SmfService.clear_config(smf, createPara)
        if result:
            return success(msg="SMF删除专用承载成功")
        return fail(msg="SMF删除专用承载失败")
    except BaseException as err:
        Logger.error(f"SMF删除专用承载在执行过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'{err}')


@router.post("/xgw_create_bearer", summary="XGW创建专用承载",
             description=description("4G核心网XGW创建专用承载(使用action前，请自行确保环境信息中XGW信息和自己环境匹配)", "侯小飞10270755"))
async def xgw_create_bearer(request: Request, paras: XgwCreatePara):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices("XGW"):
        return fail(
            msg="环境中无XGW资源信息,请参考链接配置:https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/52e8606f62f544d0a3670b75d6bfb7df/view")
    xgw = Xgw(deviceManager.find_devices("XGW")[0].get("attr")).xgwDevice
    try:
        await xgw.init_login()
        imsi = await XgwService.update_imsi(paras.imsi, request)
        pdnip = XgwService.update_pdnip(paras.pdnip, request)
        erabDictString = XgwService.merge_xgw_paras(paras.qci, arp=paras.arpConfig,
                                                    gbr=paras.gbrConfig, ambr=paras.ambrConfig)
        createPara, result = await XgwService.create_bearer(xgw, imsi, pdnip, erabDictString)
        if result.return_info and all(res == "success" for res in result.return_info.values()):
            return success(data=createPara, msg="XGW创建专用承载成功")
        return fail(msg=f"XGW创建专用承载失败:{XgwService.analysis_result(result)}")
    except BaseException as err:
        Logger.error(f"XGW创建专用承载在执行过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'{err}')

@router.post("/xgw_modify_bearer", summary="XGW修改专用承载",
             description=description("4G核心网XGW修改专用承载","侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "XGW创建专用承载"}]})
async def xgw_modify_bearer(request: Request, paras: XgwModifyPara):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices("XGW"):
        return fail(
            msg="环境中无XGW资源信息,请参考链接配置:https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/52e8606f62f544d0a3670b75d6bfb7df/view")
    xgw = Xgw(deviceManager.find_devices("XGW")[0].get("attr")).xgwDevice
    createPara = (await Refs(request).get_output())[0]
    try:
        if not createPara:
            return fail(msg=f"XGW创建专用承载失败:请确认索引的'XGW创建专用承载'是否执行成功")
        await xgw.init_login()
        modifyPara, result = await XgwService.modify_bearer(xgw, createPara, paras.pdnip, paras.config)
        if result.return_info and all(res == "success" for res in result.return_info.values()):
            return success(data=modifyPara, msg="XGW修改专用承载成功，以下是'XGW创建专用承载'创建修改后承载的信息")
        return fail(msg=f"XGW创建专用承载失败:{XgwService.analysis_result(result)}")
    except BaseException as err:
        Logger.error(f"XGW修改专用承载在执行过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'{err}')


@router.post("/xgw_release_bearer", summary="XGW删除专用承载",
             description=description("4G核心网XGW删除专用承载","侯小飞10270755"),
             openapi_extra={"refs": [{"actionName": "XGW创建专用承载"}]})
async def xgw_release_bearer(request: Request, paras: XgwReleasePara):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices("XGW"):
        return fail(
            msg="环境中无XGW资源信息,请参考链接配置:https://i.zte.com.cn/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/52e8606f62f544d0a3670b75d6bfb7df/view")
    xgw = Xgw(deviceManager.find_devices("XGW")[0].get("attr")).xgwDevice
    createPara = (await Refs(request).get_output())[0]
    try:
        await xgw.init_login()
        afterReleasePara, result = await XgwService.release_bearer(xgw, createPara, paras.qci)
        if result.return_info and all(res == "success" for res in result.return_info.values()):
            return success(data=afterReleasePara, msg="XGW删除专用承载成功,以下是'XGW创建专用承载'创建剩余的承载")
        return fail(msg=f"XGW删除专用承载失败:{XgwService.analysis_result(result)}")
    except BaseException as err:
        Logger.error(f"XGW删除专用承载在执行过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'{err}')


@router.post("/mml_send_cmd", summary="MML核心网受理台发送指令",
             description=description("在MML核心网受理台发送指令","侯小飞10270755"))
async def mml_send_cmd(request: Request, paras: MmlCmd):
    mmls = MmlFactory.create(request)
    ues = UeFactory.create(request)
    successFlag, resultInfo = True, {}
    parasTemp = dict(paras)
    parasTemp.update({"ues": ues})
    if not mmls:
        return fail(msg="环境中无mml资源信息,请参考链接配置:"
                        "https://i.zte.com.cn/index/ispace/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/d208ef2472d6485e99b42a70b14a8081/view")
    try:
        for mml in mmls:
            ret, result = await MmlService.send_cmd(mml, paras.func, parasTemp)
            if not ret:
                successFlag = False
            resultInfo.update({repr(mml.id): result})
        if successFlag:
            return success(data=resultInfo, msg="MML核心网受理台发送指令成功")
        return fail(data=resultInfo, msg=f"MML核心网受理台发送指令失败")
    except BaseException as err:
        Logger.error(f"MML核心网受理台发送指令过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'MML核心网受理台发送指令过程中出现异常,详情{err}')

@router.post("/cornet_send_cmd", summary="核心网执行通用命令",
             description=description("核心网（SMF、PCF、AMF、XGW）执行通用命令","侯小飞10270755"))
async def cornet_send_cmd(request: Request, paras: CornetCmd):
    deviceManager = DeviceManager(request.state.resource.get("config"))
    if not deviceManager.find_devices(paras.cornetType):
        return fail(
            msg=f"环境中无{paras.cornetType}资源信息,请参考链接配置:https://i.zte.com.cn/index/ispace/#/space/828a41865fbf4c5b817fbc75663847fb/wiki/page/3b22822aad394e5881d082ad7e05df8a/view")

    cornetModel = _get_cornet_model(deviceManager, paras)
    successFlag, resultInfo = True, {}
    try:
        if '#IMSI#' in str(paras.customCmds):
            ues = UeFactory.create(request)
            for ue in ues:
                imsi = await MmlService.get_imsi(ue)
                if not imsi:
                    err, successFlag = f'UE: {repr(ue.id)} 执行在线查询imsi失败，环境信息中也没有imsi信息', False
                    resultInfo.update({repr(ue.id): err})
                    continue
                customCmds = _update_cmds_imsi(paras.customCmds, imsi)
                ret, result = await cornetModel.execute_cmd(customCmds)
                if not ret: successFlag = False
                resultInfo.update({repr(ue.id): result})
        else:
            ret, resultInfo = await cornetModel.execute_cmd(paras.customCmds)
            if not ret: successFlag = False
        if not successFlag:
            return fail(data=resultInfo, msg=f"核心网执行通用命令失败")
        return success(data=resultInfo, msg="核心网执行通用命令成功")
    except BaseException as err:
        Logger.error(f"核心网执行通用命令过程中出现异常,详情:", traceback.format_exc())
        return fail(msg=f'核心网执行通用命令过程中出现异常,详情{err}')

def _get_cornet_model(deviceManager, paras):
    match paras.cornetType:
        case "SMF":
            cornetModel = Smf(deviceManager.find_devices(paras.cornetType)[0].get("attr"))
        case "PCF":
            cornetModel = Pcf(deviceManager.find_devices(paras.cornetType)[0].get("attr"))
        case "AMF":
            cornetModel = Amf(deviceManager.find_devices(paras.cornetType)[0].get("attr"))
        case "XGW":
            cornetModel = Xgw(deviceManager.find_devices(paras.cornetType)[0].get("attr"))
    return cornetModel

def _update_cmds_imsi(customCmds, imsi):
    customCmdsTemp = []
    for customCmd in customCmds:
        if isinstance(customCmd, str):
            customCmdsTemp.append(customCmd.replace("#IMSI#", str(imsi)))
        else:
            customCmd[0] = customCmd[0].replace("#IMSI#", str(imsi))
            customCmdsTemp.append(customCmd)
    return customCmdsTemp


