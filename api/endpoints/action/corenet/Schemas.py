# -*- encoding: utf-8 -*-
"""
@File    :   Schemas.py
@Time    :   2023/08/24 10:47:44
<AUTHOR>   侯小飞10270755,李双红10258068
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

import json

from pydantic import BaseModel, Field, validator, root_validator
from typing import Literal, TypeVar
from domain.platform.schema_interconnection.ConstructSchema import SchemaGenerator as MySchema

DataT = TypeVar('DataT')


TEMP_CUSTOM_CMDS = ["Set NGSBSC:IMSI=460088100800185,AMBRDW=**********;",
                    "Set NGSBSC:IMSI=460088100800185,AMBRDW=**********;"]

CORNET_TEMP_CUSTOM_CMDS = [['SHOW AMFSNSSAI', 'zte:>', 10], 'SHOW SCTP']


class PcfModifyPara(BaseModel):
    imsi: str = Field(None, description='终端IMSI,默认不填,表示从环境信息中获取IMSI', regex=r"^$|\s*\d{15}\s*$")
    dnn: str = Field("cmnet", description='DNN,默认cmnet，根据实际放号信息填写')
    sessqos: Literal["5", "6", "7", "8", "9", "80"] = Field("9", description='默认承载的ngbr qos（一定是ngbr）,例:9')
    fiveqilist: str = Field(
        None,
        description='其他qoslist(根据需要填写)，用"&"分开，例:8&4;\r\n如果用户使用其他规则，可直接输入规则全集，例:n7_total_pre&n7_total_sess_5qi8&n7_total_pcc_ngbr_5qi8&n7_total_pcc_gbr_5qi4')

class SmfCreatePara(BaseModel):
    imsi: str = Field(None, description='终端IMSI,默认不填,表示从环境信息中获取IMSI', regex=r"^$|\s*\d{15}\s*$")
    fiveqi: str = Field(..., description='承载5QI,举例:6', regex=r"^\s*\d{1,3}\s*$")
    pdnip: str | None= Field("*************", description='pdn cnip地址,不填时,表示从环境信息中获取第一个PDN信息', ifEmptyUseDefault=False)
    dnn: str = Field("cmnet", description='DNN,默认cmnet，根据实际放号信息填写')
    preConfig: str = Field('PCI:0,PL:6,PVI:0', description="PCI(0:具有抢占能力，1不具有抢占能力),PL(arp等级，取值1-15，值越大优先级越低),PVI(0:可被抢占，1:不可被抢占)")
    gbrConfig: str = Field(
        None,
        description='NGBR不用填,MBRUL、MBRDL、GBRUL、GBRDL任意有个有数据则表示为GBR类型,例: MBRUL:200000,MBRDL:1500000,GBRUL:200000,GBRDL:1500000')

    @validator("preConfig", "gbrConfig", pre=True)
    def attrs_to_jsonstr(cls, v):
        if not v:
            return "{}"
        d = dict()
        for item in v.split(','):
            k, v = item.split(':')
            d[k.strip().upper()] = v.strip()
        return json.dumps(d)

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        pdnip_field = cls.__fields__["pdnip"]
        if not values.get("pdnip"):
            if not pdnip_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["pdnip"] = ""
        return values


class SmfModifyPara(BaseModel):
    pdnip: str = Field(None, description='新的pdn地址，默认为空表示不变更;如果资源里pdn信息与创建时的不一致，也会更新')
    preConfig: str = Field('PCI:0,PL:6,PVI:0', description="PCI(0:具有抢占能力，1不具有抢占能力),PL(arp等级，取值1-15，值越大优先级越低),PVI(0:可被抢占，1:不可被抢占)")
    gbrConfig: str = Field(
        None,
        description='NGBR不用填,MBRUL、MBRDL、GBRUL、GBRDL任意有个有数据则表示为GBR类型,按照MBRUL:200000,MBRDL:1500000,GBRUL:200000,GBRDL:1500000(kpbs)默认')

    @validator("preConfig", "gbrConfig", pre=True)
    def attrs_to_jsonstr(cls, v):
        if not v:
            return "{}"
        d = dict()
        for item in v.split(','):
            k, v = item.split(':')
            d[k.strip().upper()] = v.strip()
        return json.dumps(d)


class XgwCreatePara(BaseModel):
    imsi: str = Field(None, description='终端IMSI,默认不填,表示从环境信息中获取IMSI', regex=r"^$|\s*\d{15}\s*$")
    qci: str = Field(..., description='承载qci,多个承载用分号";"连接，例:3;6', regex=r"\d{1,3}\s*;?")
    pdnip: str | None = Field("*************", description='pdn cnip地址,不填时,表示从环境信息中获取第一个PDN信息', ifEmptyUseDefault=False)
    arpConfig: str = Field('PCI=0,PL=6,PVI=0',
                           description="PCI(0:具有抢占能力，1不具有抢占能力),"
                                       "PL(arp等级，取值1-15，值越大优先级越低),"
                                       "PVI(0:可被抢占，1:不可被抢占),"
                                       "PRIORITY(包过滤优先级)，每个承载单独使用一个优先级，UE里不能重复，不输入时将随机生成"
                                       "多qci场景时，当每个qci的arp信息一样时，可以只填一组信息，如果arp信息不一样，则用;符号连接，例："
                                       "PCI=0,PL=6,PVI=0;PCI=1,PL=12,PVI=1,PRIORITY=5", regex=r"(\w+\s*=\s*\d+)[,|;]?")
    gbrConfig: str = Field(
        "ULMBR=1000,DLMBR=1000,ULGBRUL=1000,DLGBR=1000",
        description='承载速率,该参数只对GBR生效,以下属性支持部分填写，对于未填写参数将使用默认值,注意mbr>=gbr；对于多qci速率属性不同场景，用分号";"连接'
                    'GBR例: ULMBR=1000,DLMBR=1000,ULGBRUL=1000,DLGBR=1000', regex=r"(\w+\s*=\s*\d+)[,|;]?")
    ambrConfig: str = Field('ULAMBR=50000,DLAMBR=100000',
                            description='apn上下行总传输比特率,支持部分填写，对于未填写参数将使用默认值；对于多qci速率属性不同场景，用&符号连接'
                                        '例：ULAMBR=50000,DLAMBR=100000;DLAMBR=20000', regex=r"(\w+\s*=\s*\d+)[,|;]?")

    @root_validator(pre=True)
    def handle_empty_values(cls, values):
        pdnip_field = cls.__fields__["pdnip"]
        if not values.get("pdnip"):
            if not pdnip_field.field_info.extra.get("ifEmptyUseDefault", True):
                values["pdnip"] = ""
        return values


class XgwModifyPara(BaseModel):
    pdnip: str = Field(None, description='新的pdn地址，默认为空表示不变更;如果资源里pdn信息与创建时的不一致，也会更新')
    config: str = Field(...,description="需要修改的属性，参考创建承载所填参数，如果要指定修改某个qci属性，增加QCI=qci值即可，"
                                        "注意：多承载时，PRIORITY不输将随机生成，如果是输入的话，不能重复，例："
                                        "QCI=3,ULMBR=2048,DLMBR=2048,PRIORITY=7;QCI=6,ULGBRUL=2048,PRIORITY=10",
                        regex=r"(\w+\s*=\s*\d+)[,|;]?")


class XgwReleasePara(BaseModel):
    qci: str = Field(None, description='需要删除的承载承载qci,默认不填，表示删除所有"XGW创建专用承载"创建出的承载，例:3;6',
                     regex=r"\d{1,3}\s*;?")

class MmlCmd(BaseModel):
    func: Literal["自定义", "查询终端受理台信息", "修改终端限速"] = Field(default="修改终端限速", description='任务类型')
    imsi: str = Field(None, description='终端IMSI,默认不填,表示从环境信息中获取IMSI', regex=r"^$|\s*\d{15}\s*$")
    customCmds: DataT = Field(default=TEMP_CUSTOM_CMDS, type="jsonEditor",
                              description="自定义命令集, 支持多个命令输入(可以自行输入imsi，也可用'#IMSI#'替代,"
                                          "底层根据资源过滤的UE[不支持MCP和MTUE]信息自动补全IMSI)")
    resultToDict: bool = Field(False, description='输出结果是否转换为字,默认不转换,只输出字符串')
    radioMode: Literal["SA", "NSA"] = Field(default="SA", description='网络制式')
    ambrDw: str = Field(None, description="下行最大带宽(单位:kbps),默认为空表示不修改")
    ambrUp: str = Field(None, description="上行最大带宽(单位:kbps),默认为空表示不修改")

    @classmethod
    def schema(cls):
        schema_map = [
            ({"func": ["自定义"]}, {"customCmds": TEMP_CUSTOM_CMDS, "resultToDict": False}),
            ({"func": ["查询终端受理台信息"]}, {"imsi": None}),
            ({"func": ["修改终端限速"]}, {"imsi": None, "radioMode":"SA", "ambrDw": None, "ambrUp": None})
        ]
        dynamic_paras = ["customCmds", "resultToDict", "imsi", "radioMode", "ambrDw", "ambrUp"]
        return MySchema().generate_schema(super().schema(), None, "generate_by_if_then_not", schema_map, dynamic_paras)

MmlCmd.schema()


class CornetCmd(BaseModel):
    cornetType: Literal["SMF", "AMF", "PCF", "XGW"] = Field(default="AMF", description='核心网设备类型')
    customCmds: DataT = Field(default=CORNET_TEMP_CUSTOM_CMDS, type="jsonEditor",
                              description="自定义命令集：1.每个元素是三元素列表，第一个元素是 命令，第二个元素是期望值(支持正则，不入时取默认值)，第三个元素是超时时间(默认5s)"
                                          "2.举例：[['SHOW AMFSNSSAI', 'zte:>', 10], 'SHOW SCTP']"
                                          "3.支持多个命令输入(可以自行输入imsi，也可用'#IMSI#'替代,底层根据资源过滤的UE[不支持MCP和MTUE]信息自动补全IMSI)")
