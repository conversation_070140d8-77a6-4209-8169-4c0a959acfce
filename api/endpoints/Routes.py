#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:25
# <AUTHOR> 10263601
import os

from fastapi import APIRouter

from api.endpoints.platform.Api import router as api
from api.endpoints.platform.Slave import router as slave
from infrastructure.utils import RouterUtil

router = APIRouter(prefix="/api")
RouterUtil.traverse_all_sub_routers(router, os.path.dirname(os.path.abspath(__file__)))
router.include_router(api, tags=["default"])
router.include_router(slave, tags=["default"])
