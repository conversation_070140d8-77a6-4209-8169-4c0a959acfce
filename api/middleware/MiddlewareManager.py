#!/usr/bin/env python 
# -*- coding: utf-8 -*-
# @Time    : 2023/6/9 14:10
# <AUTHOR> 10263601
from starlette.middleware.cors import CORSMiddleware

from config.AppSettings import settings


class MiddlewareManager:

    def __init__(self, app):
        self.app = app

    def register_middleware(self):
        self._register_cors_middleware()

    def _register_cors_middleware(self):
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.allowed_hosts,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
