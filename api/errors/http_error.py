#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:25
# <AUTHOR> 10263601

from fastapi import HTTPException
from starlette.requests import Request
from starlette.responses import JSONResponse

from api.Const import FAIL_CODE


async def http_error_handler(_: Request, exc: HTTPException) -> JSONResponse:
    return JSONResponse({"code": FAIL_CODE, "message": exc.detail, "data": {}},
                        status_code=exc.status_code)
