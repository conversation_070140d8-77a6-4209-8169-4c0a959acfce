#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/5/16 15:25
# <AUTHOR> 10263601


from typing import Union

from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from starlette.requests import Request
from starlette.responses import JSONResponse
from starlette.status import HTTP_400_BAD_REQUEST

from api.Const import FAIL_CODE


async def http_params_error_handler(_: Request, exc: Union[RequestValidationError, ValidationError], ) -> JSONResponse:
    return JSONResponse(
        {
            "code": FAIL_CODE,
            "message": f"数据校验错误: {exc.errors()}",
            "data": exc.errors(),
        },
        status_code=HTTP_400_BAD_REQUEST,
    )
