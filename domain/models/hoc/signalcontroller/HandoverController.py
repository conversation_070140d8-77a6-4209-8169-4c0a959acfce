# codin=utf-8
'''
Created on 20171202
@author: 10193332
'''
import time

from infrastructure.logger import Logger
from infrastructure.device.hoc.radiorack.RadioRack import RadioRack
from infrastructure.device.hoc.yvam.Yvam import Yvam
from domain.models.hoc.Hoc import Hoc


class HandoverController(Hoc):

    def __init__(self, hocInfo):
        super(HandoverController, self).__init__(hocInfo)
        self._hocInfo = hocInfo

    async def signal_attenuation_by_port(self, valueList, hocPortList):
        status = await self._set_hoc_value(hocPortList, valueList)
        return status

    async def pullfar_and_close_by_port(self, hocFarPortList, farValueList, farValueSourceList, step, interval):
        stepList, addList, lessList = await self._get_step_list(farValueList, farValueSourceList, step)
        maxvalue = max([abs(x-y) for x, y in zip(farValueList, farValueSourceList)])
        newFarValueList = farValueSourceList
        for i in range(int(maxvalue)):
            newFarValueList = [x + y for x, y in zip(stepList, newFarValueList)]
            Logger.debug(f"目前衰减器值为{newFarValueList}")
            for k in addList:
                if newFarValueList[k] > farValueList[k]:
                    newFarValueList[k] = farValueList[k]
            for j in lessList:
                if newFarValueList[j] < farValueList[j]:
                    newFarValueList[j] = farValueList[j]
            time.sleep(int(interval)/1000)
            status = await self._set_hoc_value(hocFarPortList, newFarValueList)
            if farValueList == newFarValueList:
                return status
        return status

    async def _get_step_list(self,farValueList,farValueSourceList,step):
        addList = []
        lessList = []
        if len(step) == 1:
            step = step * len(farValueList)
        for i in range(len(farValueList)):
            if farValueList[i] < farValueSourceList[i]:
                step[i] = -int(step[i])
                lessList.append(i)
            else:
                step[i] = int(step[i])
                addList.append(i)
        return step, addList, lessList

    def _set_single_port_list(self, hocPort):
        singlePortList = []
        singlePortList.append(hocPort)
        return singlePortList

    def _set_single_value_list(self,hocValue):
        singleValuetList = []
        singleValuetList.append(hocValue)
        return singleValuetList

    async def _set_hoc_value(self, hocPortList, valueList):
        if self._get_hoc_type(self.type) == "RadioRack":
            await self._set_radiorack_attenuator_value_cascaded(hocPortList, valueList)
        else:
            await self._set_attenuator_value_cascaded(hocPortList, valueList)

    def _get_hoc_type(self, hocVersion):
        if hocVersion == "blueAttenuator":
            return "Yvam"
        return "RadioRack"

    async def _set_attenuator_value_cascaded(self, portList, valueList):
        if (len(portList) == 0 or len(valueList) == 0):
            Logger.error('no channel found or no value found，Please check the input values！！')
            raise
        for i in range(len(valueList)):
            if float(valueList[i]) > 61.5 or float(valueList[i]) < 0:
                Logger.error('set cascaded attenuator value failed: %s is larger than 31.5 or less than 0' % (str(valueList[i])))
        await Yvam(self._hocInfo).set_multi_channel_value(portList, valueList)

    async def _set_radiorack_attenuator_value_cascaded(self, portList, valueList):
        if (len(portList) == 0 or len(valueList) == 0):
            Logger.error('no channel found or no value found，Please check the input values！！')
            raise
        for i in range(len(valueList)):
            if float(valueList[i]) > 70 or float(valueList[i]) < 0:
                Logger.error('set cascaded attenuator value failed: %s is larger than 70 or less than 0' % (str(valueList[i])))
        await RadioRack(self._hocInfo).set_multi_channel_value(portList, valueList)

if __name__ == '__main__':
    ip = "************"
    hocVersion = "Yvam"
    hoc = {"id": "1", "ip": ip, "tcpPort": "5025", "version": "blueAttenuator"}
    value = 20
    hocPortList = [1,2,3,4]
    HandoverController(hoc).signal_attenuation_by_port(value,hocPortList)



