# coding=utf-8
'''
Created on 2019年7月23日

@author: 10207409
'''
import re
import os
import csv
import time
import logging
try:
    import paramiko
except Exception, e:
    logging.warn(e)
from time import sleep
from testlib5g.domain.model.ue.cpe500.Cpe500 import Cpe500
from testlib5g.domain.DomainRepository import DomainRepository
from testlib5g.infrastructure.utility.envpara.EnvPara import EnvPara
from testlib5g.app_service.basic.contract.ContractService import ContractService


def cpe500_reboot(cpe500Alias, isNeedLmt=True):
    if isNeedLmt:
        return DomainRepository().find(cpe500Alias).reboot_ue()
    return ContractService.execute_contract("Cpe500Basic.cpe500_reboot", {"ueAlias": cpe500Alias}).result


def cpe500_version_upgrade(cpe500Alias, versionAlias, isNeedLmt=True):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    if not isNeedLmt:
        ContractService.execute_contract("Cpe500Basic.cpe500_upgrade", {
                                         "ueAlias": cpe500Alias, 'versionAlias': artifactsPath})
    else:
        ContractService.execute_contract('Cpe500Basic.cpe500_upgrade_by_lmt', {
                                         'ueAlias': cpe500Alias, 'versionAlias': artifactsPath}).result
    return cpe500_reboot(cpe500Alias, False)


def cpe500_ci2_upgrade(cpe500Alias, versionAlias):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "hardIp", "*************")
    ContractService.execute_contract("Cpe500Basic.cpe500_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    return cpe500_reboot(cpe500Alias, True)


def mtue_ci2_upgrade(cpe500Alias, versionAlias):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "hardIp", "************")
    ContractService.execute_contract("Cpe500Basic.mtue_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    return cpe500_reboot(cpe500Alias, True)

def tue600_ci2_upgrade(cpe500Alias, versionAlias):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "mcs0Ip", "************")
    ContractService.execute_contract("Cpe500Basic.tue600_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    return cpe500_reboot(cpe500Alias, True)

def cpe500_fpga_ci2_upgrade(cpe500Alias, versionAlias):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "hardIp", "*************")
    ContractService.execute_contract("Cpe500Basic.cpe500_fpga_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    return cpe500_reboot(cpe500Alias, True)

def sub1g_ci2_upgrade(cpe500Alias, versionAlias, isGT20M=False, isURLLC=False):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(cpe500Alias, "mcs0Ip", "************")
    if isGT20M:
        ContractService.execute_contract("Cpe500Basic.sub1g_GT20M_ci2_upgrade", {
                                      'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    else:
        ContractService.execute_contract("Cpe500Basic.sub1g_20M_ci2_upgrade", {
            'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    ftp2IpAddr = EnvPara().get_attribute(cpe500Alias, "mcs1Ip", "************")
    if isURLLC:
        ContractService.execute_contract("Cpe500Basic.tue600_fpga2_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftp2IpAddr})
    else:
        ContractService.execute_contract("Cpe500Basic.tue600_fpga2SD_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftp2IpAddr})
    ContractService.execute_contract("Cpe500Basic.tue600_UpdateFpga2", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftp2IpAddr})
    return cpe500_reboot(cpe500Alias, True)

def tue600_ci3_upgrade(cpe500Alias, versionAlias):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "mcs0Ip", "************")
    ContractService.execute_contract("Cpe500Basic.tue600_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    ContractService.execute_contract("Cpe500Basic.tue600_fpga0_ci2_upgrade", {
        'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    ftp2IpAddr = EnvPara().get_attribute(cpe500Alias, "mcs1Ip", "************")
    ContractService.execute_contract("Cpe500Basic.tue600_fpga2_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftp2IpAddr})
    ContractService.execute_contract("Cpe500Basic.tue600_UpdateFpga2", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftp2IpAddr})
    return cpe500_reboot(cpe500Alias, True)

def mtue_fpga_ci2_upgrade(cpe500Alias, versionAlias, mtueType):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    if mtueType == "CPE500":
        fpgaVersionName = "fpga_cur.swv"
    elif mtueType == "TUE700M01":
        fpgaVersionName = "tue700TF_fpga_cur.swv"
    elif mtueType == "TUE700M02":
        fpgaVersionName = "tue700TT_fpga_cur.swv"
    elif mtueType == "TUE700M03":
        fpgaVersionName = "tue700FF_fpga_cur.swv"
    else:
        logging.info("mtueType error")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "hardIp", "*************")
    ContractService.execute_contract("Cpe500Basic.mtue_fpga_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, 'fpgaVersionName': fpgaVersionName, "ftpIpAddr": ftpIpAddr})
    return cpe500_reboot(cpe500Alias, True)

def mtue_ci3_upgrade(cpe500Alias, versionAlias, mtueType):
    iscpe500 = False
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    if mtueType == "CPE500":
        fpgaVersionName = "fpga_cur.swv"
        iscpe500 = True
    elif mtueType == "TUE700M01":
        fpgaVersionName = "tue700TF_fpga_cur.swv"
    elif mtueType == "TUE700M02":
        fpgaVersionName = "tue700TT_fpga_cur.swv"
    elif mtueType == "TUE700M03":
        fpgaVersionName = "tue700FF_fpga_cur.swv"
    else:
        logging.info("mtueType error")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "hardIp", "************")
    ContractService.execute_contract("Cpe500Basic.mtue_ci2_upgrade", {
        'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    if iscpe500:
        ContractService.execute_contract("Cpe500Basic.mtue_cpe500_ci3_upgrade", {
            'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    else:
        ContractService.execute_contract("Cpe500Basic.mtue_tue700_ci3_upgrade", {
            'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    ContractService.execute_contract("Cpe500Basic.mtue_fpga_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, 'fpgaVersionName': fpgaVersionName, "ftpIpAddr": ftpIpAddr})
    return cpe500_reboot(cpe500Alias, True)

def tue600_fpga_ci2_upgrade(cpe500Alias, versionAlias):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(cpe500Alias, "mcs0Ip", "************")
    ContractService.execute_contract("Cpe500Basic.cpe500_fpga_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr})
    ftp2IpAddr = EnvPara().get_attribute(cpe500Alias, "mcs1Ip", "************")
    ContractService.execute_contract("Cpe500Basic.tue600_fpga2_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftp2IpAddr})
    ContractService.execute_contract("Cpe500Basic.tue600_UpdateFpga2", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftp2IpAddr})
    return cpe500_reboot(cpe500Alias, True)

def check_version_file(cpeAlias, versionAlias):
    versionPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ContractService.execute_contract("Cpe500Basic.version_file_exist", {
                                     'ueAlias': cpeAlias, 'versionAlias': versionPath})

def check_tue600_version_file(cpeAlias, verisonAlias):
    versionPath = EnvPara().get_attribute(verisonAlias, "artifactsPath")
    ContractService.execute_contract("Cpe500Basic.tue600_version_file_exist", {
                                     'ueAlias': cpeAlias, 'versionAlias': versionPath})

def check_ci2_version_file(cpeAlias, versionAlias):
    versionPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ContractService.execute_contract("Cpe500Basic.version_file_exist", {
                                     'ueAlias': cpeAlias, 'versionAlias': versionPath})

def get_atg_tun_packets_info(cpeAlias):
    try:
        cmdResult = ContractService.execute_contract('Cpe500Basic.get_ifconfig_info', {'ueAlias': cpeAlias})
        statTime = time.time()
        tunStat = str(cmdResult.return_string).split('tun')[1]
        packetNum = re.findall(r"RX packets:\d+", tunStat)[0].split(':')[1]
    except:
        logging.debug("get_ifconfig_info error")
    return list((statTime, packetNum))

def get_process(cpeAlias):
    try:
        cmdResult = ContractService.execute_contract('Cpe500Basic.get_process', {'ueAlias': cpeAlias})
        cpeVersion = re.findall(".*/Product_lte.*?(V.*)", cmdResult.return_string)
        if cpeVersion:
            logging.debug('version is  {0}'.format(cpeVersion[0]))
    except:
        logging.debug("get_process error")
    return cpeVersion

def get_atg_version_info(cpeAlias):
    try:
        cmdResult = ContractService.execute_contract('Cpe500Basic.get_process', {'ueAlias': cpeAlias})
        cpeVersion = re.findall(".*/Product_lte.*?(V.*.\S+)", cmdResult.return_string)
        if cpeVersion:
            logCpeVersion = re.findall(".*/Product_lte.*?(V.*.\S+)", cmdResult.return_string)
            logging.debug('version is  {0}'.format(logCpeVersion[0]))
    except:
        logging.debug("get_process error")
    return cpeVersion

def check_atg_version(ueVersion, checkInfo):   
    return checkInfo in ueVersion

def get_tue_version_info(cpeAlias):
    queryresult = ContractService.execute_contract('Tue510Basic.query_tue_version_info',{'cpeAlias': cpeAlias}).return_string
    tueversioninfo = re.findall('Product_lte_tdd.+(V\S+)\s', queryresult)[0]
    return tueversioninfo


def get_tue_derivedqos_info(cpeAlias):
    queryresult = ContractService.execute_contract('Tue510Basic.query_tue_derivedqos_info',{'cpeAlias': cpeAlias}).return_string
    tuederivedqosinfo = re.findall('derivedQosRule->qosFlowId\s=\s(\d+)', queryresult)[0]
    return tuederivedqosinfo


def tue510_version_upgrade(cpe500Alias, versionAlias, isNeedLmt=False):
    DomainRepository().find(cpe500Alias).copy_map_file(versionAlias)
    if not isNeedLmt:
        ContractService.execute_contract("Tue510Basic.tue510_upgrade", {"ueAlias": cpe500Alias})
    else:
        artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
        ContractService.execute_contract('Tue510Basic.tue510_upgrade_by_lmt', {'ueAlias': cpe500Alias, 'versionAlias': artifactsPath})
    ContractService.execute_contract("Tue510Basic.tue_reboot", {"cpeAlias": cpe500Alias})


def tue_cell_check_ulmac(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    DspResult = ContractService().execute_contract('Tue510Basic.tue_cell_ulmac', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult == 1:
        return True
    else:
        return False


def tue_cell_check_dsp(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_dspceva_ready', {'cpeAlias': cpeAlias})
    DspResult1 = ContractService().execute_contract('Tue510Basic.tue_cell_dsp1', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_cell_dsp2', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 1 and DspResult2 == 0:
        return True
    else:
        return False


def tue_cell_check_dspresetnum(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    DspResult3 = ContractService().execute_contract('Tue510Basic.tue_cell_dsp3', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult4 = ContractService().execute_contract('Tue510Basic.tue_cell_dsp4', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult3 != DspResult4:
        return False
    else:
        return str(DspResult3)


def tue_cmac_msg_set(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_cmac_msg1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract(
        'Tue510Basic.tue_cmac_msg2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract(
        'Tue510Basic.tue_cmac_msg3', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract(
        'Tue510Basic.tue_cmac_msg4', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract(
        'Tue510Basic.tue_cell_DebugSwitch', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract(
        'Tue510Basic.tue_cell_debugtype', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult3 = ContractService().execute_contract(
        'Tue510Basic.tue_cell_Msg2Switch', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult4 = ContractService().execute_contract(
        'Tue510Basic.tue_cell_Msg4Switch', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 1 and DspResult2 == 2 and DspResult3 == 1 and DspResult4 == 1:
        return True
    else:
        return False


def tue_reboot(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService.execute_contract("Tue510Basic.tue_reboot", {"cpeAlias": cpeAlias})
    sleep(1)
    return True


def tue_check_interrupt(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    Result1 = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt1', {'cpeAlias': cpeAlias})
    sleep(2)
    Result2 = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt2', {'cpeAlias': cpeAlias})
    sleep(2)
    Result3 = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt3', {'cpeAlias': cpeAlias})
    sleep(2)
    Result4 = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt4', {'cpeAlias': cpeAlias})
    sleep(2)

    Result1_new = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt1', {'cpeAlias': cpeAlias})
    sleep(2)
    Result2_new = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt2', {'cpeAlias': cpeAlias})
    sleep(2)
    Result3_new = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt3', {'cpeAlias': cpeAlias})
    sleep(2)
    Result4_new = ContractService().execute_contract(
        'Tue510Basic.tue_checkinterrupt4', {'cpeAlias': cpeAlias})
    sleep(2)
    if Result1_new != Result1 and Result2_new != Result2 and Result3_new != Result3 and Result4_new != Result4:
        return True
    else:
        return False


def tue_check_pdcch_interrupt(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    Result1 = ContractService().execute_contract(
        'Tue510Basic.tue_checkpdcchinterrupt1', {'cpeAlias': cpeAlias})
    sleep(2)
    Result2 = ContractService().execute_contract(
        'Tue510Basic.tue_checkpdcchinterrupt2', {'cpeAlias': cpeAlias})
    sleep(2)
    Result1_new = ContractService().execute_contract(
        'Tue510Basic.tue_checkpdcchinterrupt1', {'cpeAlias': cpeAlias})
    sleep(2)
    Result2_new = ContractService().execute_contract(
        'Tue510Basic.tue_checkpdcchinterrupt2', {'cpeAlias': cpeAlias})
    sleep(2)
    if Result1_new != Result1 and Result2_new != Result2:
        return True
    else:
        return False


def tue_send_prach(cpeAlias):
    format_value = re.compile('data=0x(\S+)')
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_sendprach', {'cpeAlias': cpeAlias})
    sleep(2)
    Result1 = ContractService().execute_contract('Tue510Basic.tue_cell_debugtype', {'cpeAlias': cpeAlias})
    sleep(2)
    if Result1 != 1:
        return False
    try:
        Result2 = re.search(format_value, ContractService().execute_contract(
            'Tue510Basic.tue_checkprach2', {'cpeAlias': cpeAlias}).return_string).group(1)
        sleep(5)
        Result3 = re.search(format_value, ContractService().execute_contract(
            'Tue510Basic.tue_checkprach3', {'cpeAlias': cpeAlias}).return_string).group(1)
        sleep(5)
        Result4 = re.search(format_value, ContractService().execute_contract(
            'Tue510Basic.tue_checkprach3', {'cpeAlias': cpeAlias}).return_string).group(1)
        sleep(5)
        Result5 = re.search(format_value, ContractService().execute_contract(
            'Tue510Basic.tue_checkprach3', {'cpeAlias': cpeAlias}).return_string).group(1)
        sleep(5)
        if Result5 != Result4 and Result4 != Result3 and Result2 != '0':
            return True
        else:
            return False
    except:
        return False



def tue_get_uptime(cpeAlias):
    ISOTIMEFORMAT = '%Y-%m-%d %X'
    Local_time = time.strftime(ISOTIMEFORMAT, time.localtime())
    hour_time = int(Local_time[11:13])
    if hour_time < 7:
        sleep(4000)
        Cpe500(cpeAlias)._register_contracts()
        result = ContractService().execute_contract('Tue510Basic.tue_getuptime', {'cpeAlias': cpeAlias}).return_string
        upmin = re.findall(r'min', result)
        if upmin:
            return False
        uphour = re.findall(r'up\s+(\d+):', result)
        try:
            if int(uphour[0]) >= 1:
                return True
            else:
                return False
        except:
            return True
    else:
        return True


def tue_pusch_dmac(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_dmac_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dmac_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dmac_write3', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dmac_write4', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dmac_write5', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dmac_write6', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dmac_write7', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dmac_write8', {'cpeAlias': cpeAlias})
    sleep(2)
    return True


def tue_pusch_cmac(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_cmac_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract(
        'Tue510Basic.tue_cmac_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract(
        'Tue510Basic.tue_cmac_write3', {'cpeAlias': cpeAlias})
    sleep(2)
    return True


def tue_msg2_mcs0_dsp1(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_write3', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_write4', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_write5', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract(
        'Tue510Basic.tue_msg2_mcs0_read1', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_read2', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult3 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_read3', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult4 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_read4', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult5 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_read5', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 0 and DspResult2 == 2 and DspResult3 == 1 and DspResult4 == 0 and DspResult5 == 0:
        return True
    else:
        return False



def tue_msg4_mcs0_dsp1(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_write3', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_write4', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_write5', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_read1', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_read2', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult3 = ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_read3', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult4 = ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_read4', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult5 = ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_read5', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 0 and DspResult2 == 2 and DspResult3 == 63 and DspResult4 == 1 and DspResult5 == 0:
        return True
    else:
        return False


def tue_msg6_mcs0_dsp1(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_write3', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_write4', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_write5', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_read1', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_read2', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult3 = ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_read3', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult4 = ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_read4', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult5 = ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_read5', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 0 and DspResult2 == 2 and DspResult3 == 63 and DspResult4 == 2 and DspResult5 == 0:
        return True
    else:
        return False


def tue_close_mcs0dpu_interrupt(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_close_mcs0dpu_interrupt', {'cpeAlias': cpeAlias})
    sleep(2)
    return True



def tue_msg2_mcs0_dsp2(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_write6', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_write7', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_read6', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs0_read7', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 0 and DspResult2 == 144:
        return True
    else:
        return False


def tue_msg4_mcs0_dsp2(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_write7', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg4_mcs0_read7', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult2 == 144:
        return True
    else:
        return False


def tue_msg6_mcs0_dsp2(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_write7', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg6_mcs0_read7', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult2 == 144:
        return True
    else:
        return False


def tue_msg2_mcs1_dsp(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_write3', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_write4', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_write5', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_read1', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_read2', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult3 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_read3', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult4 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_read4', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult5 = ContractService().execute_contract('Tue510Basic.tue_msg2_mcs1_read5', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 0 and DspResult2 == 2 and DspResult3 == 1 and DspResult4 == 1 and DspResult5 == 0:
        return True
    else:
        return False


def tue_msg46_mcs1_dsp(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_write3', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_write4', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_write5', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_write6', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_read1', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_read2', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult3 = ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_read3', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult4 = ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_read4', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult5 = ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_read5', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult6 = ContractService().execute_contract('Tue510Basic.tue_msg46_mcs1_read6', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 0 and DspResult2 == 2 and DspResult3 == 1 and DspResult4 == 1 and DspResult5 == 0 and DspResult6 == 1:
        return True
    else:
        return False



def tue_pusch_write_mem(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_write_file_to_mem', {'cpeAlias': cpeAlias})
    sleep(20)
    return True


def tue_msg2_write_mem1(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg2_write_file_to_mem1', {'cpeAlias': cpeAlias})
    sleep(20)
    return True


def tue_msg2_write_mem2(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg2_write_file_to_mem2', {'cpeAlias': cpeAlias})
    sleep(20)
    return True


def tue_msg4_write_mem1(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg4_write_file_to_mem1', {'cpeAlias': cpeAlias})
    sleep(20)
    return True


def tue_msg4_write_mem2(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg4_write_file_to_mem2', {'cpeAlias': cpeAlias})
    sleep(20)
    return True


def tue_msg6_write_mem1(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg6_write_file_to_mem1', {'cpeAlias': cpeAlias})
    sleep(20)
    return True


def tue_msg6_write_mem2(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_msg6_write_file_to_mem2', {'cpeAlias': cpeAlias})
    sleep(20)
    return True


def tue_pusch_data_start(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_write_mem_start', {'cpeAlias': cpeAlias})
    sleep(2)
    return True


def tue_cmac_uds(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_cmac_uds_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_cmac_uds_write2', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult1 = ContractService().execute_contract('Tue510Basic.tue_cmac_uds_read1', {'cpeAlias': cpeAlias})
    sleep(2)
    DspResult2 = ContractService().execute_contract('Tue510Basic.tue_cmac_uds_read2', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult1 == 1 and DspResult2 == 1:
        return True
    else:
        return False


def save_tue_samplebfdatafile(cpeAlias, filename):
    return ContractService().execute_contract('Tue510Basic.save_tue_samplebfdatafile', {'cpeAlias': cpeAlias, 'filename': filename})


def tue_pucch_cmac_fmt0(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_cmac_pucch_fmt0_2_switch', {'cpeAlias': cpeAlias})
    ContractService().execute_contract('Tue510Basic.tue_cmac_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_cmac_pucch_fmt0', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_cmac_pucch_debugType3', {'cpeAlias': cpeAlias})
    sleep(2)
    return True


def tue_pucch_cmac_fmt2(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_cmac_pucch_fmt0_2_switch', {'cpeAlias': cpeAlias})
    ContractService().execute_contract('Tue510Basic.tue_cmac_write1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_cmac_pucch_fmt2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_cmac_pucch_debugType3', {'cpeAlias': cpeAlias})
    sleep(2)
    return True



def tue_config_frequency_packet(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_dsp_config_frequency_packet', {'cpeAlias': cpeAlias})
    return True



def tue_send_frequency_packet(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_dsp_send_frequency_packet_1', {'cpeAlias': cpeAlias})
    result1 = ContractService().execute_contract('Tue510Basic.tue_dsp_send_frequency_packet_2', {'cpeAlias': cpeAlias})
    ContractService().execute_contract('Tue510Basic.tue_dsp_send_frequency_packet_3', {'cpeAlias': cpeAlias})
    if not result1 == 0:
        return False
    return True


def tue_send_time_domain_packet(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_dsp_send_time_domain_packet_1', {'cpeAlias': cpeAlias})
    result1 = ContractService().execute_contract('Tue510Basic.tue_dsp_send_time_domain_packet_2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_dsp_send_time_domain_packet_3', {'cpeAlias': cpeAlias})
    if not result1 == 0:
        return False
    return True


def tue_config_frame_head(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    result = ContractService().execute_contract(
        'Tue510Basic.tue_config_frame_head', {'cpeAlias': cpeAlias}).return_string
    frame_head = re.findall('data=(\w+)', result)
    if not frame_head:
        return False
    frame_head_offset = hex(int(frame_head[0], 16) - 32)
    ContractService().execute_contract('Tue510Basic.tue_config_frame_head_2',
                                       {'cpeAlias': cpeAlias, 'frame_head_offset': frame_head_offset})
    frame_head = frame_head[0]
    ContractService().execute_contract('Tue510Basic.tue_config_frame_head_3',
                                       {'cpeAlias': cpeAlias, 'frame_head': frame_head})
    ContractService().execute_contract('Tue510Basic.tue_auto_dl_sync_resetue', {'cpeAlias': cpeAlias})
    sleep(5)
    dProcState = ContractService().execute_contract('Tue510Basic.tue_config_frame_head_4', {'cpeAlias': cpeAlias})
    if not dProcState == 2:
        return False
    return True


def tue_206_config_frame_head(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_206_config_frame_head3', {'cpeAlias': cpeAlias})
    sleep(1)
    dspresult = ContractService().execute_contract(
        'Tue510Basic.tue_206_config_frame_head3_read', {'cpeAlias': cpeAlias})
    if dspresult != 1:
        return False
    dspresult1 = ContractService().execute_contract('Tue510Basic.tue_206_config_frame_head1', {'cpeAlias': cpeAlias})
    frame_head = hex(dspresult1 + 32)
    ContractService().execute_contract('Tue510Basic.tue_206_config_frame_head2',
                                       {'cpeAlias': cpeAlias, 'frame_head': frame_head})
    sleep(1)
    ContractService().execute_contract(
        'Tue510Basic.tue_206_config_frame_head_read', {'cpeAlias': cpeAlias})
    return True


def tue_206_dl_sync(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    dProcState = ContractService().execute_contract('Tue510Basic.tue_config_frame_head_4', {'cpeAlias': cpeAlias})
    if not dProcState == 2:
        return False
    return True


def tue_auto_dl_sync(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_send_msg3_stub_1', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_send_msg3_stub_2', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_config_frame_head_5', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_config_frame_head_6', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_config_frame_head_7', {'cpeAlias': cpeAlias})
    return True



def tue_check_dl_sync(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    dProcState = ContractService().execute_contract(
        'Tue510Basic.tue_config_frame_head_8', {'cpeAlias': cpeAlias})
    for _ in xrange(5):
        dProcState = ContractService().execute_contract(
            'Tue510Basic.tue_config_frame_head_4', {'cpeAlias': cpeAlias})
        if dProcState == 2:
            return True
        else:
            tue_config_frame_head(cpeAlias)
            tue_auto_dl_sync(cpeAlias)
            sleep(5)
    return False


def tue_send_msg1_stub(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_fpga0_syn', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_fpga1_syn', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_fpga0_set1', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_fpga0_reset0', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_fpga1_set1', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_fpga1_reset0', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_send_msg1_stub', {'cpeAlias': cpeAlias})


def tue_send_msg2_stub(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_send_msg2_mcs0_stub', {'cpeAlias': cpeAlias})
    ContractService().execute_contract('Tue510Basic.tue_send_msg2_mcs1_stub', {'cpeAlias': cpeAlias})
    return True


def tue_turn_nsa_dl_sync(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    for _ in xrange(15):
        pbchBitInfo = ContractService().execute_contract('Tue510Basic.tue_check_dl_sync', {'cpeAlias': cpeAlias})
        ContractService().execute_contract('Tue510Basic.tue_nsa_switch_cellid', {'cpeAlias': cpeAlias})
        pbchBitInfo_hex16 = hex(int(pbchBitInfo))
        if pbchBitInfo_hex16[-3:] == '050' or pbchBitInfo_hex16[-3:] == '50L':
            return True
        else:
            ContractService().execute_contract('Tue510Basic.tue_config_nsa_g_dProcState', {'cpeAlias': cpeAlias})
            sleep(1)
    return False


def tue_nsa_attach_check(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    DspResult = ContractService().execute_contract(
        'Tue510Basic.tue_nsa_attach_read', {'cpeAlias': cpeAlias})
    sleep(2)
    if DspResult == 2:
        return True
    else:
        return False


def tue_send_core8_test(cpeAlias):
    ContractService().execute_contract(
        'Tue510Basic.tue_test_5', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_test_6', {'cpeAlias': cpeAlias})
    sleep(4)
    ContractService().execute_contract(
        'Tue510Basic.tue_test_7', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract(
        'Tue510Basic.tue_test_8', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract(
        'Tue510Basic.tue_send_cmac_test1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract(
        'Tue510Basic.tue_send_cmac_test2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract(
        'Tue510Basic.tue_send_dmac_test1', {'cpeAlias': cpeAlias})
    sleep(3)
    ContractService().execute_contract(
        'Tue510Basic.tue_send_dmac_test2', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract(
        'Tue510Basic.tue_send_cmac_test3', {'cpeAlias': cpeAlias})
    ContractService().execute_contract(
        'Tue510Basic.tue_test_1', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract(
        'Tue510Basic.tue_test_2', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract(
        'Tue510Basic.tue_test_3', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract(
        'Tue510Basic.tue_test_4', {'cpeAlias': cpeAlias})
    sleep(1)
    return True


def tue_test_write_mem(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_test_9', {'cpeAlias': cpeAlias})
    sleep(10)
    return True


def tue_send_srs_test(cpeAlias):
    ContractService().execute_contract('Tue510Basic.tue_test_1', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_2', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_3', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_5', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_6', {'cpeAlias': cpeAlias})
    sleep(4)
    ContractService().execute_contract('Tue510Basic.tue_test_7', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_8', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_srs_send1', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_test_srs_send2', {'cpeAlias': cpeAlias})
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_test_srs_send3', {'cpeAlias': cpeAlias})
    sleep(2)
    return True


def tue_send_prach_test(cpeAlias):
    ContractService().execute_contract('Tue510Basic.tue_test_1', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_2', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_3', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_5', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_6', {'cpeAlias': cpeAlias})
    sleep(4)
    ContractService().execute_contract('Tue510Basic.tue_test_7', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_8', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_prach_dsp1', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_prach_dsp2', {'cpeAlias': cpeAlias})
    sleep(1)
    ContractService().execute_contract('Tue510Basic.tue_test_prach_dsp3', {'cpeAlias': cpeAlias})
    sleep(1)
    return True


def tue_master_reboot(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService.execute_contract(
        "Tue510Basic.tue_reboot", {"cpeAlias": cpeAlias})
    sleep(1200)
    return True


def tue_rlfswitch_open(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_rlfswitch_open_write', {'cpeAlias': cpeAlias})
    sleep(1)
    dspresult = ContractService().execute_contract(
        'Tue510Basic.tue_rlfswitch_open_read', {'cpeAlias': cpeAlias})
    if dspresult != 1:
        return False
    return True


def tue_dl_throughput(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    Result1 = ContractService().execute_contract(
        'Tue510Basic.tue_dl_throughput1', {'cpeAlias': cpeAlias})
    sleep(1)
    Result2 = ContractService().execute_contract(
        'Tue510Basic.tue_dl_throughput2', {'cpeAlias': cpeAlias})
    sleep(1)
    Result3 = ContractService().execute_contract(
        'Tue510Basic.tue_dl_throughput3', {'cpeAlias': cpeAlias})
    sleep(1)
    Result4 = ContractService().execute_contract(
        'Tue510Basic.tue_dl_throughput4', {'cpeAlias': cpeAlias})
    Result = int(Result1) + int(Result2) + int(Result3) + int(Result4)
    sleep(1)
    return Result


def tue_ul_throughput(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    Result1 = ContractService().execute_contract(
        'Tue510Basic.tue_ul_throughput1', {'cpeAlias': cpeAlias})
    sleep(1)
    Result2 = ContractService().execute_contract(
        'Tue510Basic.tue_ul_throughput1', {'cpeAlias': cpeAlias})
    sleep(1)
    Result3 = ContractService().execute_contract(
        'Tue510Basic.tue_ul_throughput1', {'cpeAlias': cpeAlias})
    sleep(1)
    Result4 = ContractService().execute_contract(
        'Tue510Basic.tue_ul_throughput1', {'cpeAlias': cpeAlias})
    sleep(1)
    Result5 = ContractService().execute_contract(
        'Tue510Basic.tue_ul_throughput1', {'cpeAlias': cpeAlias})
    sleep(1)
    Result11 = (Result1 + Result2 + Result3 + Result4 + Result5) / 5
    Result22 = ContractService().execute_contract(
        'Tue510Basic.tue_ul_throughput2', {'cpeAlias': cpeAlias})
    Result = (int(Result22) + int(Result11)) * 8 / 1024 / 1024
    sleep(1)
    return Result


def tue_prevent_beam_change(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract(
        'Tue510Basic.tue_prevent_beam_change', {'cpeAlias': cpeAlias})
    return True


def get_dl_throughout_dsp(cpeAlias,flag='AVG',duration=20,unit='M',savetag='null'):
    Cpe500(cpeAlias)._register_contracts()
    paraDict={
              'cpeAlias':cpeAlias,
              'mcsId':0,
              'coreId':8,
              'expression':'g_rxDmacTaskDataPtr->rxDmacRcvTbStat.rcvDataKBps'
              }
    resultList=[]
    dl_throughout=0
    for i in range(int(duration)):
        result=ContractService().execute_contract('Tue510Basic.tue_read_var_value', paraDict)
        resultList.append(result)
        time.sleep(1)
    if unit.upper()=='M':
        resultList=[float(each)*8/1024 for each in resultList]
    elif unit.upper()=='K':
        resultList=[float(each)*8 for each in resultList]
    elif unit.upper()=='G':
        resultList=[float(each)*8/1024/1024 for each in resultList]
    if flag.upper()=='AVG':
        dl_throughout=float(sum(resultList))/len(resultList)
    elif flag.upper()=='MAX':
        dl_throughout=max(resultList)
    elif flag.upper()=='MIN':
        dl_throughout=min(resultList)
    dirPath=r'D:\data'
    if not os.path.exists(dirPath):
        os.makedirs(dirPath)
    filePath=os.path.join(dirPath,'dl_%s_%s.csv'%(savetag,time.strftime('%m%d_%H%M')))
    name=['index','dl_throughout','bler']
    with open(filePath,'wb') as f:
        writer=csv.writer(f)
        writer.writerow(name)
        for i in range(len(resultList)):
            writer.writerow([i+1,resultList[i]])
    return round(dl_throughout,2)

def atg_fpga_ci2_upgrade(cpe500Alias, versionAlias,boardtype):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    ftpIpAddr = EnvPara().get_attribute(
        cpe500Alias, "hardIp", "*************")
    ContractService.execute_contract("Cpe500Basic.atg_fpga_ci2_upgrade", {
                                     'ueAlias': cpe500Alias, 'versionAlias': artifactsPath, "ftpIpAddr": ftpIpAddr,"boardtype":boardtype})
    return cpe500_reboot(cpe500Alias, True)

def atg_create_atfolder(cpe500Alias, versionAlias, boardtype):
    artifactsPath = EnvPara().get_attribute(versionAlias, "artifactsPath")
    localPath = artifactsPath + "/CPE/" + boardtype + "/SwFiles/"
    ContractService.execute_contract('Cpe500Basic.atg_create_atfolder', {'ueAlias': cpe500Alias})
    return localPath

def atg_delete_atfolder(cpe500Alias):
    return ContractService.execute_contract('Cpe500Basic.atg_delete_atfolder', {'ueAlias': cpe500Alias}).result

def rename_rfindicator_file(cpe500Alias,oldFileName,newFileName):
    return ContractService.execute_contract('Cpe500Basic.rename_rfindicator_file', {'ueAlias': cpe500Alias, 'oldFileName': oldFileName, 'newFileName': newFileName}).result


def check_fpga_version_file(cpeAlias, verisonAlias):
    versionPath = EnvPara().get_attribute(verisonAlias, "artifactsPath")
    ContractService.execute_contract("Tue510Basic.fpga_version_file_exist", {
                                     'ueAlias': cpeAlias, 'versionAlias': versionPath})

def check_mtue_fpga_version_file(cpeAlias, verisonAlias, mtueType):
    versionPath = EnvPara().get_attribute(verisonAlias, "artifactsPath")
    if mtueType == "CPE500":
        fpgaVersionName = "fpga_cur.swv"
    elif mtueType == "TUE700M01":
        fpgaVersionName = "tue700TF_fpga_cur.swv"
    elif mtueType == "TUE700M02":
        fpgaVersionName = "tue700TT_fpga_cur.swv"
    elif mtueType == "TUE700M03":
        fpgaVersionName = "tue700FF_fpga_cur.swv"
    else:
        logging.info("mtueType error")    
    ContractService.execute_contract("Tue510Basic.mtue_fpga_version_file_exist", {
                                     'ueAlias': cpeAlias, 'versionAlias': versionPath, 'fpgaVersionName': fpgaVersionName})

def mue8500_upgrade(cpe500Alias):
    return ContractService.execute_contract('Cpe500Basic.mue8500_upgrade', {'ueAlias': cpe500Alias}).result

def set_mue_board_stub(ueAlias, expression):
    ip = EnvPara().get_attribute(ueAlias, 'hardIp', '***********')
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    client.connect(ip, 22, 'root', 'mueci_123')
    appPid = get_mue_app_pid(client)
    shell = client.invoke_shell()
    shell.sendall("ushell\n")
    sleep(1)
    shell.sendall("pad {}\n".format(appPid))
    sleep(1)
    shell.sendall("{}\n".format(expression))
    client.close()

def get_mue_app_pid(client):
    stdin, stdout, stderr = client.exec_command('ps -ef|grep -E "APP.EXE" |grep -v grep|awk \'{print $2}\'\n')
    for line in stdout.readlines():
        return str(line)