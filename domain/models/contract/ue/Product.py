# coding=utf-8
'''
Created on 2019年7月23日

@author: 10207409
'''
import re
from time import sleep
import logging
import datetime, time, os
import inspect
import ctypes
import paramiko
import threading
from testlib5g.domain.model.ue.cpe500.Cpe500 import Cpe500
from testlib5g.infrastructure.utility.envpara.EnvPara import EnvPara
from testlib5g.app_service.basic.contract.ContractService import ContractService
from testlib5g.app_service.basic.ume.dms.DmsResfulService import DmsResfulService
from testlib5g.infrastructure.utility.Repeat import retries_on_exception

def set_fre(ueAlias):
    fre = EnvPara().get_attribute(ueAlias, 'fre')
    return ContractService.execute_contract('Cpe500Basic.set_fre', {'ueAlias': ueAlias, 'fre': fre}).result


def change_flowtest(ueAlias, flowTestValue):
    ContractService.execute_contract('Cpe500Basic.set_flowTest',
                                     {'ueAlias': ueAlias, 'flowTestValue': flowTestValue})


def set_pc_mac_addr(cpeAlias):
    pc_mac = EnvPara().get_attribute(cpeAlias, 'pc_mac')
    ContractService.execute_contract('Cpe500Basic.set_pc_mac', {'ueAlias': cpeAlias, 'pc_mac': pc_mac})


def stub_reestablish(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.stub_reestablish', {'cpeAlias': cpeAlias})
    return True


def get_qos_flow_id(cpeAlias):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_StubShowQosContext', {'ueAlias': cpeAlias}).return_string
    result = re.findall(r"derivedQosRule->qosFlowId = \d+", returnStr)
    qosFlowIdList = result[0].split(" = ")
    qosFlowId = qosFlowIdList[1]
    return qosFlowId

def get_ushellWhiteListOpenMode_addr(cpeAlias):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_ushell_white_list', {'ueAlias': cpeAlias}).return_string
    result = re.findall(r"\w+   g_bUshellWhiteListOpenMode", returnStr)
    addr = result[0].split("   ")[0]
    return addr

def reset_tue_ready(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_reset_ready', {'cpeAlias': cpeAlias}).return_string
    sleep(30)
    return True


def tue_close_simulateRFS(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_close_simRFS', {'cpeAlias': cpeAlias}).return_string
    sleep(10)
    return True


def tue_mib_cell(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_mib_simula2', {'cpeAlias': cpeAlias}).return_string
    sleep(10)
    ContractService().execute_contract('Tue510Basic.tue_mib_simula3', {'cpeAlias': cpeAlias}).return_string
    sleep(10)
    return True


def tue_rmsi1_cell(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_rmsi_simula1', {'cpeAlias': cpeAlias}).return_string
    sleep(10)
    return True


def tue_rmsi2_cell(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_rmsi_simula2', {'cpeAlias': cpeAlias}).return_string
    sleep(5)
    return True


def tue_cell_check_select(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    cellCheck = ContractService().execute_contract('Tue510Basic.tue_cell_select', {'cpeAlias': cpeAlias}).return_string
    list_search = re.findall(r'ucCellSelectResult = 1', cellCheck)
    try:
        if list_search != []:
            return True
        else:
            return False
    except:
        return False


def tue_access_print_set(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_access_print', {'cpeAlias': cpeAlias}).return_string
    sleep(2)
    return True


def tue_access_attach(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    attachResult = ContractService().execute_contract(
        'Tue510Basic.tue_attach', {'cpeAlias': cpeAlias}).return_string
    list1 = re.findall(r'received: EV_ULMAC_RRC_RA_SUCCESS', attachResult)
    list2 = re.findall(r'received: EV_ULMAC_RRC_RA_REPORT', attachResult)
    list3 = re.findall(r'received: EV_ULMAC_RRC_CRNTI', attachResult)
    try:
        if list1 != [] and list2 != [] and list3 != []:
            return True
        else:
            return False
    except:
        return False


def tue_rrc_init(cpeAlias):
    format_value = re.compile('From\s\DRRC_INST_IDLE\D\sTo\s\DRRC_INST_CONNECTED')
    Cpe500(cpeAlias)._register_contracts()
    rrcinitResult = ContractService().execute_contract('Tue510Basic.tue_rrcinit', {'cpeAlias': cpeAlias}).return_string
    list_search = re.findall(format_value, rrcinitResult)
    try:
        if list_search != []:
            return True
        else:
            return False
    except:
        return False


def tue_rrc_reconfig(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    rrcreconfigResult = ContractService().execute_contract('Tue510Basic.tue_rrcconfig', {'cpeAlias': cpeAlias}).return_string
    list_search = re.findall(r'setNextState: state =4, substate =3', rrcreconfigResult)
    try:
        if list_search != []:
            return True
        else:
            return False
    except:
        return False


def tue_rrc_release(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_rrcrelease1', {'cpeAlias': cpeAlias}).return_string
    sleep(2)
    rrcreleaseResult2 = ContractService().execute_contract('Tue510Basic.tue_rrcrelease2', {'cpeAlias': cpeAlias}).return_string
    sleep(2)
    ContractService().execute_contract('Tue510Basic.tue_rrcrelease3', {'cpeAlias': cpeAlias}).return_string
    sleep(2)
    list_search = re.findall(r'Enter RRC_INST_IDLE state', rrcreleaseResult2)
    try:
        if list_search != []:
            return True
        else:
            return False
    except:
        return False


def tue_check_msg8(cpeAlias, ueImsi):
    Cpe500(cpeAlias)._register_contracts()
    try:
        result = ContractService().execute_contract('Tue510Basic.tue_send_msg1_stub_attach',
                                                    {'cpeAlias': cpeAlias, 'ueImsi': ueImsi}).return_string
    except Exception:
        return False
    return True


def tue_check_206_msg8(cpeAlias, ueImsi):
    Cpe500(cpeAlias)._register_contracts()
    try:
        result = ContractService().execute_contract('Tue510Basic.tue_send_206_attach',
                                                    {'cpeAlias': cpeAlias, 'ueImsi': ueImsi}).return_string
    except Exception:
        return False
    return True


def get_ue_ip(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    result = ContractService().execute_contract(
        'Tue510Basic.get_ue_ip', {'cpeAlias': cpeAlias}).return_string
    ueip = re.findall('Type IPV4: Addr (\S+)', result)
    if not ueip:
        return False
    return ueip[0]


def detach_tue(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_detach', {'cpeAlias': cpeAlias}).return_string
    return True


def tue_recovery_a2_threshold(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_recovery_a2_threshold', {'cpeAlias': cpeAlias})
    return True


def tue_send_a2mr(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_send_a2mr', {'cpeAlias': cpeAlias})
    return True


def tue_send_a4mr(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_send_a4mr', {'cpeAlias': cpeAlias})
    return True


def tue_set_a2_threshold(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_set_a2_threshold', {'cpeAlias': cpeAlias})
    return True


def tue_rlfdetachcheck(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    detachResult = ContractService().execute_contract('Tue510Basic.tue_detachcheck', {'cpeAlias': cpeAlias}).return_string
    list_search = re.findall(r'rrc State: 2', detachResult)
    try:
        if list_search != []:
            return True
        else:
            return False
    except:
        return False


def tue_dl_control(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_dl_throughput_control', {'cpeAlias': cpeAlias})
    return True


def tue_dl_control0(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.tue_dl_throughput_control0', {'cpeAlias': cpeAlias})
    return True


def modify_reset_timer(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.modify_reset_timer',{'cpeAlias': cpeAlias}).return_string


def reset_check_point(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.Reset_tue_Check_Pointment', {'cpeAlias':cpeAlias})


def get_each_syn_attach_delay(cpeAlias,synTimeThred,attachTimeThred):
    Cpe500(cpeAlias)._register_contracts()
    result=ContractService().execute_contract('Tue510Basic.calculate_tue_SynAndAttach_delay', {'cpeAlias':cpeAlias}).return_string
    eachTimeList = re.findall(r'lastTime: (\d+\.\d+)', result)
    flag=0
    feachTimeList=[float(i) for i in eachTimeList]
    ieachTimeList=[int(i) for i in feachTimeList]
    eachTimeSyn=ieachTimeList[1]+ieachTimeList[2]
    eachAttachTime=sum(ieachTimeList)
    if eachTimeSyn > synTimeThred or eachAttachTime > attachTimeThred:
        flag=1
    return eachTimeSyn,eachAttachTime,flag

def set_gIsUseLVFormat(ueAlias, value):
    return ContractService.execute_contract('Cpe500Basic.set_gIsUseLVFormat', {'ueAlias': ueAlias, 'value': value}).result

def atg_send_sitemap(ueAlias):
    return ContractService.execute_contract('Cpe500Basic.atg_send_sitemap', {'ueAlias': ueAlias}).result

def atg_send_sibswitch(ueAlias, sibSwitch):
    value = 0 if sibSwitch == 'open' else 1
    return ContractService.execute_contract('Cpe500Basic.atg_send_sibswitch', {'ueAlias': ueAlias, 'value': value}).result

def atg_add_whitelist(ueAlias):
    return ContractService.execute_contract('Cpe500Basic.atg_add_whitelistcmd_product', {'ueAlias': ueAlias}).result

def get_cpe_moc_values(ueAlias, mocName):
    mocName = "Device." + mocName + "."
    motree = ContractService.execute_contract('Cpe500Basic.atg_query_motree',{'ueAlias': ueAlias, 'attrName': mocName}).return_string
    logging.debug(motree)
    return motree


def check_branchnode_config(motree, attrDic):
    for i in range(len(attrDic['attrNames'])):
        cpeResult = re.findall('"' + attrDic['attrNames'][i] + '" value="(\S+)"', motree)
        if len(cpeResult) == 0:
            logging.warn('Name:%s value not found!'%(attrDic['attrNames'][i]))
            continue
        logging.info('Name:%s: dmsValue:%s , cpeValue:%s'%(attrDic['attrNames'][i],
                                                               attrDic['values'][0][i], cpeResult[0]))
        if attrDic['values'][0][i] != cpeResult[0]:
            return False
    return True


def atg_check_motree(ueAlias, dmsValues):
    #dmsValues列表，此列表有n个moc，每个moc是字典
    for moc_dict in dmsValues:
        motree = get_cpe_moc_values(ueAlias, moc_dict["moc"])
        flag = check_branchnode_config(motree, moc_dict)
        if not flag:
            return flag
    return True


def check_leafnode_config(motree, attrName, attrValue):
     cpeResult = re.findall('"' + attrName + '" value="(\S+)"', motree)
     if len(cpeResult) == 0:
        logging.warn('Name:%s value not found!'%(attrName))
        return False
     logging.info('Name:%s: dmsValue:%s , cpeValue:%s'%(attrName, attrValue, cpeResult[0]))
     if attrValue != cpeResult[0]:
         return False
     return True

def atg_check_leafnode(ueAlias, value, moc, attrName):
    motree = get_cpe_moc_values(ueAlias, moc)
    return check_leafnode_config(motree, attrName, value)

def atg_get_product_pid(cpeAlias):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_mcs0_product_pid', {'ueAlias': cpeAlias}).return_string
    taskList = returnStr.split("\n")
    for i in taskList:
        if i.find("Product_lte_tdd.o") > 1:
            appPid = i.split("zte", 1)[0]
            return appPid

def atg_get_version_info(cpeAlias):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_mcs0_product_pid', {'ueAlias': cpeAlias}).return_string
    taskList = returnStr.split("\n")
    for i in taskList:
        if i.find(".CV") > 1:
            break
    tmpList = i.split(' ')
    for i in tmpList:
        if ".CV" in i:
            logging.debug("version is:".format(i))
    return i

def remove_tmp_version_file(cpeAlias):
    return ContractService.execute_contract('Cpe500Basic.remove_tmpfile_from_flash', {'ueAlias': cpeAlias}).result

def turnon_rebootfunction(cpeAlias):
    Cpe500(cpeAlias)._register_contracts()
    ContractService().execute_contract('Tue510Basic.atg_turnon_reboot_switch', {'cpeAlias': cpeAlias})
    return True

def atg_switch_urllc_mode(cpeAlias, mode='1'):
    ContractService().execute_contract('Tue510Basic.atg_switch_urllc_mode', {'cpeAlias': cpeAlias, 'mode': mode})

def get_value_from_show_func(cpeAlias, funcName, valueName):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_CpsShowFuncLog', {'ueAlias': cpeAlias, 'showFunc': funcName}).return_string
    try:
        result = re.findall(valueName + r"\s*:\-?\d+", returnStr)
        resultList = result[0].split(":")
    except:
        result = re.findall(valueName + r"\s*=\s*\-?\d+", returnStr)
        resultList = result[0].split("=")
    value = resultList[1]
    return value

def get_value_from_show_funcnew(cpeAlias, funcName, valueName):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_CpsShowFuncLog', {'ueAlias': cpeAlias, 'showFunc': funcName}).return_string
    result = re.findall(valueName + r"\s*=\s*\-?\d+", returnStr)
    resultList = result[0].split("=") 
    value = resultList[1]
    return value

def get_cpe_global_variable_value(cpeAlias, globalVariable, valueLength, valueIndex):
    @retries_on_exception(5, time.sleep, 1)
    def _get_cpe_global_variable_value():
        returnStr = ContractService.execute_contract('Cpe500Basic.get_cpe_global_var_value', {'ueAlias': cpeAlias, 'globalVariable': globalVariable}).return_string
        result = re.findall(r"\d{" + valueLength.encode() + "}", "***".join(returnStr.split("\n")))
        value = int(result[int(valueIndex.encode())])
        return value
    return _get_cpe_global_variable_value()

def get_cpe_log_dir(cpeAlias):
    aliasList = EnvPara().get_link_devices(cpeAlias)
    lmtAlias = filter(lambda l: 'lmt' in l, aliasList)[0]
    logDir = EnvPara().get_attribute(lmtAlias, 'workDir')
    logDir = logDir + '\\User File\\CpsLog'
    if not os.path.exists(logDir):
        os.makedirs(logDir)
    return logDir


def create_client(cpeAlias):
    ip = EnvPara().get_attribute(cpeAlias, "ip")
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        client.connect(ip, 22, 'zte', 'eNodeB@V3^^')
    except:
        raise('login failed')
    return client


def create_client_failed(cpeAlias):
    ip = EnvPara().get_attribute(cpeAlias, "ip")
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        client.connect(ip, 22, 'zte', 'eNodeB')
        return True
    except:
        logging.debug('login failed')


def get_app_pid(client, appName='Product_lte_tdd.o'):
    stdin, stdout, stderr = client.exec_command("ps\n")
    for line in stdout.readlines():
        if line.find(str(appName)) > 1:
            appPid = re.split(' root| 0| zte', line)[0]
            return appPid


def get_cpe_log(cpeAlias, appName, logPath):
    client = create_client(cpeAlias)
    appPid = get_app_pid(client, appName)
    channel = client.invoke_shell()
    channel.sendall("/ushell\n")
    channel.sendall("pad " + str(appPid) + "\n")
    log = open(logPath, 'a+')
    while True:
        try:
            console_data = ""
            if channel.recv_ready():
                console_data += channel.recv(65535).decode('utf-8')
                console_data = console_data.replace("\r\n", "\n" + datetime.datetime.now().strftime('%m/%d-%H:%M:%S.%f# '))
                log.write(str(console_data))
        except:
            log.close()
            channel.close()
            client.close()
            break

def get_certificate_type_by_dms_and_cpe_log(cpeAlias, timeout=200):
    client = create_client(cpeAlias)
    appPid = get_app_pid(client)
    channel = client.invoke_shell()
    channel.sendall("/ushell\n")
    channel.sendall("pad " + str(appPid) + "\n")
    channel.sendall("dmcPrint 1")
    cerType = ''
    t = 0
    while t < timeout:
        try:
            console_data = ""
            if channel.recv_ready():
                console_data += channel.recv(65535).decode('utf-8')
                console_data = console_data.replace("\r\n", "\n")
                if console_data.find('cpeCaCertPath') != -1:
                    cerType = re.findall(r'/mnt/cert/(.*)(?=\), cpePublic)', console_data)
                    cerType = " ".join(cerType)
                    break
        except:
            channel.close()
            client.close()
            break
        time.sleep(1)
        t += 1
        if t == timeout:
            break
    return cerType

def continuously_get_cpe_log(cpeAlias, appName):
    logPath = get_cpe_log_dir(cpeAlias) + '\\' + time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime(time.time())) + ".log"
    thread = threading.Thread(target=get_cpe_log, args=(cpeAlias, appName, logPath))
    thread.start()
    thread.join(timeout=10)
    return thread, logPath


def stop_cpe_log(thread, exctype=SystemExit):
    if thread[0].is_alive():
        ttd = thread[0].ident
        ttd = ctypes.c_long(ttd)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(ttd, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError("invalid thread id")
        elif res != 1:
            ctypes.pythonapi.PyThreadState_SetAsyncExc(ttd, None)
            raise SystemError("PyThreadState_SetAsyncExc failed")
        return thread[1]
    else:
        raise('thread is dead')

def get_MGR_ushellWhiteListOpenMode_addr(cpeAlias):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_MGR_ushell_white_list', {'ueAlias': cpeAlias}).return_string
    result = re.findall(r"\w+   g_bUshellWhiteListOpenMode", returnStr)
    addr = result[0].split("   ")[0]
    return addr

def get_vaule_with_key(cpeAlias, funcName, valueName):
    returnStr = ContractService.execute_contract('Cpe500Basic.get_CpsShowFuncLog', {'ueAlias': cpeAlias, 'showFunc': funcName}).return_string
    result = re.findall(valueName + r".*:.*", returnStr)
    resultList = result[0].split(":", 1)
    return resultList[1].strip()

def delete_pz_file(cpeAlias):
    ContractService.execute_contract('Cpe500Basic.delete_pz_file', {'ueAlias': cpeAlias})
