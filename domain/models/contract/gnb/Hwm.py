# coding=utf-8
'''
Created on 2019年7月27日

@author: 10207409
'''
from testlib5g.app_service.basic.contract.ContractService import ContractService

def set_vsw_1ppstodclock(gnbAlias):
    return ContractService().execute_contract('Comm.set_vsw_1ppstodclock', {'gnbAlias': gnbAlias})

def vsw_clock_stub(gnbAlias, stubValue):
    return ContractService().execute_contract('Comm.vsw_clock_stub_bspclkfaultset', {'gnbAlias': gnbAlias, 'stubValue': stubValue})
    
def set_vbp_power_save(gnbAlias,pos_id):
    return ContractService().execute_contract('Comm.set_vbp_power_save', {'gnbAlias': gnbAlias,'pos_id': pos_id})
    
def set_vbp_power_on(gnb<PERSON>lias,pos_id):
    return ContractService().execute_contract('Comm.set_vbp_power_on', {'gnbAlias': gnb<PERSON>lias,'pos_id': pos_id})

def get_vbp_power_save(gnbAlias):
    result = ContractService().execute_contract('Comm.get_vbp_power_save', {'gnbAlias': gnbAlias}).return_string
    if "0x1b" in result:
        return True
    else:
        return False