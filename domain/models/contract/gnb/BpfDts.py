# coding=utf-8
'''
Created on 2019年7月25日

@author: 10207409
'''
from testlib5g.domain.DomainFactory import DomainFactory
from testlib5g.infrastructure.utility.log.Logger import logger
from testlib5g.app_service.basic.contract.ContractService import ContractService
from testlib5g.infrastructure.utility.CatchException import catchExceptionReturnTrueFalse


@catchExceptionReturnTrueFalse
def cover_sensitive_data(gnbAlias):
    temp = ContractService.execute_contract('MaintenanceContract.cover_sensitive_data_bpfdts', {'gnbAlias': gnbAlias}).return_string
    logger.debug("******cover_sensitive_data_bpfdts_log******\n{}\n******cover_sensitive_data_bpfdts_log******".format(temp))


@catchExceptionReturnTrueFalse
def insensitive_data(gnbAlias):
    temp = ContractService.execute_contract('MaintenanceContract.insensitive_data_bpfdts', {'gnbAlias': gnb<PERSON>lia<PERSON>}).return_string
    logger.debug("******insensitive_data_bpfdts_log******\n{}\n******insensitive_data_bpfdts_log******".format(temp))

