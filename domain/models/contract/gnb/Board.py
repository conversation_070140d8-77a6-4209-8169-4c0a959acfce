# coding=utf-8
'''
Created on 2019骞�7鏈�23鏃�

@author: 10207409
'''
import logging
import re
import time
from time import sleep
import traceback
import json

from service.action.contract.ContractService import ContractService
from infrastructure.device.foreground.ForeGround import ForeGround
from infrastructure.utils.Repeat import retries_during_time


def get_uptime_vbp(gnbAlias):
    result = ContractService().execute_contract('Comm.get_uptime_vbp', {'gnbAlias': gnbAlias}).return_string
    upmin = re.findall(r'up\s+(\d+).*min', result)
    if upmin:
        return False
    uphour = re.findall(r'up\s+(\d+):', result)
    if uphour:
        return int(uphour[0])
    return False


def get_uptime_vsw(gnbAlias):
    result = ContractService().execute_contract(
        'Comm.get_uptime_vsw', {'gnbAlias': gnbAlias}).return_string
    upmin = re.findall(r'up\s+(\d+).*min', result)
    if upmin:
        return False
    uphour = re.findall(r'up\s+(\d+):', result)
    if uphour:
        return int(uphour[0])
    return False


@append_to_thread_pool("CREATE_GNB", isDaemon=True, isJoined=True, isRaiseException=True)
def generate_gnb_version_info(gnbAlias):
    try:
        if not _generate_gnb_version_info(gnbAlias):
            _generate_gnb_version_info(gnbAlias, branch="cm")
        generate_gnb_board_name(gnbAlias)
    except:
        traceback.print_stack(limit=4)


def _generate_gnb_version_info(gnbAlias, branch="common"):
    if branch == "common":
        cmdResult = ContractService.execute_contract('{0}'.format('Comm.get_tar_real_version'), {'gnbAlias': gnbAlias},
                                                     throwException=False)
        m = _match_real_pkg_version(cmdResult.return_string)
    else:
        cmdResult = ContractService.execute_contract('{0}'.format('Comm.get_cm_tar_version'), {'gnbAlias': gnbAlias},
                                                     throwException=False)
        if re.search('Run Package Version.*(V5.*#V3)', cmdResult.return_string, re.M):
            m = re.search('Run Package Version.*(V\d+[\d\.a-zA-Z_]+)', cmdResult.return_string, re.M)
        else:
            m = re.search('Run Package Version\s*-+\s*((NRV|V|NR-V|BENRV|FNR_CMBasic_V|CPA_.*)\d+[\d\.a-zA-Z_\-]+)', cmdResult.return_string, re.M)
    if m:
        BackgroundLogger().info('version is {0}'.format(m.group(1)).replace('.lv', '_LV', 1))
        BackgroundLogger().info('$5G_version:{0}:5G_version$'.format(m.group(1)).replace('.lv', '_LV', 1))
        return m.group(1)
    return False


@catchExceptionReturnOriginValue
def _match_real_pkg_version(retString):
    mapDict = {'runStatus': '1', 'backupStatus': '0',
               'hotPatch': '6', 'coldPatch': '2', 'basicVersion': '1'}
    basicVersionLine = coldPatchVersionLine = ''
    hotPatchVersionLineList = []
    for line in retString.split('\n'):
        matchObj = re.match('\d+\. +\[0\](\d+)', line)
        if not matchObj or matchObj.group(1) != mapDict['runStatus']:
            continue
        pkgTypeObj = re.search('\[4\](\d+)', line)
        if not pkgTypeObj:
            continue
        if pkgTypeObj.group(1) == mapDict['hotPatch']:
            hotPatchVersionLineList.append(line)
        if pkgTypeObj.group(1) == mapDict['coldPatch']:
            coldPatchVersionLine = line
        if pkgTypeObj.group(1) == mapDict['basicVersion']:
            basicVersionLine = line
    for hotPatchVersionLine in hotPatchVersionLineList:
        mainVersion = re.search('\[2\]([\d\.a-zA-Z_\-]+)', hotPatchVersionLine).group(1)
        if _parse_version_run_status(mainVersion, [basicVersionLine, coldPatchVersionLine]) == mapDict['runStatus']:
            return re.search('\[1\]([\d\.a-zA-Z_\-]+)', hotPatchVersionLine)
    return re.search('\[1\]([\d\.a-zA-Z_\-]+)', coldPatchVersionLine or basicVersionLine)


def _parse_version_run_status(versionNo, versionInfoList):
    for versionInfo in versionInfoList:
        matchObj = re.search('\[1\]([\d\.a-zA-Z_\-]+)', versionInfo)
        if matchObj and matchObj.group(1) == versionNo:
            return re.search('\[0\](\d+)', versionInfo).group(1)


def generate_gnb_board_name(gnbAlias):
    try:
        _generate_gnb_board_name(gnbAlias)
    except:
        traceback.print_stack(limit=4)


def _generate_gnb_board_name(gnbAlias):
    boardInfo = DomainRepository().find(gnbAlias).vswBoard.hardWare.query_real_board_info()
    VSW, VBP = _match_board_real_name(boardInfo)
    BackgroundLogger().info('$VSW_type:{0}:VSW_type$, $VBP_type:{1}:VBP_type$'.format(VSW, VBP))
    _get_gnb_all_board_info(gnbAlias, boardInfo)
    return False


def _match_board_real_name(string):
    index = re.search('[0-9]+', re.search('\[[0-9]+\]acRealBoardName', string).group(0)).group(0)
    VSW, VBP = '', ''
    for line in string.split('\n'):
        VSW = _match_board_type(line, index, '(VSW|BPS|GCP)', VSW)
        VBP = _match_board_type(line, index, '(VBP|CCG|GBA)', VBP)
    return VSW[:-1], VBP[:-1]


def _match_board_type(string, index, boardType, ret):
    m = re.search(r'\[{0}\]{1}\S*'.format(index, boardType), string)
    if m:
        ret += m.group(0).split(']')[1] + ","
    return ret


def _get_gnb_all_board_info(gnbAlias, queryInfo):
    gnb = DomainRepository().find(gnbAlias)
    attrDict = _get_front_field_value_dict(queryInfo, ['ucSlotNo', 'acMoId', 'acBoardName', 'dwFuncSet', 'acRealBoardName'])
    boardInfoList = [{'subNetwork': gnb.subNetwork, 'meId': gnb.meId, 'functionMode': attrDict['dwFuncSet'][index],
                      'slotNo': attrDict['acMoId'][index] if attrDict['ucSlotNo'][index] == '1' and not attrDict['acMoId'][index].startswith('VSW') else attrDict['ucSlotNo'][index],
                      'boardType': attrDict['acBoardName'][index] if attrDict['acRealBoardName'][index] == '--' else attrDict['acRealBoardName'][index],
                      'boardName': attrDict['acBoardName'][index]}
                     for index in range(len(attrDict['acMoId']))]
    BackgroundLogger().info('$board_info:{0}:board_info$'.format(boardInfoList))


def _get_front_field_value_dict(queryInfo, fieldNameList):
    queryInfo = queryInfo.replace('\r', '').replace('\n', '')
    attrDict = {}
    for fieldName in fieldNameList:
        fieldId = re.search('\[(\d+)\]' + fieldName, queryInfo)
        if not fieldId:
            continue
        pattern = '\[\n?\s*' + fieldId.group(1) + '\n?\s*\]\n?\s*(\S+|\S+ \S+)[\s]{2,}'
        fieldResult = re.findall(pattern, queryInfo)
        if not fieldResult:
            continue
        if fieldResult[0].find('(*)') != -1:
            fieldResult[0] = fieldResult[0].replace('(*)', '')
        if fieldName == fieldResult[0]:
            fieldResult.remove(fieldName)
            attrDict[fieldName] = fieldResult
    return attrDict


def get_docker_images(gnbAlias, dockerListStr, durationSecs):
    dockerListStr = re.sub(r'[\"\'\s]+', '', dockerListStr)
    dockerList = dockerListStr.split(",")

    @retries_during_time(durationSecs, 5)
    def _get_docker_images():
        checkResult = ContractService.execute_contract('Comm.showAllDockersImages',
                                                       {'gnbAlias': gnbAlias}).return_string
        for docker in dockerList:
            if len(re.findall(docker, checkResult)) == 0:
                return False
        return True
    return _get_docker_images()


def get_docker_nums(gnbAlias):
    checkResult = ContractService.execute_contract('Comm.showAllDockersImages', {'gnbAlias': gnbAlias}).return_string
    result = re.findall(r"/\w+", checkResult)
    return len(result)


def get_vsw2_docker_nums(gnbAlias):
    checkResult = ContractService.execute_contract('Comm.showAllDockersImages_for_VSW2', {'gnbAlias': gnbAlias}).return_string
    result = re.findall(r"/\w+", checkResult)
    return len(result)


def check_docker_images_status(gnbAlias, durationSecs):
    @retries_during_time(durationSecs, 1)
    def _check_docker_images_status():
        checkResult = ContractService.execute_contract(
            'Comm.checkDockerImagesStatus', {'gnbAlias': gnbAlias}).return_string
        runningCount = re.findall(r'running', checkResult)
        dockerCount = re.findall(r'(?<=1-).*(?=-0|1)', checkResult)
        if len(runningCount) == len(dockerCount):
            return True
        else:
            return False
    return _check_docker_images_status()


def check_docker_image_status(gnbAlias, imageNameList):
    checkResult = ContractService.execute_contract(
        'Comm.checkDockerImagesStatus', {'gnbAlias': gnbAlias}).return_string
    counter = 0
    for i in imageNameList:
        if len(re.findall(r'1-{0}-.*running'.format(i), checkResult)) == 1:
            counter += 1
        else:
            break
    if counter == len(imageNameList):
        return True
    else:
        return False


def check_son_docker_image_status(gnbAlias, imageNameList):
    checkResult = ContractService.execute_contract(
        'Comm.checkDockerImagesStatus', {'gnbAlias': gnbAlias}).return_string
    for i in imageNameList:
        containerResult = re.findall(r'1-{0}-.*running.*16 \|\s+(\d)\s+\|\s+(\d)'.format(i), checkResult)
        print("{0} containerResult is {1} +++".format(i, containerResult))
        if not containerResult or (int(containerResult[0][0]) >= 1 and int(containerResult[0][1]) >= 1):
            logger.error(i + " ERROR!!!")
            return False
    return True


def ischeck_docker_image_restart(gnbAlias, imageNameList):
    checkResult = ContractService.execute_contract(
        'Comm.checkDockerImagesStatus', {'gnbAlias': gnbAlias}).return_string
    for i in imageNameList:
        containerResult = re.findall(r'1-{0}-.*running.*16 \|\s+(\d)\s+\|\s+(\d)'.format(i), checkResult)
        if not containerResult or int(containerResult[0][0]) >= 1 or int(containerResult[0][1]) >= 1:
            logger.error(i + "ERROR!!!")
            return False
        logger.info(i + ":succeeded")
    return True


def get_docker_image_restart_count(gnbAlias, imageNameList):
    checkResult = ContractService.execute_contract('Comm.checkDockerImagesStatus',
                                                   {'gnbAlias': gnbAlias}).return_string
    res = []
    for i in imageNameList:
        containerResult = re.findall(r'1-{0}-.*running.*16 \|\s+(\d)\s+\|\s+(\d)'.format(i), checkResult)
        res.append((i, int(containerResult[0][0]), int(containerResult[0][1])))
    return res


def check_all_docker_image_running_and_no_restart(gnbAlias):
    checkResult = ContractService.execute_contract('Comm.checkDockerImagesStatus', {'gnbAlias': gnbAlias}).return_string
    containerResult = re.findall(r'1-(.*?)-0\s+\|\s+(.*?)\s+\|.*16 \|\s+(\d)\s+\|\s+(\d)', checkResult)
    isNormal = True
    for item in containerResult:
        if 'running' not in item[1] or int(item[2]) >= 1 or int(item[3]) >= 1:
            logging.info(item)
            isNormal = False
    return isNormal


def prevent_omc_disconnect(gnbAlias):
    ContractService().execute_contract('Comm.prevent_omc_disconnect', {'gnbAlias': gnbAlias})
    return True


def execute_ul_ac_cmd(cellAlias):
    ContractService.execute_contract('CellBasic.send_ul_ac_cmd', {'cellAlias': cellAlias})
    returnString = ContractService.execute_contract('CellBasic.get_ul_ac_result', {'cellAlias': cellAlias}).return_string
    ulACResult = re.search(r"Ac usACResult\s+:\s+([0-9])", returnString).group(1)
    return ulACResult


def execute_dl_ac_cmd(cellAlias):
    ContractService.execute_contract('CellBasic.send_dl_ac_cmd', {'cellAlias': cellAlias})
    returnString = ContractService.execute_contract('CellBasic.get_dl_ac_result', {'cellAlias': cellAlias}).return_string
    dlACResult = re.search(r"Ac usACResult\s+:\s+([0-9])", returnString).group(1)
    return dlACResult


def show_kpitask_cpf(gnbAlias, boardType='VSW'):
    return ContractService().execute_contract('CellBasic.show_kpitask_cpf', {'gnbAlias': gnbAlias, 'boardType': boardType}).return_string


def get_container_id(gnbAlias, dockerName, boardType='VSW'):
    return get_all_container_id(gnbAlias, dockerName, boardType)[0]


def get_board_real_type(gnbAlias, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.get_board_real_type', {'gnbAlias': gnbAlias, 'boardType': boardType}).return_string
    boardTypeInfoListStr = re.split('\r\n', returnStr)[1]
    boardTypeInfoList = json.loads(boardTypeInfoListStr)
    boardTypeInfo = []
    for i in range(len(boardTypeInfoList)):
        boardTypei = boardTypeInfoList[i].get("name")
        boardTypeInfo.append(boardTypei)
    return boardTypeInfo


def check_container_exist(gnbAlias, dockerName, boardType='VSW'):
    findList = _get_container_info(gnbAlias, dockerName, boardType)
    containerId = []
    for findI in findList:
        if re.search('Up', findI):
            containerId.append(re.split(' ', findI)[0])
    return len(containerId) > 0


def get_all_container_id(gnbAlias, dockerName, boardType='VSW', dockerNameReg=None):
    findList = _get_container_info(gnbAlias, dockerName, boardType)
    containerId = []
    for findI in findList:
        if re.search('Up', findI) and re.search('(\s{0}|[0-9a-z]+ *[0-9\.:]+/{0}:)'.format(dockerNameReg or dockerName), findI, re.I):
            containerId.append(re.split(' ', findI)[0])
    return containerId


def get_all_container_ids(gnbAlias, dockerName, boardType='VSW'):
    containerIds = get_all_container_id(gnbAlias, dockerName + ':', boardType, dockerName)
    if not containerIds:
        containerIds = get_all_container_id(gnbAlias, dockerName + '-arm:', boardType, dockerName)
    return containerIds


def get_container_version(gnbAlias, dockerName, boardType='VSW'):
    findList = _get_container_info(gnbAlias, dockerName, boardType)
    for findI in findList:
        if re.search('Up', findI) and re.search('sctp-ng.*:([\w.-]+)'.format(dockerName), findI, re.I):
            dockerVersion = re.search('sctp-ng.*:([\w.-]+)'.format(dockerName), findI, re.I)
    return dockerVersion.group(1)


def get_uds_blue_name(gnbAlias, dockerName, boardTypeFlag, boardType='VSW'):
    blueMapName = ""
    blueMapName_snr = ""
    if boardTypeFlag == 'D':
        dockerName = 'uds-snr'
        returnStr = ContractService().execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'dockername': dockerName, 'boardType': boardType}).return_string
        findList = re.split('\s+', returnStr)
        blueMapName_str = re.split('/', findList[7])
        blueMapName_snr = blueMapName_str[1].replace(":", "@") + ".json"
        blueMapName = blueMapName_str[1].replace("-snr:", "@") + ".json"
    if boardTypeFlag == 'C':
        dockerName = 'uds'
        returnStr = ContractService().execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'dockername': dockerName, 'boardType': boardType}).return_string
        findList = re.split('\s+', returnStr)
        blueMapName_str = re.split('/', findList[7])
        blueMapName = blueMapName_str[1].replace(":", "@") + ".json"
        blueMapName_snr = blueMapName_str[1].replace(":", "-snr@") + ".json"
    return blueMapName, blueMapName_snr


def get_uds_blue_name_with_boardType(gnbAlias, boardTypeFlag, boardType='VSW'):
    if boardTypeFlag == 'E':
        dockerName = 'uds-lt-arm'
    elif boardTypeFlag == 'D':
        dockerName = 'uds-snr'
    elif boardTypeFlag == 'C':
        dockerName = 'uds'
    returnStr = ContractService().execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'dockername': dockerName, 'boardType': boardType}).return_string
    findList = re.split('\s+', returnStr)
    blueMapName_str = re.split('/', findList[7])
    blueMapName = blueMapName_str[1].replace(":", "@") + ".json"
    return blueMapName


def get_uc_blue_name(gnbAlias, dockerName, boardTypeFlag, boardType='VSW'):
    blueMapName = ""
    returnStr = ContractService().execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'dockername': dockerName, 'boardType': boardType}).return_string
    findList = re.split('\s+', returnStr)
    blueMapName_str = re.split('/', findList[7])
    blueMapName = blueMapName_str[1].replace(":", "@") + ".json"
    return blueMapName


def get_container_index_in_2_vsw(gnbAlias, dockerName, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'dockername': dockerName.lower(), 'boardType': boardType}).return_string
    containerId = []
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId.append(re.split(' ', findI)[0])
    if not containerId:
        returnStr = ContractService().execute_contract('Comm.get_container_id_in_vsw2', {'gnbAlias': gnbAlias, 'dockername': dockerName.lower(), 'boardType': boardType}).return_string
        findList = re.findall('.*\n', returnStr)
        for findI in findList:
            if re.search('Up', findI):
                containerId.append(re.split(' ', findI)[0])
    if not containerId:
        return False
    dockerId = containerId[0]
    return dockerId


def get_container_index_in_specified_vsw(gnbAlias, vswSlotId, dockerName, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.get_container_index_in_specified_vsw', {'gnbAlias': gnbAlias, 'dockername': dockerName.lower(), 'vswSlotId': vswSlotId, 'boardType': boardType}).return_string
    containerId = []
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId.append(re.split(' ', findI)[0])
    if not containerId:
        return False
    dockerId = containerId[0]
    return dockerId


def restart_container_in_vsw(gnbAlias, dockerName, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'dockername': dockerName.lower(), 'boardType': boardType}).return_string
    containerId = []
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId.append(re.split(' ', findI)[0])
    if not containerId:
        returnStr = ContractService().execute_contract('Comm.get_container_id_in_vsw2', {'gnbAlias': gnbAlias, 'dockername': dockerName.lower(), 'boardType': boardType}).return_string
        containerId = []
        findList = re.findall('.*\n', returnStr)
        for findI in findList:
            if re.search('Up', findI):
                containerId.append(re.split(' ', findI)[0])
        dockerId = containerId[0]
        returnStr = ContractService().execute_contract('Comm.restart_container_in_vsw2', {'gnbAlias': gnbAlias, 'dockername': dockerId, 'boardType': boardType}).return_string
    else:
        dockerId = containerId[0]
        returnStr = ContractService().execute_contract('Comm.restart_container', {'gnbAlias': gnbAlias, 'dockername': dockerId, 'boardType': boardType}).return_string
    return returnStr


def restart_container_in_vsw2(gnbAlias, dockerName, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.get_container_id_in_vsw2', {'gnbAlias': gnbAlias, 'dockername': dockerName, 'boardType': boardType}).return_string
    containerId = []
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId.append(re.split(' ', findI)[0])
    dockerId = containerId[0]
    returnStr = ContractService().execute_contract('Comm.restart_container_in_vsw2', {'gnbAlias': gnbAlias, 'dockername': dockerId, 'boardType': boardType}).return_string
    return returnStr


def set_vbp_reboot(cellAlias):
    ContractService().execute_contract('CellBasic.set_vbp_reboot', {'cellAlias': cellAlias})
    sleep(1)
    return True


def set_vsw_reboot(cellAlias):
    ContractService().execute_contract('CellBasic.set_vsw_reboot', {'cellAlias': cellAlias})
    sleep(1)
    return True


def pad_to_shell(cellAlias, dockerAlias, commandAlias):
    return ContractService.execute_contract('CellBasic.pad_to_shell',
                                            {"cellAlias": cellAlias, "dockerAlias": dockerAlias, "commandAlias": commandAlias}, 2)


def query_affcache_info(gnbAlias, dockerName):
    returnStr = ContractService().execute_contract('Comm.query_affcache_info', {'gnbAlias': gnbAlias, 'dockerName': dockerName}).return_string
    sessionsSize = re.findall(r"sessionsSize:\d+", returnStr)
    linksSize = re.findall(r"linksSize:\d+", returnStr)
    return sessionsSize, linksSize


def open_ca_ue_capability_switch(gnbAlias):
    ContractService().execute_contract('Comm.open_ca_ue_capability_switch', {'gnbAlias': gnbAlias})


def get_vsw_eid_info(gnbAlias):
    returnStr = ContractService().execute_contract('Vsw.get_vsw_eid_addr', {'gnbAlias': gnbAlias}).return_string
    eidAddr = re.findall(r"\((\w+)\)", returnStr)[0]
    returnStr = ContractService().execute_contract('Vsw.get_vsw_eid_info', {'gnbAlias': gnbAlias, 'eidAddr': eidAddr}).return_string
    return re.findall(r"\s+(\d{12})", returnStr)[0]


def query_abnormal_state_container_name(gnbAlias):
    gnb = DomainRepository().find(gnbAlias)
    cmd = 'kubectl get pod -n operator' if gnb.isVranPlat else 'lubanctl pod -a'
    returnStr = ContractService().execute_contract('Comm.query_all_container_state', {'gnbAlias': gnbAlias, 'cmd': cmd}).return_string
    if gnb.isVranPlat:
        resultList = re.findall('(\S+)\s+(\d+/\d+)\s+(\S+)\s+(\d+)', returnStr)
        return [info[0] for info in resultList if int(info[3]) != 0]
    resultList = re.findall('\n\|\s+(\S+)\s+\|.+running.+\|.+\|(.+)\|.+\|.+\|\s+(\d+)\s+\|\s+(\d+)\s+', returnStr)
    return [info[0] for info in resultList if int(info[2]) > 0 or int(info[3]) > 0]


def set_vsw_ip(gnbAlias):
    vswAlias = EnvPara().get_link_devices(gnbAlias, 'VSW')[0]
    envId = EnvPara().get_attribute(vswAlias, 'envId')
    ContractService().execute_contract('Vsw.set_vsw_ip_by_envId', {'gnbAlias': gnbAlias, 'envId': envId}, isRelogin=False)


def copy_file_vsw(gnbAlias, containerName, filefullpath, vswfullpath):
    ContractService().execute_contract('Vsw.copy_file_from_docker_to_vsw', {'gnbAlias': gnbAlias, 'containerName': containerName, 'filefullpath': filefullpath, 'vswfullpath': vswfullpath})


def get_container_all_id(gnbAlias, dockerName, boardType='VSW'):
    findList = _get_container_info(gnbAlias, dockerName, boardType)
    containerId = []
    for findI in findList:
        if re.search('Up' + '.*' + str(dockerName) + '-1-' + str(dockerName), findI):
            containerId.append(re.split(' ', findI)[0])
    return containerId


def get_program_all_pid_in_board(gnbAlias, programname, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.get_process_id_in_board', {'gnbAlias': gnbAlias, 'programname': programname, 'boardType': boardType}).return_string
    processAllId = re.findall('(\d+) itran', returnStr)
    if processAllId:
        return processAllId
    else:
        logging.info('The program {} has no any process run in board'.format(programname))
        return False

# boot自动化新增


def get_process_id_in_bandboard(vbpAlias, programname, regularExp, expectInfo):
    gnbAlias = DomainRepository().find(vbpAlias).gnb.alias
    time.sleep(10)
    returnStr = ContractService().execute_contract('Comm.get_process_id_in_bandboard', {'gnbAlias': vbpAlias, 'programname': programname, 'expectInfo': expectInfo}).return_string
    #processId = re.findall('(\d+) root', returnStr)
    processId = re.findall(regularExp, returnStr)
    if processId:
        return processId
    else:
        logging.info('The program {} has no any process run in board'.format(programname))
        return False


def _get_container_info(gnbAlias, dockerName, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'dockername': dockerName.lower(), 'boardType': boardType}).return_string
    return re.findall('.*\n', returnStr)


def get_container_pid(gnbAlias, containerName, containerIndex=-1):
    containerId = get_container_all_id(gnbAlias, containerName)[int(containerIndex)]
    returnStr = ContractService().execute_contract('Comm.get_container_pid', {'gnbAlias': gnbAlias, 'containerId': containerId}).return_string
    for line in returnStr.splitlines():
        if 'huc' in line:
            return re.split('\s*', line.strip())[0]


def execute_gnb_cmd(gnbAlias, cmd, expected, timeout=20, regularExp=None):
    cmdResultStr = ContractService().execute_contract('Comm.execute_gnb_cmd', {'gnbAlias': gnbAlias, 'cmd': cmd, 'expected': expected, 'timeout': timeout}).return_string
    if regularExp:
        return re.findall(regularExp, cmdResultStr)
    return cmdResultStr


def get_stream_id(gnbAlias, boardtype="VSW"):
    rltString = ContractService.execute_contract('Comm.show_ngm_asso_info', {'gnbAlias': gnbAlias, "boardtype": boardtype}, 2).return_string
    streamId = re.search(r"treamId\s{0,1}=\s{0,1}(\d+?),", rltString)
    streamId = streamId.group(1)
    print streamId
    return streamId


def create_white_list(gnbAlias, dockerImagesID, boardType='VSW'):
    ContractService().execute_contract('Comm.create_white_list', {'gnbAlias': gnbAlias, 'dockerImagesID': dockerImagesID, 'boardType': boardType})
    sleep(1)
    return True


def query_board_temperature(boardAlias):
    boardSlot = EnvPara().get_attribute(boardAlias, 'slot')
    gnbAlias = _get_gnb_alias_by_board_alias(boardAlias)
    envTempInfo = ContractService().execute_contract('Comm.exec_bum_container_cmd', {'gnbAlias': gnbAlias, 'cmd': 'AirInTemp ' + str(boardSlot)}).return_string
    matchObj = re.search('Fan Air In Temp = ([\d\.]+)', envTempInfo)
    return matchObj and matchObj.group(1) or -1


def _get_gnb_alias_by_board_alias(boardAlias):
    if EnvPara().get_attribute(boardAlias, "type") == "FIBER":
        boardAlias = EnvPara().get_parent_alias(boardAlias)
    return EnvPara().get_link_devices(boardAlias, 'GNODEB')[0]


def query_board_power_consumption(boardAlias):
    boardSlot = EnvPara().get_attribute(boardAlias, 'slot')
    gnbAlias = _get_gnb_alias_by_board_alias(boardAlias)
    powerInfo = ContractService().execute_contract('Comm.login_vsw_and_exe_hwm_cmd', {'gnbAlias': gnbAlias, 'cmd': 'PowerOfBoard ' + str(boardSlot)}).return_string
    matchObj = re.search('Power = ([\d\.]+)', powerInfo)
    return matchObj and matchObj.group(1) or -1


def get_basic_band_min_version_name(gnbAlias, vbpAlias):
    slotNo = EnvPara().get_attribute(vbpAlias, 'slot')
    logging.info('Board slotNo : {}'.format(slotNo))
    vmpLog = ContractService.execute_contract('{0}'.format('Comm.get_vmpVersionReport_log'), {'gnbAlias': gnbAlias}).return_string
    vmpLogBak = ContractService.execute_contract('{0}'.format('Comm.get_vmpVersionReport_log_bak'), {'gnbAlias': gnbAlias}).return_string
    vmpVersionSwid = _match_band_board_version_swid(vmpLog + vmpLogBak, slotNo)
    cmdResult = ContractService.execute_contract('{0}'.format('Comm.get_versionConfig_xml'), {'gnbAlias': gnbAlias})
    logging.info('Board min-ver swid: {}:'.format(vmpVersionSwid))
    return _match_band_board_version_name(cmdResult.return_string, vmpVersionSwid)


def _match_band_board_version_swid(cmdResultStr, slotNo):
    vmpVersionSwid = []
    minVers = ['DSP', 'RMIOS', 'SO']
    for minVer in minVers:
        verSwid = re.search('Pos:\[\n?\s*1:1:' + str(slotNo) + '\n?\s*\],\s*SwClass:NR.*?\s*{}.*?\s*Swid\:(\d+)'.format(minVer), cmdResultStr).group(1)
        vmpVersionSwid.append(verSwid)
    return vmpVersionSwid


def _match_band_board_version_name(cmdResultStr, vmpVersionSwid):
    verNameList = []
    for swid in vmpVersionSwid:
        versionName = re.search('\|-\s\d+\.\s*\[\n?\s*0\n?\s*\]\n?\s*(\S+|\S+ \S+)[\s]{2,}\s*.*?\[\n?\s*3\n?\s*\]' + swid, cmdResultStr).group(1)
        verNameList.append(versionName.split('.')[-1])
    return ','.join(verNameList)


def reboot_vbp_and_exec_cmd(vbpAlias, cmd):
    if not ContractService.execute_contract('Comm.reboot_VBP', {'gnbAlias': vbpAlias}).result:
        return False
    gnbAlias = DomainRepository().find(vbpAlias).gnb.alias
    ip = DeviceRepository().find(vbpAlias)._hardware.ip
    time.sleep(60)  # boot自动化修改时延为60s
    result = ContractService.execute_contract('Comm.execute_gnb_cmd',
                                              {'gnbAlias': gnbAlias, 'cmd': 'ping {}'.format(ip), 'expected': 'ms', 'timeout': 180}).result
    return result and ContractService.execute_contract('Comm.login_vbp_and_exe_cmd_onBoard', {'gnbAlias': vbpAlias, 'cmd': cmd, 'expectInfo': '#'}).result

# boot自动化新增


def check_vbp_mcs0_status(vbpAlias):
    gnbAlias = DomainRepository().find(vbpAlias).gnb.alias
    mcsCheckResult = ContractService.execute_contract('Comm.check_vbp_mcs0_status', {'gnbAlias': vbpAlias}).return_string
    print ("BspShowSlaveCoreInfo返回结果", mcsCheckResult)
    ret = re.findall(r'0x5a5a5a5a', mcsCheckResult)
    print ("从核加载结果", ret)
    if len(ret) > 0:
        return True

# boot自动化新增


def login_vbp_and_exe_cmd_onBoard(vbpAlias, cmd, regularExp, expectInfo):
    gnbAlias = DomainRepository().find(vbpAlias).gnb.alias
    time.sleep(10)
    cmdResult = ContractService.execute_contract('Comm.login_vbp_and_exe_cmd_onBoard', {'gnbAlias': vbpAlias, 'cmd': cmd, 'expectInfo': expectInfo}).return_string
    cmdResult = ReturnInfoHandler.del_color_info_from_info(cmdResult)
    if regularExp:
        cmdResult = re.findall(regularExp, cmdResult)
        return cmdResult
    return cmdResult

# boot自动化新增


def login_vbpdir_and_exe_cmd_onBoard(vbpAlias, vbpDir, cmd, regularExp, expectInfo):
    gnbAlias = DomainRepository().find(vbpAlias).gnb.alias
    time.sleep(10)
    cmdResult = ContractService.execute_contract('Comm.login_vbpdir_and_exe_cmd_onBoard', {'gnbAlias': vbpAlias, 'vbpDir': vbpDir, 'cmd': cmd, 'expectInfo': expectInfo}).return_string
    cmdResult = ReturnInfoHandler.del_color_info_from_info(cmdResult)
    if regularExp:
        cmdResult = re.findall(regularExp, cmdResult)
        return cmdResult
    return cmdResult

# boot自动化新增


def exec_cmd_in_docker_dir(gnbAlias, containerId, dockerName, cmd, dockerDir, regularExp, expectInfo):
    cmdResult = ContractService.execute_contract('Comm.exec_cmd_in_docker_dir', {'gnbAlias': gnbAlias, 'containerId': containerId, 'dockerName': dockerName, 'cmd': cmd, 'dockerDir': dockerDir, 'expectInfo': expectInfo}).return_string
    if regularExp:
        cmdResult = re.findall(regularExp, cmdResult)
        return cmdResult
    return cmdResult


def get_basic_aau_min_version_name(gnbAlias, aauAlias):
    cmdResult = ContractService().execute_contract('Rum.get_rum_dbtr', {'gnbAlias': gnbAlias, 'cmdAlisa': 'dbtr 1025'})
    wRackNo = _match_aau_board_wrack_no(cmdResult.return_string, EnvPara().get_attribute(aauAlias, 'moId'))
    logging.info('wRackNo: {}'.format(wRackNo))
    vmpLog = ContractService.execute_contract('{0}'.format('Comm.get_vmpVersionReport_log'), {'gnbAlias': gnbAlias}).return_string
    vmpLogBak = ContractService.execute_contract('{0}'.format('Comm.get_vmpVersionReport_log_bak'), {'gnbAlias': gnbAlias}).return_string
    vmpVersionSwid = _match_aau_board_version_swid(vmpLog + vmpLogBak, wRackNo, _match_gnb_vmp(gnbAlias))
    verInfos = _match_aau_board_version_class(vmpLog + vmpLogBak, wRackNo, vmpVersionSwid)
    cmdResult = ContractService.execute_contract('{0}'.format('Comm.cat_versionConfig_xml'), {'gnbAlias': gnbAlias})
    verInfos = _match_aau_board_is_irsoft(cmdResult.return_string, verInfos)
    logging.info(verInfos)
    return verInfos


def _match_aau_board_wrack_no(cmdResultStr, moId):
    return re.search('\d\s*\.\s*\[\n?\s*0\n?\s*\]\n?\s*(\d+)[\s]{2,}\s*.*?\[\n?\s*4\n?\s*\]' + moId, cmdResultStr).group(1)


def _match_aau_board_version_swid(cmdResultStr, wRackNo, realVer):
    verSwid = re.findall('Pos:\[\n?\s*' + str(wRackNo) + ':1:1\n?\s*\],.*?\s*SwVersion\:\[(\S+)\].*?\s*Swid\:(\d+)', cmdResultStr, re.I)
    if not verSwid:
        verSwid = re.findall('Pos:\[\n?\s*' + str(wRackNo) + ':1:1\n?\s*\],.*?\s*Swid\:(\d+).*?\s*SwVersion\:\[(\S+)\]', cmdResultStr, re.I)
        return list(set([ver[0] for ver in verSwid if realVer not in ver[1]]))
    return list(set([ver[1] for ver in verSwid if realVer not in ver[0]]))


def _match_aau_board_version_class(cmdResultStr, wRackNo, verSwids):
    verInfos = []
    for verSwid in verSwids:
        dictVer = {}
        swClass = re.search('Pos:\[\n?\s*' + str(wRackNo) + ':1:1\n?\s*\],\s*SwClass\:(\S+ \S+),.*?\s*Swid\:' + verSwid, cmdResultStr, re.I)
        if not swClass:
            swClass = re.search('Pos:\[\n?\s*' + str(wRackNo) + ':1:1\n?\s*\],\s*SWID\:{}.*?\s*SoftClass\:\s?(\d+),'.format(verSwid), cmdResultStr, re.I)
        dictVer.setdefault('swId', verSwid)
        dictVer.setdefault('swClass', swClass.group(1))
        verInfos.append(dictVer)
    return verInfos


def _match_aau_board_is_irsoft(cmdResultStr, verInfos):
    for verInfo in verInfos:
        isIrSoft = re.search('<SoftInfo\s*swid="{}"\s*.*?\s*IsIrSoft="(\d+)"'.format(verInfo.get('swId')), cmdResultStr).group(1)
        verInfo.setdefault('isIrSoft', isIrSoft)
    return verInfos


def _match_gnb_vmp(gnbAlias):
    cmdResult = ContractService.execute_contract('{0}'.format('Comm.get_tar_real_version'), {'gnbAlias': gnbAlias},
                                                 throwException=False)
    m = _match_real_pkg_version(cmdResult.return_string)
    if m:
        BackgroundLogger().info('version is {0}'.format(m.group(1)))
        return m.group(1)
    return ''


def generate_gnb_version_info_temp(gnbAlias):
    try:
        version = _generate_gnb_version_info(gnbAlias)
        if not version:
            version = _generate_gnb_version_info(gnbAlias, branch="cm")
        return version
    except:
        traceback.print_stack(limit=4)


def bsp_set_boot_start_step(gnbAlias, aauAlias):
    cmdResult = ContractService().execute_contract('Comm.login_aau_and_exe_cmd_with_ushell', {'gnbAlias': gnbAlias,
                                                                                              'aauAlias': aauAlias, 'cmdAlisa': 'BspSetBootStartStep 0'})
    logging.info(cmdResult)
    return cmdResult


def confrim_all_container_normal(gnbAlias):
    result = ContractService().execute_contract('Vsw.query_all_container_status', {'gnbAlias': gnbAlias}).return_string
    lines = result.strip().split('\n')
    state_pattern = r'32m(\w+)'
    for line in lines:
        match = re.search(state_pattern, line)
        if match:
            state = match.group(1)
            if state != 'running':
                return False
    return True


def confrim_vbp_software_status(gnbAlias):
    retry = 5
    while retry > 0:
        versionCheckResult = ContractService().execute_contract('HfCellBasic.show_vbp_version_status', {'cellAlias': gnbAlias}).return_string
        ret = re.findall(r'done', versionCheckResult)
        if not ret or len(ret) < 12:
            retry -= 1
            continue
        return True
    return False


def query_exist_alarm_info(gnbAlias):
    alarmCodeList = []
    alarmResult = ContractService().execute_contract('OamCI.query_alarm_exists', {'cellAlias': gnbAlias}).return_string
    alarmInfos = re.findall(r'{"almCode.*', alarmResult)
    for alarmInfo in alarmInfos:
        alarmCodeList.append(json.loads(alarmInfo).get("almCode-Cause").split("-")[0])
    return alarmCodeList
