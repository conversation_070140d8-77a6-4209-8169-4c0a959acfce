# coding=utf-8
'''
Created on 2019年7月30日

@author: 10207409
'''
import re
from testlib5g.app_service.basic.contract.ContractService import ContractService


def get_luc_msg4_send_time(gnbAlias):
    cmdResult = ContractService().execute_contract('GnbPerformanceBasic.luc_msg4_send', {'gnbAlias': gnbAlias})
    recvTime = _get_luc_msg4_sendtime(cmdResult.return_string)
    return recvTime


def _get_luc_msg4_sendtime(src_str):
    proc_src = delete_container_id(src_str)
    blockBgn = "--007.PPS_DlRrcMsgTransfer(msg4)"
    blockEnd = "--008.MCC_UeCtxtSetupReq"
    lineBgn = "******003. OssSender.cpp:60"
    lineEnd = "******004. DlccchTransferRole.cpp:85"
    bgnTime = get_begin_time(blockBgn, blockEnd, lineBgn, lineEnd, proc_src)
    return long(bgnTime)


def check_luc_ues_openlog(gnbAlias):
    return ContractService().execute_contract('GnbPerformanceBasic.check_luc_ues_openlog', {'gnbAlias': gnbAlias}).return_string


def delete_container_id(src_str):
    regStr = "\n\[\d+\]\n"
    return re.sub(regStr, "", src_str)


def get_begin_time(blockBgn_str, blockEnd_str, lineBgn_str, lineEnd_str, src_str):
    blockStr = get_block_str(blockBgn_str, blockEnd_str, src_str)
    lineStr = get_block_str(lineBgn_str, lineEnd_str, blockStr)
    bgnStr = "bgnTime["
    endStr = "]  endTime"
    return get_str_except(bgnStr, endStr, lineStr)


def get_block_str(start_str, end_str, src_str):
    start = src_str.find(start_str)
    if start >= 0:
        end = src_str.find(end_str, start)
        if end >= 0:
            return src_str[start:end].strip()
    return ''


def get_str_except(start_str, end_str, src_str):
    start = src_str.find(start_str)
    if start >= 0:
        start += len(start_str)
        end = src_str.find(end_str, start)
        if end >= 0:
            return src_str[start:end].strip()
    return "0"
