# coding=utf-8
'''
Created on 2019年7月25日

@author: 10207409
'''
import re
import os
import shutil
import time
import traceback

from testlib5g.domain.DomainRepository import DomainRepository
from testlib5g.infrastructure.utility.envpara.EnvPara import EnvPara
from testlib5g.app_service.basic.contract.ContractService import ContractService
from testlib5g.infrastructure.utility.SimDecorator import SIM_ENV_IP


def try_to_copy_config_to_history(gnbAlias, configFile):
    try:
        if EnvPara().get_attribute(gnbAlias, "nrGnbIp") in SIM_ENV_IP:
            return
        gnbId = EnvPara().get_attribute(gnbAlias, "gnodebId")
        verInfo = ContractService.execute_contract('Comm.get_tar_version',
                                                   {'gnbAlias': gnbAlias}).return_string
        defVerRep = re.findall('Run\s*Package\s*Version\s*\-*\s*([vV][0-9\.A-Z_\-]+)', verInfo)
        defVer = time.strftime('%Y-%m-%d-%H-%M-%S', time.localtime(time.time())) if defVerRep == [] else str(defVerRep[0])
        caseSceneObject = DomainRepository().find('CaseScene')
        caseScene = "get_scene_fail"
        if caseSceneObject:
            caseSceneObject.save_case_scene(False)
            caseScene = str(caseSceneObject.case_scene)
        dstConfigPath = configFile[:configFile.rindex('\\')] + "\\bakCfgHistory"
        dstConfigFileName = gnbId + "_" + defVer + "_" + caseScene + os.path.splitext(configFile)[-1]
        if not os.path.exists(dstConfigPath):
            os.mkdir(dstConfigPath)
        shutil.copyfile(configFile, os.path.join(dstConfigPath, dstConfigFileName))
    except:
        traceback.print_exc()


def set_vsw_remote_update_swtich(gnbAlias, onOff):
    cmd = 'OAM_VMP_VerReqDebugModeDisable' if int(onOff) == 1 else 'OAM_VMP_VerReqDebugModeEnable'
    ContractService().execute_contract('Comm.set_vsw_remote_update_swtich', {'gnbAlias': gnbAlias, 'cmd': cmd})
