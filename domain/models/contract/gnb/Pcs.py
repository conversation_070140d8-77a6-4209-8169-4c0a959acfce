# coding=utf-8
'''
Created on 2019年12月02日

@author: 10255283
'''
import re
from testlib5g.app_service.basic.contract.ContractService import ContractService

def get_gtpu_pcs_info(cellAlias):
    gtpuInfo = ContractService.execute_contract('CellBasic.get_pcs_info', {'cellAlias': cellAlias}, 2)
    if re.search(r'dwPubUpmRcvEchoRspProc_ProcSucc\]: \d+', gtpuInfo.return_string):
        return re.search(r'dwPubUpmRcvEchoRspProc_ProcSucc\]: \d+', gtpuInfo.return_string).group().strip().split(":")[-1]
    return None

def check_pcs_no_zombie_process(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.get_pcs_process_info', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    pcsInfoList = re.findall('.*\n', pcsInfo)
    for pcsInfo in pcsInfoList:
        if re.search(r'^(z|Z).*', pcsInfo):
            return False
    return True

def check_pcs_op_is_power_on(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_pcs_op_power_info', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    for pcsInfo in pcsInfoList:
        m = re.search(r'^\d+\s+pcs.*0x\w+\s+0x\w+\s+(\d)\s+0x\w+.*', pcsInfo)
        if m and m.group(1) not in ['1', '2']:
            return False
    return True

def change_pcs_go_version(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    ContractService.execute_contract('Comm.change_pcsgo_version', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    return None

def check_generate_registered_file(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.generate_registered_file', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    pcsInfoList = re.findall('.*\n', pcsInfo)
    for pcsInfo in pcsInfoList:
        if re.search(r'^./ordinaryuserhome/testA.txt', pcsInfo):
            return True
    return False

def kill_sigterm_pcs_go(gnbAlias):
    ContractService.execute_contract('Comm.kill_sigterm_pcsgo', {'gnbAlias': gnbAlias})
    return None

def restart_pcs_container(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    ContractService.execute_contract('Comm.restart_pcs', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    return None

def kill_sigterm_pcs(gnbAlias):
    ContractService.execute_contract('Comm.kill_sigterm_pcs', {'gnbAlias': gnbAlias})
    return None

def kill_sigterm_run(gnbAlias):
    ContractService.execute_contract('Comm.kill_sigterm_run', {'gnbAlias': gnbAlias})
    return None

def remove_registered_file(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.rm_registered_file', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    if pcsInfo == 'find ./ordinaryuserhome/ -name testA.txt\r\n/ # ':
        return True
    return False

def hangup_pcs_go(gnbAlias):
    ContractService.execute_contract('Comm.hangup_pcs_go_proc', {'gnbAlias': gnbAlias})
    return None
	
def remove_bbx_file(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.rm_bbx_file', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    if pcsInfo == 'find ./oss/oss/pcs-1-pcs-0-rcm  -name Bbx\r\n/ # ':
        return True
    return False

def check_generate_bbx_file(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.check_bbx_file', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    pcsInfoList = re.findall('.*\n', pcsInfo)
    for pcsInfo in pcsInfoList:
        if re.search(r'^./oss/oss/pcs-1-pcs-0-rcm/Bbx/pcs_go_01.bbx.gz', pcsInfo):
            return True
    return False

def check_generate_infosnap_file(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.check_infosnap_file', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    pcsInfoList = re.findall('.*\n', pcsInfo)
    for pcsInfo in pcsInfoList:
        if re.search(r'^./oss/oss/pcs-1-pcs-0-rcm/Bbx/pcs_go_01.infosnap.gz', pcsInfo):
            return True
    return False

def kill_44_pcs_go(gnbAlias):
    ContractService.execute_contract('Comm.kill_44_pcsgo', {'gnbAlias': gnbAlias})
    return None

def check_generate_bbx_infosnap_file(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.check_bbx_infosnap_file', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    pcsInfoList = re.findall('.*\n', pcsInfo)
    for pcsInfo in pcsInfoList:
        if re.search(r'./oss/oss/pcs-1-pcs-0-rcm/Bbx/pcs_go_06.infosnap.gz', pcsInfo):
            return True
    return False
	
def enable_get_cpu(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.enable_get_cpu_usage', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    if pcsInfoList[2] == 'excel fun:EnableGet successfully\r\n' :
        return True
    return False

def disable_get_cpu(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.disable_get_cpu_usage', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    if pcsInfoList[2] == 'excel fun:DisableGet successfully\r\n' :
        return True
    return False

def get_cpu_num(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_cpu_number', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    cpunum = re.findall('\d+',pcsInfoList[2])
    return cpunum[0]

def get_cpu_usage_pcs(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_cpu_usage_pcs_go', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    cpusage = re.findall('\w*(0.[0-9][0-9])[0-9]+\w*', pcsInfoList[2])
    return cpusage[0]

def stub_in_pcs_go(gnbAlias,cmd,excepted):
    powerInfo = ContractService.execute_contract('Comm.stub_in_pcs_go_contract', {'gnbAlias': gnbAlias, 'cmd':cmd, 'excepted':excepted}).return_string
    if not powerInfo:
        return False
    return True

def confirm_expansion_ant_switch_to_specified_sector(gnbAlias, sectorId):
    result = ContractService.execute_contract('Comm.confirm_expansion_ant_switch_to_specified_sector', {'gnbAlias': gnbAlias}).return_string
    if not re.search('portId:{0}, is2T:1'.format(sectorId), result):
        raise Exception('switch sector ant to {0} failed'.format(sectorId))

def top_get_cpu_usage(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.top_get_cpuusage', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    pcsInfoList1 = pcsInfoList[2].split()
    cpuusage1 = float(pcsInfoList1[1].strip("%"))
    cpuusage = cpuusage1/100.0
    return cpuusage

def get_cpu_usage_pcs_disable(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_cpu_usage_pcs_dis', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    if pcsInfoList[2] == 'excel fun:GetCpuUseRate successfullyvalue =0Get CPU rate disabled\r\n' :
        return True
    return False

def get_pcs_container_cpu_usage(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_pcs_con_cpu_usage', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    cpusage = re.findall('\w*(0.[0-9][0-9][0-9])+\w*', pcsInfoList[2])
    return cpusage[0]

def get_pcs_cpu_usage_linux(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.get_pcs_cpu_usage_linux', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    pcsInfoList = re.findall('\d+.\d+%', pcsInfo)
    pcscpu = float(pcsInfoList[0].strip("%"))
    pcscpuusage = pcscpu/100
    return pcscpuusage

def get_single_core_cpu0_usage(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_single_core_cpu0_use', {'gnbAlias': gnbAlias}).return_string
    returnStr = re.findall(r"CPU0:(.*)", returnStr)
    str1 = returnStr[0].split()
    str2 = float(str1[6].strip("%"))
    cpuusage = 1 - str2/100.0
    return cpuusage

def get_single_core_cpu0_usage_pcs(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_single_core_cpu0_usage_pcs', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    cpusage = re.findall('\w*(0.[0-9][0-9][0-9])+\w*', pcsInfoList[2])
    return cpusage[0]

def get_single_core_cpu15_usage_pcs(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_single_core_cpu15_usage_pcs', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    cpusage = re.findall('\d+', pcsInfoList[2])
    return cpusage[0]

def check_pcs_op_is_power_on_normal_user(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.get_pcs_op_power_info_normal_user', {'gnbAlias': gnbAlias}).return_string
    pcsInfoList = re.findall('.*\n', powerInfo)
    for pcsInfo in pcsInfoList:
        m = re.search(r'^\d+\s+pcs.*0x\w+\s+0x\w+\s+(\d)\s+0x\w+.*', pcsInfo)
        if m and m.group(1) not in ['1', '2']:
            return False
    return True

def docker_exec_pcs_normal_user(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.docker_exec_pcs_normal_user', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    if re.search('(docker exec -it).*', pcsInfo).group(1) :
        return True
    return False

def docker_exec_pcs_root_user(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    pcsInfo = ContractService.execute_contract('Comm.docker_exec_pcs_root_user', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    if re.search('(docker exec -it -u root).*', pcsInfo).group(1)  :
        return True
    return False

def remove_history_file(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    returnStr = ContractService.execute_contract('Comm.remove_history_file', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    result = re.search('.*(#)', returnStr).group(1)
    if result == '#':
        return True
    return False

def check_history_normal_user(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    returnStr = ContractService.execute_contract('Comm.check_history_normal_user', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    if re.search('.*(PCB).*' ,returnStr):
        return True
    return False

def check_history_root_user(gnbAlias):
    returnStr = ContractService.execute_contract('Comm.get_container_id', {'gnbAlias': gnbAlias, 'boardType':'VSW', 'dockername':'pcs-1-pcs-0-rcm'}).return_string
    findList = re.findall('.*\n', returnStr)
    for findI in findList:
        if re.search('Up', findI):
            containerId = re.split(' ', findI)[0]
    returnStr = ContractService.execute_contract('Comm.check_history_root_user', {'gnbAlias': gnbAlias, 'containerId':containerId}).return_string
    if re.search('.*(PCB).*' ,returnStr):
        return True
    return False

def exit_normal_user(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.exit_normal_user', {'gnbAlias': gnbAlias}).return_string
    result = re.search('.*(start ushell failed user exit)', powerInfo).group(1)
    # result = re.search('.*(start ushell failed user exit)', powerInfo)
    if result :
        return True
    return False

def exit_root_user(gnbAlias):
    powerInfo = ContractService.execute_contract('Comm.exit_root_user', {'gnbAlias': gnbAlias}).return_string
    result = re.search('.*(start ushell failed user exit)', powerInfo).group(1)
    if result :
        return True
    return False
