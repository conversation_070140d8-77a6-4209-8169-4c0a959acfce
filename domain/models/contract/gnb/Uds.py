# coding=utf-8
'''
Created on 2019年9月10日

@author: 10207409
'''
import re
from threading import Thread
import time

from testlib5g.app_service.basic.contract.ContractService import ContractService
from testlib5g.app_service.basic.signalcondition.SignalConditionService import SignalConditionService
from testlib5g.app_service.basic.ue.UeService import UeService
import xml.etree.ElementTree as ET


def query_uds_security_info(gnbAlias, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.query_uds_UdsRbIndex', {'gnbAlias': gnbAlias}).return_string
    UdsRbIndex = re.findall(r'(\d+)\s+\|\s+0x', returnStr)
    UdsRbIndex = UdsRbIndex[0]
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnb<PERSON>lias, 'UdsRbIndex': UdsRbIndex}).return_string
    ucCipherAlgType = re.findall(r"ucCipherAlgType        \|\s+(\d+)", returnStr)
    ucCipherAlgType = ucCipherAlgType[0]
    ucAuthAlgType = re.findall(r"ucAuthAlgType        \|\s+(\d+)", returnStr)
    ucAuthAlgType = ucAuthAlgType[0]
    authKey = 0
    cipherKey = 0
    return ucCipherAlgType, ucAuthAlgType, cipherKey, authKey

def query_uds_vectorization_info(gnbAlias, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.udsShowAllTaskStat_uds',  {'gnbAlias': gnbAlias, 'boardType': boardType}).return_string
    try:
        delivPduVppSdapNodeSucc = re.findall(r"DelivPduVppSdapNodeSucc\|\s+(\d+)", returnStr)
        if delivPduVppSdapNodeSucc == []:
            return 0, 0, 0, 0, 0
    except:
        delivPduVppSdapNodeSucc = re.findall(r"DelivPduVppSdapNodeSucc\|\s+(\d+)", returnStr)[0]
        delivPduVppAppNodeSucc = re.findall(r"DelivPduVppAppNodeSucc\|\s+(\d+)", returnStr)[0]
        delivPduVppKpiNodeSucc = re.findall(r"DelivPduVppKpiNodeSucc\|\s+(\d+)", returnStr)[0]
        delivPduVppGtpuNodeSucc = re.findall(r"DelivPduVppGtpuNodeSucc\|\s+(\d+)", returnStr)[0]
        delivPduVppSendPduNodeSucc = re.findall(r"DelivPduVppSendPduNodeSucc\|\s+(\d+)", returnStr)[0]
    return delivPduVppSdapNodeSucc, delivPduVppAppNodeSucc, delivPduVppKpiNodeSucc, delivPduVppGtpuNodeSucc, delivPduVppSendPduNodeSucc

def query_rb_outoforderdelivery_info(gnbAlias, UdsRbIndex, boardType='VSW'):
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    try:
        outoforderdelivery = re.findall(r"outOfOrderDelivery  \|\s+(\d+)", returnStr)
        if outoforderdelivery == []:
            raise
    except:
        outoforderdelivery = re.findall(r"indeedOutOfOrdDeliv  \|\s+(\d+)", returnStr)
    outoforderdelivery = outoforderdelivery[0]
    return outoforderdelivery


def query_uds_pdcp_info(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.query_uds_UdsRbIndex', {'gnbAlias': gnbAlias}).return_string
    UdsRbIndex = re.findall(r'DpfUeId\s+(\d+)\s+\|\s+', returnStr)
    UdsRbIndex = UdsRbIndex[0]
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    result = re.findall(r"RxPdcpPtlInst[^s]+snType\s+\|\s+([^ ]+)[^s]+snMask\s+\|\s+(\d+)", returnStr)
    RxPdcpPtlInst = result[0][0]
    snMask = result[0][1]
    tReorderTimerLen = re.findall(r"tReorderTimerLen   \|\s+(\d+)", returnStr)
    tReorderTimerLen = tReorderTimerLen[0]
    pdcpSnType = re.findall(r"pdcpSnType\s+\|\s+([^ ]+)", returnStr)
    pdcpSnType = pdcpSnType[0]
    discardTimerLen = re.findall(r"discardTimerLen\s+\|\s+([^ ]+)", returnStr)
    discardTimerLen = discardTimerLen[0]
    return RxPdcpPtlInst, snMask, tReorderTimerLen, pdcpSnType, discardTimerLen


def query_uds_pdcp_info_by_rbindex(gnbAlias, udsRbIndex, statNameList, pos):
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnbAlias, 'UdsRbIndex': udsRbIndex}).return_string
    resultList = []
    for i in range(len(statNameList)):
        temp = re.findall(r"%s\s+\|\s+([^ ]+)" % (statNameList[i]), returnStr)
        temp = temp[int(pos)]
        temp = temp.split('_')
        temp = temp[0]
        resultList.append(temp)
    return resultList


def change_pdcp_count(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.query_uds_UdsRbIndex', {'gnbAlias': gnbAlias}).return_string
    UdsRbIndex = re.findall(r'CpfUeId\s+(\d+)\s+\|\s+', returnStr)
    UdsRbIndex = UdsRbIndex[0]
    return ContractService().execute_contract('Comm.change_pdcp_count', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex})


def query_rb_qci_value(gnbAlias, UdsRbIndex):
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    qci = re.findall(r"qci    \|\s+(\d+)", returnStr)
    return qci


def query_rb_legTransBitmap_value(gnbAlias, UdsRbIndex):
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    legTransBitmap = re.findall(r"legTransBitmap    \|\s+(\d+)", returnStr)
    return legTransBitmap


def query_rb_teid_qci_value(gnbAlias, UdsRbIndex, containerId, dockername='uds.*'):
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index_by_containerId', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex[0], 'containerId': containerId, 'dockername': dockername}).return_string
    teid = re.findall(r"localTeid      \|\s+(\d+)", returnStr)
    qci = re.findall(r"qci    \|\s+(\d+)", returnStr)
    return teid[0], qci[0]


def query_all_rb_teid_qci_value(gnbAlias, UdsRbIndex, containerId, dockername='uds.*'):
    teid = []
    qci = []
    if len(UdsRbIndex) == 0:
        return False
    for i in range(len(UdsRbIndex)):
        returnStr = ContractService().execute_contract('Comm.print_uds_rb_index_by_containerId', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex[i], 'containerId': containerId, 'dockername': dockername}).return_string
        teid.append(re.findall(r"localTeid      \|\s+(\d+)", returnStr)[0])
        qci.append(re.findall(r"qci    \|\s+(\d+)", returnStr)[0])
    return teid, qci


def query_rb_atuflag(gnbAlias, UdsRbIndex):
    atuflagall = 0
    for Rbnum in UdsRbIndex:
        returnStr = ContractService().execute_contract('Comm.print_uds_rb_atuflag', {'gnbAlias': gnbAlias, 'UdsRbIndex': Rbnum}).return_string
        atuflag = re.findall(r"ATUFlag:\s+(\d+)", returnStr)
        atuflagall = atuflagall + int(''.join(atuflag or ['0']))
    return atuflagall


def query_signalrb_atuflag(gnbAlias, UdsRbIndex):
    atuflag = 0
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_atuflag', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    atuflag = re.findall(r"ATUFlag:        (\d+)", returnStr)
    return atuflag


def query_rb_RbIdentifyType(gnbAlias, UdsRbIndex):
    UdsRbIndex = int(''.join(UdsRbIndex))
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_RbIdentifyType', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    atuflag = re.findall(r"RbIdentifyType:\s+(\d+)", returnStr)
    atuflag = int(''.join(atuflag))
    return atuflag

def query_rb_UdsRbInstByUdsRbIndex(gnbAlias, UdsRbIndex,statName):
    UdsRbIndex = int(''.join(UdsRbIndex))
    returnStr = ContractService().execute_contract('CellBasic.print_uds_rb_RbInstByUdsRbIndex', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    atuflag = re.findall(r"%s\s+\|\s+(\d)"% statName, returnStr)
    atuflag = int(''.join(atuflag))
    return atuflag

def open_uds_cvpp(gnbAlias):
    returnStr = ContractService().execute_contract('CellBasic.open_uds_cvpp_entry_switch', {'gnbAlias': gnbAlias}).return_string

def close_uds_cvpp(gnbAlias):
    returnStr = ContractService().execute_contract('CellBasic.close_uds_cvpp_entry_switch', {'gnbAlias': gnbAlias}).return_string

def query_rb_RbIdentifyType2(gnbAlias, UdsRbIndex):
    RbIdentifyTypeall = 0
    for Rbnum in UdsRbIndex:
        returnStr = ContractService().execute_contract('Comm.print_uds_rb_RbIdentifyType2', {'gnbAlias': gnbAlias, 'UdsRbIndex': Rbnum}).return_string
        RbIdentifyType = re.findall(r"RbIdentifyType:\s+(\d+)", returnStr)
        RbIdentifyTypeall = RbIdentifyTypeall + int(''.join(RbIdentifyType))
    return RbIdentifyTypeall


def get_integrityfail_discardnum(cellAlias):
    results1 = ContractService().execute_contract('CellBasic.check_uds_outwindow_num_ue', {'cellAlias': cellAlias}).return_string
    CheckResult = re.findall(r'\[UdsDeCipherCallBack_PdcpPduDiscardByIntegrityFail\]\:(\d+)', results1)
    if not CheckResult:
        return False
    else:
        outwindownum = CheckResult[0]
        return outwindownum


def get_xnsend_endmarknum(cellAlias):
    results1 = ContractService().execute_contract('CellBasic.check_uds_xnendmark_num_ue', {'cellAlias': cellAlias}).return_string
    CheckResult = re.findall(r'\[TxRouteSendOneXnPdu_SendEndMarkerToXn\]\:(\d+)', results1)
    if not CheckResult:
        return False
    else:
        sendendmarknum = CheckResult[0]
        return sendendmarknum


def get_xnrcv_endmarknum(cellAlias):
    results1 = ContractService().execute_contract('CellBasic.check_uds_xnendmark_num_ue', {'cellAlias': cellAlias}).return_string
    CheckResult = re.findall(r'\[UdsTxGtpuEndMarkerProc_RcvdXnDlEndMarkerNum\]\:(\d+)', results1)
    if not CheckResult:
        return False
    else:
        rcvendmarknum = CheckResult[0]
        return rcvendmarknum


def query_rb_RetranQueueSwitch(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.query_uds_RetranQueueSwitch', {'gnbAlias': gnbAlias}).return_string
    result = re.findall(r"value=+([^\r\n]+)", returnStr)
    return result


def query_udc_upfgtpu_present(gnbAlias, srcCell, desCell, targetpci, ueAlias, nums=20):
    thread1 = Thread(target=ue_handover, args=(srcCell, desCell, targetpci, ueAlias, nums))
    thread1.setDaemon(True)
    thread1.start()
    returnStr = ContractService().execute_contract('Comm.query_udc_upfaddr_present', {'gnbAlias': gnbAlias}).return_string
    result = re.findall(r"upfGtpuPresent\s+=\s(\d+)", returnStr)
    return result


def query_udc_upfgtpu_times(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.query_udc_upfaddr_times', {'gnbAlias': gnbAlias}).return_string
    result = re.findall(r"udcUpfGtpuUpdateInfoProc_UdcUpmUpfGtpuPresentSwitchSucc\]\:(\d+)", returnStr)
    if not result:
        return False
    else:
        rcvendmarknum = int(result[0])
        return rcvendmarknum


def query_rb_maxS1QueueLen(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.query_uds_maxS1QueueLen', {'gnbAlias': gnbAlias}).return_string
    result = re.findall(r"value=+([^\r\n]+)", returnStr)
    return result


def check_change_sence_para(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.query_exclusive_cpu', {'gnbAlias': gnbAlias}).return_string
    result = re.findall(r"cat /proc/exclusive_cpus\r\n+([^\r\n]+)", returnStr)
    exclusive_cpu_list = bin(int(result[0], 16))[2:]
    returnStr = ContractService().execute_contract('Comm.print_container_resource', {'gnbAlias': gnbAlias}).return_string
    uds_cpu = re.findall(r"export EXCLUSIVE_CPUS_LIST=+([^/ #\r\n]+)", returnStr)
    uds_cpu = uds_cpu[0].split(",")
    return exclusive_cpu_list, len(uds_cpu), uds_cpu


def get_all_uds_session_index(gnbAlias):
    results1 = ContractService().execute_contract('Comm.query_uds_UdsSessionId', {'gnbAlias': gnbAlias}).return_string
    CheckResult = re.findall(r'\s+(\d+)\s+\|', results1)
    if not CheckResult:
        return False
    else:
        sessionindex = CheckResult
        return sessionindex


def print_dpscallback_cpu(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.print_dpscallback_cpu', {'gnbAlias': gnbAlias}).return_string
    returnStr = re.findall(r"COMMAND+([^*]+)", returnStr)
    returnStr = returnStr[0].split("-")
    returnStr_len = len(returnStr)
    zip_list = zip(*(range(returnStr_len), returnStr))
    slave_list = [z[0] for i, z in enumerate(zip_list) if z[1] == "slave"]
    list = []
    for slave_index in slave_list:
        returnStr1 = returnStr[slave_index - 1].split(" ")
        lcore = returnStr1.index("{lcore")
        dpscallback_cpu = returnStr1[lcore - 3]
        list.append(dpscallback_cpu)
    return len(slave_list), list


def query_uds_rb_inforamtion(gnbAlias):
    returnStr = ContractService().execute_contract('Comm.query_uds_UdsRbIndex', {'gnbAlias': gnbAlias}).return_string
    udsRbIndex = re.findall(r'CpfUeId\s+(\d+)\s+\|\s+', returnStr)
    if udsRbIndex != []:
        return False
    else:
        return True

def query_uds_rb_UdsRbIndex(gnbAlias,statName):
    resultList = []
    returnStr = ContractService().execute_contract('Comm.query_uds_UdsRbIndex', {'gnbAlias': gnbAlias}).return_string
    UdsRbInstNum = re.findall(r'Total Online UdsRbInst Num : (\d+)', returnStr)
    UdsRbInstInfo = returnStr.replace(" ","").split("\r\n")
    for i in range(len(UdsRbInstInfo)):
        if statName in UdsRbInstInfo[i]:
            statNameList = UdsRbInstInfo[i].split("|")
            indexNum = statNameList.index(statName)
            for j in range(int(UdsRbInstNum[0])):
                statValueList = UdsRbInstInfo[i+j+1].split("|")
                statValue = statValueList[int(indexNum)]
                resultList.append(statValue)
            break
    return resultList


def query_uds_tcp_proxy_instnum(gnbAlias):
    resultStr = ContractService().execute_contract('uds.query_uds_tcpproxy', {'gnbAlias': gnbAlias}).return_string
    DlTcpProxyInstCreateNum = re.findall(r'\[DlTcpProxyInstCreateNum\]\:(\d+)', resultStr)
    if not DlTcpProxyInstCreateNum:
        return False
    return True


def query_uds_specified_statistics_have(gnbAlias, statistics):
    resultStr = ContractService().execute_contract('uds.query_uds_statisticshave', {'gnbAlias': gnbAlias}).return_string
    specified_statistics_have = re.findall(r'\[{}\]:(\d+)'.format(statistics), resultStr)
    if not specified_statistics_have:
        return False
    return True


def query_uds_specified_statistics_num(gnbAlias, statistics):
    resultStr = ContractService().execute_contract('uds.query_uds_statisticsnum', {'gnbAlias': gnbAlias}).return_string
    CheckResult = re.findall(r'\[{}\]:(\d+)'.format(statistics), resultStr)
    if not CheckResult:
        return False
    else:
        outwindownum = CheckResult[0]
        return outwindownum

def query_uds_specified_statistics_num_in_only_container(gnbAlias, statistics, containerId):
    resultStr = ContractService().execute_contract('uds.query_uds_all_statistics_num', {'gnbAlias': gnbAlias, 'containerId': containerId}).return_string
    CheckResult = re.findall(r'\[{}\]:(\d+)'.format(statistics), resultStr)
    if not CheckResult:
        return False
    else:
        return CheckResult[0]

def query_uds_pilefunction_return_value(gnbAlias, cmd):
    resultStr = ContractService().execute_contract('uds.print_uds_returnvalue', {'gnbAlias': gnbAlias, 'cmd': cmd}).return_string
    result = re.findall(r'value\s+=\s+(\d+)', resultStr)
    CheckResult = result[0]
    return CheckResult


def query_uds_globalvariable_return_value(gnbAlias, cmd):
    resultStr = ContractService().execute_contract('uds.print_uds_global_returnvalue', {'gnbAlias': gnbAlias, 'cmd': cmd}).return_string
    result = re.findall(r'value\s*=\s*(0x[0-9a-fA-F]{1,})', resultStr)
    print(resultStr)
    print(result)
    CheckResult = result[0]
    return CheckResult


def query_uds_decipher_fail_stat(gnbAlias):
    resultStr = ContractService().execute_contract('uds.query_uds_FailStat', {'gnbAlias': gnbAlias}).return_string
    decipherFailStat = re.findall(r'UdsShowSecDecipherFailStat\s+(\d+)\s+\|\s+', resultStr)
    if decipherFailStat == []:
        return True
    return False


def get_uds_outwindow_num(cellAlias):
    results1 = ContractService().execute_contract('CellBasic.check_uds_outwindow_num_ue', {'cellAlias': cellAlias}).return_string
    CheckResult = re.findall(r'\[RxPdcpReorderProc_OutWindowDiscardPduSucc\]\:(\d+)', results1)
    if not CheckResult:
        return False
    else:
        outwindownum = CheckResult[0]
        return outwindownum


def print_Flow_information(gnbAlias):
    resultStr = ContractService().execute_contract('uds.print_uds_Flow', {'gnbAlias': gnbAlias}).return_string
    ulFlow = re.findall('count_ul.*?=(\d+).*', resultStr)
    dlFlow = re.findall('count_dl.*?=(\d+).*', resultStr)
    result = []
    result.append(ulFlow[0])
    result.append(dlFlow[0])
    return result


def get_session_index(gnbAlias):
    results = ContractService().execute_contract('Comm.query_uds_UdsSessionId', {'gnbAlias': gnbAlias}).return_string
    session_index = re.search(r"DpfUeId\s*(\d*)", results).group(1)
    return session_index


def get_5qi_and_dscp_by_session_index(gnbAlias):
    sessionIndex = get_session_index(gnbAlias)
    results = ContractService().execute_contract('uds.print_uds_session_inst', {'gnbAlias': gnbAlias, 'sessionIndex': sessionIndex}).return_string
    resultList = []
    if results.find('qfiInfo index is') >= 0:
        QfiInfoList = re.findall('\|\s+nr5qi\s+\|\s+(\d+)\s+\|.*?\|\s+dscp\s+\|\s+(\d+)\s+\|', results, re.S)
    else:
        QfiInfoList = re.findall(r"\|\s+\d+\s+\d+\s+\d+\s+(\d+)\s+(\d+)\s+(\d+\s+){1,3}[OX]{1}", results)
    for item in QfiInfoList:
        if item[0] != "255" and item[1] != "0":
            resultList.append({"nr5qi": item[0], "dscp": item[1]})
    return resultList


def get_all_5qi_and_dscp_by_session_index(gnbAlias):
    sessionIndexList = get_all_uds_session_index(gnbAlias)
    resultList = []
    for sessionIndex in sessionIndexList:
        results = ContractService().execute_contract('uds.print_uds_session_inst', {'gnbAlias': gnbAlias, 'sessionIndex': sessionIndex}).return_string
        if results.find('qfiInfo index is') >= 0:
            QfiInfoList = re.findall('\|\s+nr5qi\s+\|\s+(\d+)\s+\|.*?\|\s+dscp\s+\|\s+(\d+)\s+\|', results, re.S)
        else:
            if results.find("| 5qi|dscp |rqi|dl/ul SdapHeader|") == -1:
                QfiInfoList = re.findall(r"\|\s+\d+\s+\d+\s+\d+\s+(\d+)\s+(\d+)\s+(\d+\s+){1,2}O - O", results)
            if results.find("| 5qi|dscp |rqi|erabId|") == -1:
                QfiInfoList = re.findall(r"\|\s+\d+\s+\d+\s+\d+\s+(\d+)\s+(\d+)\s+(\d+\s+){2,3}O - ", results)
            else:
                QfiInfoList = re.findall(r"(\d*)\s*(\d*)\s*0\s*O - O", results)
        for item in QfiInfoList:
            if item[0] != "255" and item[1] != "0":
                resultList.append({"nr5qi": item[0], "dscp": item[1]})
    return resultList

def get_pbr_info_by_session_index(gnbAlias, sessionIndex):
    sessionInfoList = []
    results = ContractService().execute_contract('uds.print_uds_session_inst', {'gnbAlias': gnbAlias, 'sessionIndex': sessionIndex}).return_string
    qfiList = re.findall(r"qfiInfo index is (\d+)", results)
    sessionInfoList.append(qfiList)
    rbIndexList = re.findall(r"\|\s+udsRbIndex\s+\|\s+(\d+)\s+\|", results)
    sessionInfoList.append(rbIndexList)
    rbIdList = re.findall(r"\|\s+drbId\s+\|\s+(\d+)\s+\|", results)
    sessionInfoList.append(rbIdList)
    nr5qiList = re.findall(r"\|\s+nr5qi\s+\|\s+(\d+)\s+\|", results)
    sessionInfoList.append(nr5qiList)
    pbrFlagList = re.findall(r"\|\s+controlPbrFlag\s+\|\s+(\d+)\s+\|", results)
    sessionInfoList.append(pbrFlagList)
    return sessionInfoList

def get_all_5qi_and_rlcmode_by_rb_index(gnbAlias, UdsRbIndex):
    qci = []
    rlcmode = []
    resultList = []
    if len(UdsRbIndex) == 0:
        return False
    for i in range(len(UdsRbIndex)):
        returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex[i]}).return_string
        rlcmode.append(re.findall(r"rlcMode      \|\s+(\d+)", returnStr)[0])
        qci.append(re.findall(r"qci    \|\s+(\d+)", returnStr)[0])
        resultList.append({"qci": qci[i], "rlcmode": rlcmode[i]})
    return resultList

def query_rb_OutOfOrdDeliv(gnbAlias, UdsRbIndex):
    returnStr = ContractService().execute_contract('Comm.print_uds_rb_index', {'gnbAlias': gnbAlias, 'UdsRbIndex': UdsRbIndex}).return_string
    cfgOutOfOrdDeliv = re.findall(r"cfgOutOfOrdDeliv   \|              (\d)", returnStr)
    indeedOutOfOrdDeliv = re.findall(r"indeedOutOfOrdDeliv  \|              (\d)", returnStr)
    return cfgOutOfOrdDeliv,indeedOutOfOrdDeliv

def get_qos_flow_information_map(gnbAlias, ueAlias):
    thread1 = Thread(target=ue_access, args=(ueAlias,))
    thread1.setDaemon(True)
    thread1.start()
    cmdResult = ContractService().execute_contract('uds.query_uds_qos_flow', {'gnbAlias': gnbAlias})
    resultFlow = cmdResult.return_string
    nr5qi = re.search('qosFlowInfo\[0\]->nr5qi\s*= (\d*)', resultFlow).group(1)
    dscp = re.search('qosFlowInfo\[0\]->dscp\s*= (\d*)', resultFlow).group(1)
    return {"nr5qi": nr5qi, "dscp": dscp}


def get_dl_pdcp_status_rpt_ind(gnbAlias, ueAlias):
    thread1 = Thread(target=ue_access, args=(ueAlias,))
    thread1.setDaemon(True)
    thread1.start()
    cmdResult = ContractService().execute_contract('uds.query_uds_dlPdcpStatusRptInd', {'gnbAlias': gnbAlias})
    resultFlow = cmdResult.return_string
    dlPdcpStatusRptInd = re.search('dlPdcpStatusRptInd=(\d*)', resultFlow).group(1)
    return dlPdcpStatusRptInd


def ue_access(ueAlias):
    time.sleep(10)
    UeService().dlsyn_and_attach(ueAlias)


def ue_handover(srcCell, desCell, targetpci, ueAlias, nums):
    for i in range(nums):
        time.sleep(3)
        SignalConditionService.attenuate_cell_signal(srcCell, i * 2)
        SignalConditionService.attenuate_cell_signal(desCell, nums * 2 - i * 2)
    assert UeService.get_pci(ueAlias, "NR") == targetpci


def query_rft_testcase(gnbAlias, cmd, testcaseName):
    resultStr = ContractService().execute_contract('uds.exec_cmd_in_rft', {'gnb': gnbAlias, 'cmd': cmd}).return_string
    print re.findall('testcase name=\"(\w+).*\"', resultStr)[0]
    if testcaseName != re.findall('testcase name=\"(\w+).*\"', resultStr)[0]:
        return False
    print re.findall('failures=\"(\d+).*', resultStr)[0]
    if 0 == int(re.findall('failures=\"(\d+).*', resultStr)[0]):
        return True
    else:
        return False


def query_rft_gtest_xml(gnbAlias, cmd):
    resultStr = ContractService().execute_contract('uds.exec_cmd_in_rft', {'gnb': gnbAlias, 'cmd': cmd}).return_string
    gtest_xml_res = re.findall(r'No such file or directory', resultStr)
    print gtest_xml_res
    if gtest_xml_res:
        return False
    else:
        return True


def parser_rft_gtest_xml(xmlpath):
    testInfo = []
    root = ET.parse(xmlpath).getroot()
    testsuite = root.findall('testsuite')
    for i in testsuite:
        suiteDict = {}
        failInfo = '-'
        testcase = i.findall('testcase')
        for j in testcase:
            failure = j.find('failure')
            if failure is not None:
                failInfo = failInfo + ' ' + j.attrib['name']
        suiteDict['test_suite'] = i.attrib['name']
        suiteDict['test_case'] = i.attrib['tests']
        suiteDict['fail_case'] = i.attrib['failures']
        suiteDict['succ_case'] = int(i.attrib['tests']) - int(i.attrib['failures'])
        suiteDict['succ_rate'] = round(float(int(i.attrib['tests']) - int(i.attrib['failures'])) / float(i.attrib['tests']), 2)
        suiteDict['fail_info'] = failInfo
        testInfo.append(suiteDict)
    return testInfo


def get_pdcp_status_rpt_map(gnbAlias):
    cmdResult = ContractService().execute_contract('Comm.udsShowAllTaskStat_uds', {'gnbAlias': gnbAlias, 'boardType': "VSW"}).return_string
    ulSucc = re.search('\[ulPdcpStatRtpPktRcvSucc\]:(\d*)', cmdResult).group(1)
    dlSucc = re.search('\[txRouteDlPdcpStatRptSend_Succ\]:(\d*)', cmdResult).group(1)
    return {"ulSucc": ulSucc, "dlSucc": dlSucc}


def query_uds_rbindex_rcvsendflow(gnbAlias, rbIndex, boardType='VSW'):
    taskFlowNameList = ['txGtpuRcvFlow', 'txGtpuSendFlow', 'txRouteRcvGtpuFlow', 'txRouteSendPdcpFlow', 'txPdcpRcvRouteFlow', 'txPdcpSendF1uFlow\[MN\]', 'txPdcpSendF1uFlow\[SN\]',
                        'rxPdcpRcvFlow', 'rxPdcpRcvF1uFlow\[MN\]', 'rxPdcpRcvF1uFlow\[SN\]', 'rxPdcpSendFlow']
    udsRbIndexFlow = {}
    returnStr = ContractService().execute_contract('uds.OpenRbIndexFlow_uds', {'gnbAlias': gnbAlias, 'rbIndex': rbIndex, 'boardType': boardType}).return_string
    for taskFlowName in taskFlowNameList:
        result = re.findall(r"{}\s+:(\d+)".format(taskFlowName), returnStr)
        udsRbIndexFlow[taskFlowName] = result[1]
    return udsRbIndexFlow


def get_uds_ngu_teid_and_f1u_teid(gnb, cmd, dockerName, containerId, expected):
    teid = []
    returnStr = ContractService().execute_contract('GnbPerformanceBasic.stub_in_only_container', {'gnb': gnb, 'cmd': cmd, 'dockerName': dockerName, 'containerId': containerId, 'expected': expected}).return_string
    result = re.findall(r'localTeid *\| *(\d+)', returnStr)
    for i in result:
        print i
        teid.append(hex(int(i)))
    return teid


def get_uds_ip_and_mac(gnb, cmd, dockerName, containerId, expected):
    ip_mac = []
    returnStr = ContractService().execute_contract('GnbPerformanceBasic.stub_in_only_container', {'gnb': gnb, 'cmd': cmd, 'dockerName': dockerName, 'containerId': containerId, 'expected': expected}).return_string
    result1 = re.findall(r"193\.254\.\d+\.\d+", returnStr)
    result2 = result1[0]
    result3 = re.findall(r"40:00:c1:fe:\d+:\d+", returnStr)
    result4 = result3[0]
    ip_mac.append(result2)
    ip_mac.append(result4)
    return ip_mac


def get_uds_flow_by_bsp(gnb, dockerName, containerId):
    flow = []
    returnStr = ContractService().execute_contract('GnbPerformanceBasic.get_uds_flow_by_exec_bsp', {'gnb': gnb, 'dockerName': dockerName, 'containerId': containerId}).return_string
    result1 = re.findall(r"-(\d+)Kbit", returnStr)
    print result1[-1], result1[-2]
    flow.append(result1[-1])
    flow.append(result1[-2])
    return flow


def get_uds_Pps_by_bsp(gnb, dockerName, containerId):
    Pps = []
    returnStr = ContractService().execute_contract('GnbPerformanceBasic.get_uds_flow_by_exec_bsp', {'gnb': gnb, 'dockerName': dockerName, 'containerId': containerId}).return_string
    result1 = re.findall(r"(\d+)Pps-", returnStr)
    print result1[-1], result1[-2]
    Pps.append(result1[-1])
    Pps.append(result1[-2])
    return Pps


def get_ptapp_image_id(cellAlias, cmd):
    returnStr = ContractService().execute_contract('Comm.exec_cmd_on_2_vsw_ssh_login', {'cellAlias': cellAlias, 'cmd': cmd}).return_string
    result1 = re.findall(r"-\d+\s+(\w+)\s+", returnStr)
    print result1
    return result1[0]


def get_uds_perf2_info(gnb, containerId, dockerName, cmd, expectInfo):
    resultDict = {}
    resultList = []
    returnStr = ContractService().execute_contract('uds.exec_cmd_in_docker_root_dir', {'gnb': gnb, 'dockerName': dockerName, 'containerId': containerId, 'cmd': cmd, 'expectInfo': expectInfo}).return_string
    overhead = re.findall(r'(\d+.\d+)\%', returnStr)[-20:]
    function = re.findall(r'\] (\w+)', returnStr)[-20:]
    for i in range(20):
        resultDict['sn'] = i
        resultDict['overhead'] = overhead[i]
        resultDict['function'] = function[i]
        resultList.append(resultDict)
        resultDict = {}
    return resultList


def get_container_root_dir_return_info_after_exec_cmd(gnb, containerId, dockerName, cmd, expectInfo):
    return ContractService().execute_contract('uds.exec_cmd_in_docker_root_dir', {'gnb': gnb, 'dockerName': dockerName, 'containerId': containerId, 'cmd': cmd, 'expectInfo': expectInfo}).return_string


def get_uds_fastRecoveryswitch(gnbAlias):
    cmdResult = ContractService().execute_contract('uds.uds_fastRecoveryswitch', {'gnbAlias': gnbAlias}).return_string
    fastRecoveryswitch = re.findall(r"value = (\d)", cmdResult)
    return fastRecoveryswitch


def get_uds_queuestatics(gnbAlias):
    cmdResult = ContractService().execute_contract('uds.uds_queuestatics', {'gnbAlias': gnbAlias}).return_string
    queuestatics1 = re.findall(r"TcpOutOfOrderPkt]:(\d+)", cmdResult)
    queuestatics2 = re.findall(r"TcpOutOfOrderByte]:(\d+)", cmdResult)
    return queuestatics1[0], queuestatics2[0]


def get_uds_retancetimedelay(gnbAlias):
    cmdResult = ContractService().execute_contract('uds.uds_retancetimedelay', {'gnbAlias': gnbAlias}).return_string
    cudelaytime = re.findall(r"TcpRetranPkt_CuDelayTime]: (\d+)", cmdResult)
    resultlist = [int(i) for i in cudelaytime]
    print sum(resultlist)
    print len(resultlist)
    avertime = sum(resultlist) / len(resultlist)
    return avertime


def get_uds_cycle(gnb, cmd, dockerName, containerId, expected):
    returnStr = ContractService().execute_contract('GnbPerformanceBasic.stub_in_only_container', {'gnb': gnb, 'cmd': cmd, 'dockerName': dockerName, 'containerId': containerId, 'expected': expected}).return_string
    result = re.findall(r'cycleAve\[0\] = (\d+)', returnStr)
    if result:
        return result[0]
    else:
        return 0
