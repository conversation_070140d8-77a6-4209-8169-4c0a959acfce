# coding=utf-8
'''
Created on 2019年9月10日

@author: 10207409
'''
import re
import os

from testlib5g.app_service.basic.contract.ContractService import ContractService


def confirm_ue_context_exist(cellAlias):
    result = ContractService.execute_contract('CellBasic.get_ue_context_log', {'cellAlias': cellAlias})
    if "UeCtxtSetupRequest" in result.return_string and "UeCtxtSetupResponse" in result.return_string:
        return True
    else:
        return False


def release_cpf_ue(cellAlias):
    ContractService.execute_contract('CellBasic.release_cpf_ue',
                                     {'cellAlias': cellAlias}, 2)


def check_huc_ues_openlog(cellAlias):
    return ContractService.execute_contract('CellBasic.check_huc_ues_openlog',
                                     {'cellAlias': cellAlias}).return_string


def set_modify_required_timer(cellAlias,timer):
    ContractService.execute_contract('CellBasic.set_modify_required_timer',
                                     {'cellAlias': cellAlias, 'timer': timer})


def set_scelladd_rrc_timer(cellAlias,timer):
    ContractService.execute_contract('CellBasic.set_scelladd_rrc_timer',
                                     {'cellAlias': cellAlias, 'timer': timer})

def set_caadd_rrc_timer(cellAlias,timer):
    ContractService.execute_contract('CellBasic.set_caadd_rrc_timer',
                                     {'cellAlias': cellAlias, 'timer': timer})


def set_x1_rrc_timer(cellAlias,timer):
    ContractService.execute_contract('CellBasic.set_x1_rrc_timer',
                                     {'cellAlias': cellAlias, 'timer': timer})    

def set_shortmac_state(cellAlias,state):
    ContractService.execute_contract('CellBasic.set_shortmac_state',
                                     {'cellAlias': cellAlias, 'state': state})    

def rel_all_ues(cellAlias):
    return ContractService.execute_contract('CellBasic.rel_all_ue',
                                            {'cellAlias': cellAlias}).result


def get_ue_info(cellAlias):
    return ContractService.execute_contract('CellBasic.get_ue_info',
                                            {'cellAlias': cellAlias}, 2)


def nsa_not_waitfor_reconfig(cellAlias):
    ContractService().execute_contract('CellBasic.not_waitfor_reconfig', {'cellAlias': cellAlias}).return_string
    return True


def get_cpf_ue_num(cellAlias):
    ueInfo = ContractService.execute_contract('CellBasic.get_ue_info', {'cellAlias': cellAlias}, 2)
    if re.search('Total:(\d+)\s+===', ueInfo.return_string):
        return re.search('Total:(\d+)\s+===', ueInfo.return_string).group(1).strip()
    return 0

def get_huc_ue_srb_num(cellAlias):
    ueInfo = ContractService().execute_contract('CellBasic.get_ue_srbnum', {'cellAlias': cellAlias}).return_string
    CheckResult = re.findall(r"responseDiscardPktNum:(\d+)", ueInfo)
    if not CheckResult:
        return False
    else:
        srbnum = CheckResult[0]
        return srbnum

def get_integrityfail_srb_num(cellAlias):
    ueInfo = ContractService().execute_contract('CellBasic.get_ue_srbnum', {'cellAlias': cellAlias}).return_string
    CheckResult = re.findall(r"integrityCheckMacIFailNum:(\d+)", ueInfo)
    if not CheckResult:
        return False
    else:
        srbnum = CheckResult[0]
        return srbnum
 
def get_harqfail_srb_num(cellAlias):
    ueInfo = ContractService().execute_contract('CellBasic.get_ue_srbnum', {'cellAlias': cellAlias}).return_string
    CheckResult = re.findall(r"packetOutWindowDiscardNum:(\d+)", ueInfo)
    if not CheckResult:
        return False
    else:
        srbnum = CheckResult[0]
        return srbnum   

def gnb_release_ue(cellAlias):
    relInfo = ContractService.execute_contract('CellBasic.gnb_release_ue', {'cellAlias': cellAlias}, 2)
    if re.search('clear', relInfo.return_string):
        return True
    return None

def get_huc_ue_pudsession_info(gnbAlias):
    ueInfo = ContractService().execute_contract('Comm.get_hucm_ue_info', {'gnbAlias': gnbAlias}).return_string
    if re.findall(r"CPF_UE_ID:\s*\[+(\d+)", ueInfo):
        ueId = re.findall(r"CPF_UE_ID:\s*\[+(\d+)", ueInfo)[0]
        pudsessionInfo = ContractService().execute_contract('Comm.get_huc_ue_pudsession', {'gnbAlias': gnbAlias, 'ueId':ueId}).return_string
        if pudsessionInfo:
            return ueId, pudsessionInfo
    return None, None

def get_user_cpf_ue_id(cellAlias,userType='ALL'):
    udsRbIndexCpfUeIdDict = {}
    returnString = ContractService().execute_contract('CellBasic.check_uds_rbindex_ue_hf', {'cellAlias': cellAlias}).return_string
    for findI in re.findall('.*\n', returnString):
        udsRbIndexAndCpfUeId = re.findall(r'\s+(\d+)\W+0x\w+\W+(\d+)\s+', findI)
        if udsRbIndexAndCpfUeId:
            udsRbIndexCpfUeIdDict.update({udsRbIndexAndCpfUeId[0][0]: udsRbIndexAndCpfUeId[0][1]})
    if userType == 'ALL':
        return list(set(udsRbIndexCpfUeIdDict.values()))
    elif userType == 'ATU':
        return get_atu_user_cpf_ue_id(cellAlias, udsRbIndexCpfUeIdDict)

def get_atu_user_cpf_ue_id(cellAlias, udsRbIndexCpfUeIdDict):
    atuCpfUeId = []
    for rbIndex in udsRbIndexCpfUeIdDict:
        returnStr = ContractService().execute_contract('CellBasic.query_uds_rb_atuflag', {'cellAlias': cellAlias, 'UdsRbIndex': rbIndex}).return_string
        atuFlag = re.findall(r"ATUFlag:\s+(\d+)", returnStr)
        if atuFlag and str(atuFlag[0]) == '16':
            atuCpfUeId.append(udsRbIndexCpfUeIdDict[rbIndex])
    return list(set(atuCpfUeId))

def set_huc_stub(cellAlias,stubCmd):
    relInfo = ContractService.execute_contract('CellBasic.set_huc_stub', {'cellAlias': cellAlias,'stubCmd':stubCmd}, 2)
    return re.search('clear', relInfo.return_string)

def set_handover_ack_error(cellAlias,ackErrSwitch):
    relInfo = ContractService.execute_contract('CellBasic.set_handover_ack_error', {'cellAlias': cellAlias,'AckErrSwitch':ackErrSwitch}, 2)
    return re.search('clear', relInfo.return_string)

def print_huc_show_log(cellAlias):
    return ContractService().execute_contract('CellBasic.print_huc_show_log', {'cellAlias': cellAlias}).return_string

def del_huc_container_by_caps(filePath, fileName):
    os.chdir("{}".format(filePath))
    os.system("caps.exe pile -c {}".format(fileName))

def get_the_call_chain_memory_size_of_the_specified_container(gnbAlias, containerName, topNumber, totalNumeberOfFunctions, containerIndex = -1):
    paraDict = {'gnb': gnbAlias, 'cmd': 'mstat_show {},{}'.format(topNumber,totalNumeberOfFunctions), 'containerName': containerName, 'expected':'end to excel fun'}
    returnStr = ContractService().execute_contract('GnbPerformanceBasic.stub_in_container', paraDict, containerIndex=int(containerIndex)).return_string
    memorySizeList = re.findall('size: (\d+)', returnStr)
    totalMemorySize = 0
    for memorySize in memorySizeList:
        totalMemorySize += int(memorySize)
    return totalMemorySize
