# -*- encoding: utf-8 -*-
"""
@Time    :   2024/1/17 15:22:44
<AUTHOR>   10262770
@Version :   1.0
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

from infrastructure.logger import Logger
from infrastructure.device.board.service.Service9200 import Service9200
from infrastructure.utils.interface_contract.ContractRegister import ContractRegister


DEFAULT_VSW = {
    'port': 23,
    'username': 'itran',
    'password': 'Itran_2430!@#',
    'connectType': 'Ssh'
}


class Vsw(object):
    def __init__(self, paras):
        self._paras = paras

    def create_vsw_paras(self, device):
        params = {
            "gnbAlias": 'gnb',
            "boardType": self._paras.deviceType
        }
        if hasattr(self._paras, 'process'):
            process = self._paras.process.split('|') if self._paras.process else ''
            if process:
                params.update({"containerName": process[0]})
            else:
                params.update({"containerName": ''})
            padProc = self._paras.process if len(process) > 1 else ''
            if padProc:
                params.update({"padProc": padProc})
        if hasattr(self._paras, 'expectInfo'):
            params.update({"expectInfo": self._paras.expectInfo})
        if hasattr(self._paras, 'func') and self._paras.func:
            params.update({"func": self._paras.func})
            Logger.info(f"当前敲桩的方式是VSW上的组合功能指令,组合功能为:{self._paras.func} ！")
        elif hasattr(self._paras, 'cmd') and self._paras.cmd:
            params.update({"cmd": self._paras.cmd})
            Logger.info(f"当前敲桩的方式是直接在VSW上进行指令发送,发送指令为:{self._paras.cmd} ！")
        Logger.info(f"当前敲桩的VSW参数是{params} ！")
        return params, self.create_vsw_obj(device)

    def create_vsw_obj(self, device):
        vswSet = Service9200(
            ip=device.get('attr').get('ip') or DEFAULT_VSW['ip'],
            port=device.get('attr').get('port') or DEFAULT_VSW['port'],
            username=device.get('attr').get('username') or DEFAULT_VSW['username'],
            password=device.get('attr').get('password') or DEFAULT_VSW['password'],
            boardType=device.get('attr').get('type') or 'VSW',
            productVersion='NRV2',
            channelType=device.get('attr').get('connectType') or DEFAULT_VSW['connectType']
            )
        ContractRegister().register('gnb', 'VSW:SERVICE', vswSet)
        return vswSet