# -*- encoding: utf-8 -*-
"""
@Time    :   2025/2/28 15:22:44
<AUTHOR>   10270755
@Version :   1.0
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""

from infrastructure.logger import Logger
from infrastructure.device.board.vgc.Vgc import Vgc as VgcDevice
from infrastructure.utils.interface_contract.ContractRegister import ContractRegister


DEFAULT_VGC = {
    'port': 22,
    'username': 'ubuntu',
    'password': 'UbuntU1!2@3#4$',
    'channelType': 'Ssh'
}


class Vgc(object):
    def __init__(self, paras):
        self._paras = paras

    def create_vgc_paras(self, device):
        params = {
            "gnbAlias": 'gnb',
            "boardType": self._paras.deviceType
        }
        Logger.info(f"当前敲桩的vgc参数是{params} ！")
        return params, self.create_vgc_obj(device)

    def create_vgc_obj(self, device):
        VgcSet = VgcDevice(
            ip=device.get('attr').get('ip') or DEFAULT_VGC['ip'],
            port=device.get('attr').get('port') or DEFAULT_VGC['port'],
            username=device.get('attr').get('username') or DEFAULT_VGC['username'],
            password=device.get('attr').get('password') or DEFAULT_VGC['password'],
            channelType=device.get('attr').get('channelType') or DEFAULT_VGC['channelType']
            )
        ContractRegister().register('gnb', 'VGC:SERVICE', VgcSet)
        return VgcSet