import os.path
import re
import pytz
from datetime import datetime, timedelta
from domain.platform.workspace import Workspace
import csv
import pandas as pd
import xml.etree.ElementTree as ET


from infrastructure.channels.ssh.Ssh import Ssh
from infrastructure.device.DeviceCmd import DeviceCmd
from infrastructure.utils.Repeat import retries_during_time
from infrastructure.logger import Logger
from GlobalVars import status
from domain.platform.artifact import Artifact

class Nds(object):

    def __init__(self, ndsInfo):
        self._userName = ndsInfo.get('attr').get('username')
        self._password = ndsInfo.get('attr').get('password')
        self._ip = ndsInfo.get('attr').get('ip')
        self._port = ndsInfo.get('attr').get('port')
        self._rootPath = ndsInfo.get('attr').get('rootPath', "/paasdata/op-comsrv/data/FTPv3/nds_ftp_cluster1/data/cdtftpuser/TestAll/")
        self._xmlRootPath = ndsInfo.get('attr').get('xmlRootPath', "/paasdata/op-comsrv/data/FTPv3/nds_ftp_cluster1/data/ZTE_NORTH/MR/")

    async def _login_os(self):
        try:
            sshParaDict = {'hostname': self._ip, 'username': self._userName, 'password': self._password, 'sshType': 'bmsc'}
            Logger.debug(sshParaDict)
            self._ssh = Ssh(sshParaDict, self._port, 10)
            self._ssh.connect()
            await self._su_root()
        except Exception as e:
            Logger.error("连接失败，请检查sftp配置信息！:{0}".format(e))
            return False, "连接失败，请检查sftp配置信息！"


    @retries_during_time(everyTryDelaySecs=1, flag=[], maxTries=3, returnType=[])
    async def _execute_shell_cmd(self, cmd, expected='#|\$', timeout=30):
        if not self._ssh or not self._ssh.get_status():
            self._ssh.connect()
        self._ssh._chan.in_buffer.empty()
        Logger.debug("发送命令:", cmd)
        cmdResult = await self._ssh.execute_cmd(DeviceCmd(cmd, expected, timeout))
        if len(cmdResult.return_string) < 1000:
            Logger.debug("命令回显:", cmdResult.return_string)
        else:
            Logger.debug("返回字符过多，不予展示")
        if re.search(expected, cmdResult.return_string):
            return cmdResult.return_string
        return []

    async def _su_root(self):
        cmdResult = await self._ssh.execute_cmd(DeviceCmd("sudo su", '{0}:'.format(self._userName), 2))
        Logger.debug(cmdResult.return_string)
        cmdResult = await self._ssh.execute_cmd(DeviceCmd(self._password, '#|\$', 2))
        Logger.debug(cmdResult.return_string)

    @staticmethod
    def time_diff_by_now(startTime: str|int|datetime):
        if isinstance(startTime, int):return startTime
        if isinstance(startTime, datetime):
            startTime = startTime.astimezone(pytz.utc).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        if re.search(r"\d{2}T\d{2}", startTime):
            givenTime = datetime.strptime(startTime, "%Y-%m-%dT%H:%M:%S.%fZ") + timedelta(hours=8)
        else:
            givenTime = datetime.strptime(startTime, "%Y-%m-%d %H:%M:%S")
        return int((datetime.now() - givenTime).total_seconds() / 60)

    async def find_csv_files(self, gnbId, path, fileNameCharacter, startTime):
        await self._login_os()
        filePath = self._rootPath + path
        cmd = "find {0} -type f -name *{1}*{2}_* -mmin -{3}".format(
            filePath, fileNameCharacter, gnbId, self.time_diff_by_now(startTime))
        findFileRlt = await self._execute_shell_cmd(cmd)
        fileList = re.findall(r'.+csv', findFileRlt)
        Logger.debug(fileList)
        return fileList

    async def find_xml_files(self, fileNameCharacter, startTime):
        await self._login_os()
        filePath = self._xmlRootPath
        cmd = f"find {filePath} -type f -name *{fileNameCharacter}*_* -mmin -{self.time_diff_by_now(startTime)}"
        findFileRlt = await self._execute_shell_cmd(cmd)
        fileList = re.findall(r'.+zip', findFileRlt)
        Logger.debug(fileList)
        return fileList

    async def operate_col_infos(self, csvfile, colInfos):
        colNums, filters = [], []
        headTitleCsv = re.findall(".*\r", await self._execute_shell_cmd(f"head -n 1 {csvfile}"))[1].strip()
        if re.findall(r'无法|cannot.*', headTitleCsv): raise Exception(f"文件不存在{csvfile.split('TestAll')[-1]},请确认")
        headTitle, headTitleUpper = headTitleCsv.split(","), headTitleCsv.upper().split(",")
        Logger.debug("csv表头信息:",headTitle)
        infos = colInfos.upper().split(',')
        for col in infos:
            if match:= re.search(r"([<>!=])", col):
                key, value = col[:match.start()].strip(' '), col[match.start():].strip(' ')
                if key.isdigit():
                    index = int(key)
                    if len(headTitle)<=index: index = len(headTitle)
                    filters.append((headTitle[index-1], value))
                elif key in headTitleUpper: filters.append((headTitle[headTitleUpper.index(key)],value))
                else: Logger.debug(f"{key}不在csv文件表头中，忽略该过滤条件")
                col = key
            if not re.search(r"\s*\d+\s*-?\s*(\d+)?", col):
                if col.strip(' ') in headTitleUpper: colNums.append(str(headTitleUpper.index(col.strip(' ')) + 1))
                else: Logger.debug(f"{col.strip(' ')}不在csv文件表头中，忽略该过滤条件")
            else:
                colNums.append(col)
        colNums = re.sub(r"\s+", "", ",".join(colNums))
        Logger.debug(f"输入列信息:{colInfos},输出列信息:{colNums}, 输出过滤信息:{filters}")
        return colNums, filters

    async def _check_condition(self, rowDicts, filters):
        for key, condition in filters:
            try:
                if not eval(f"{rowDicts.get(key)} {condition}"): return False
            except Exception as err:
                Logger.error(f"{rowDicts.get(key)} {condition} 命令错误，请检查", err)
                raise Exception(f"{rowDicts.get(key)} {condition} 命令错误，请检查")
        return True

    async def read_csv_files(self, gnbId, files, colInfos):
        rltDict = {gnbId:[]}
        for file in files:
            colNums, filters = await self.operate_col_infos(file, colInfos)
            findFileContentRlt = await self._execute_shell_cmd("cut -d ',' -f '{0}' {1}".format(colNums, file))
            conts = re.findall(".*\r", findFileContentRlt)
            rltValues, heads = [], conts[1].strip().split(',')
            rltValues.append(heads)
            for cont in conts[2:]:
                values = cont.strip().split(',')
                rows = dict(zip(heads, values))
                if await self._check_condition(rows, filters): rltValues.append(values)
            rltDict[gnbId].append({file[len(self._rootPath):-4]: rltValues})
            status().update_progress(f"网元:{gnbId},总文件数:{len(files)},待分析数:{len(files) - files.index(file) - 1}")
        return True, rltDict

    async def get_csv_datas(self, gnbId, path, fileNameCharacter, startTime, colInfos):
        await self._login_os()
        fileList = await self.find_csv_files(gnbId, path, fileNameCharacter, startTime)
        if not fileList:
            return False, "在NDS服务器中没有搜索到合适的文件"
        elif len(fileList) > 30:
            return False, f"在NDS服务器中找到的文件数量{len(fileList)}大于30个，请减小查询时间！"
        return await self.read_csv_files(gnbId, fileList, colInfos)

    async def write_xml_datas_to_local_path(self, gnbId, files):
        localFilePathList = []
        for file in files:
            part = file.split('/')[-1].split('_')
            part.insert(-1, gnbId)
            xmlFileName = '_'.join(part)[:-4]
            innerFiles = await self._execute_shell_cmd(f"unzip -l {file}")
            if xmlFileName not in innerFiles:
                Logger.info(f"{file}中无目标文件{xmlFileName}.zip")
                continue
            await self._execute_shell_cmd(f"unzip -p {file} {xmlFileName}.zip > /tmp/inner.zip")
            findFileContentRlt = await self._execute_shell_cmd(f"unzip -p /tmp/inner.zip {xmlFileName}.xml", timeout=30)
            xmlFileContent = '\n'.join(findFileContentRlt.split('\n')[1:-1])
            saveFilePath = Workspace.get_local_path(xmlFileName + '.xml')
            with open(saveFilePath, 'w', encoding='utf-8') as f:
                f.write(xmlFileContent)
            localFilePathList.append(await self.submit_file(saveFilePath))
        return localFilePathList

    async def get_xml_datas(self, gnbId, fileNameCharacter, startTime):
        await self._login_os()
        fileList = await self.find_xml_files(fileNameCharacter, startTime)
        if not fileList:
            return False, "在NDS服务器中没有搜索到合适的文件"
        return await self.write_xml_datas_to_local_path(gnbId, fileList)

    async def write_csv_datas_to_local_path(self, fileList):
        localFilePathList = []
        for file in fileList:
            for tryTime in range(0,4):
                headTitleCsv = re.findall(".*\r", await self._execute_shell_cmd(f"head -n 1 {file}"))
                if headTitleCsv: break
            if not headTitleCsv:
                Logger.debug(f'查询{file}的表头失败')
                return False
            headTitleCsv = headTitleCsv[1].strip()
            fileContent = await self._execute_shell_cmd(f"cut -d ',' -f '1-{len(headTitleCsv)}' {file}", ']\$')
            rows = fileContent.split('\n')
            if len(rows) == 1: continue
            saveFilePath = Workspace.get_local_path(file.split('/')[-1])
            with open(saveFilePath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for row in rows[1:-1]:
                    writer.writerow(row.split(','))
            localFilePathList.append(saveFilePath)
        return localFilePathList

    async def combine_csv_datas(self, localFilePathList):
        output_file_path = Workspace.get_local_path('combined.csv')
        all_chunks, all_columns = [], set()
        for file in localFilePathList:
            chunk = pd.read_csv(file, nrows=0)
            cleaned_columns = [col.replace('\n', '') for col in chunk.columns]
            all_columns.update(cleaned_columns)
        all_columns = list(all_columns)
        for file in localFilePathList:
            for chunk in pd.read_csv(file, chunksize=10000, header=0, index_col=False):
                chunk = chunk.reindex(columns=all_columns)
                chunk = chunk.apply(lambda x: x.str.replace('\n', '', regex=False).str.replace('\r', '',
                                                                                               regex=False) if x.dtype == "object" else x)
                all_chunks.append(chunk)
        merged_df = pd.concat(all_chunks, ignore_index=True, sort=False)
        merged_df.to_csv(output_file_path, index=False, encoding='utf-8')
        return output_file_path

    async def get_combined_csv_datas(self, gnbIds, path, fileNameCharacter, startTime):
        await self._login_os()
        fileList = []
        for gnbId in gnbIds:
            fileList.extend(await self.find_csv_files(gnbId, path, fileNameCharacter, startTime))
        if not fileList:
            return False, "在NDS服务器中没有搜索到合适的文件"
        elif len(fileList) > 100:
            return False, f"在NDS服务器中找到的文件数量{len(fileList)}大于100个，请减小查询时间！"
        localFilePath = await self.write_csv_datas_to_local_path(fileList)
        saveFilePath = await self.combine_csv_datas(localFilePath)
        return await self.submit_file(saveFilePath)

    async def submit_file(self, saveFilePath):
        if os.path.exists(saveFilePath):
            sftpUrl = Artifact.submit_file(saveFilePath, file_type="CSV")
            Logger.debug("submit success.")
            return sftpUrl
        return False


    async def check_nds_data(self, ndsFile, checkFile):
        if not ndsFile:
            Logger.info('NDS文件获取失败')
            return False
        elif not checkFile:
            Logger.info('校验规则表获取失败')
            return False
        successFlag, queryInfo = True, []
        ndsFilePath = Artifact.download_file_form_url(ndsFile)
        checkFilePath = Artifact.download_file_form_url(checkFile)
        try:
            df = pd.read_csv(checkFilePath, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(checkFilePath, encoding='utf-8')
        check_dict = dict(zip(df.iloc[0:, 0], df.iloc[0:, 1]))
        header = pd.read_csv(ndsFilePath, nrows=0).columns.tolist()
        for colName, conditions in check_dict.items():
            if colName not in header:
                successFlag = False
                queryInfo.append(f"列名 '{colName}' 在文件中未查找到!")
                continue
            data_chunks, count_valid_items = [], 0
            for chunk in pd.read_csv(ndsFilePath, usecols=[colName], chunksize=10000):
                data_chunks.extend(chunk[colName].fillna('nan').tolist())
                count_valid_items = await self.validate_data(data_chunks, conditions)
            queryInfo.append(f'列名{colName}符合条件{conditions}查询出{count_valid_items}条')
            if not count_valid_items:
                successFlag = False
        return successFlag, queryInfo


    async def check_nds_xml_data(self, ndsFiles, checkFile):
        if not ndsFiles:
            Logger.info('NDS文件获取失败')
            return False
        elif not checkFile:
            Logger.info('校验规则表获取失败')
            return False
        successFlag, queryInfo = True, []
        checkFilePath = Artifact.download_file_form_url(checkFile)
        try:
            df = pd.read_csv(checkFilePath, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(checkFilePath, encoding='utf-8')
        check_dict = dict(zip(df.iloc[0:, 2], df.iloc[0:, 3]))
        for ndsFile in ndsFiles:
            ndsFilePath = Artifact.download_file_form_url(ndsFile)
            with open(ndsFilePath, 'r', encoding='utf-8') as file:
                xml_content = file.read()
                root = ET.fromstring(xml_content)
                smr_element = root.find('.//smr')
                if smr_element is None: continue
                smr_value = smr_element.text.strip().split()
                v_values = [obj.find('v').text.strip().split() for obj in root.findall('.//object')]
                data = {header: [] for header in smr_value}
                for v_value in v_values:
                    for header, value in zip(smr_value, v_value):
                        data[header].append(value)
                for colName, conditions in check_dict.items():
                    count_valid_items = await self.validate_data(data.get(colName), conditions)
                    queryInfo.append(f'文件{ndsFile.split("/")[-1]}列名{colName}符合条件{conditions}查询出{count_valid_items}条')
                if not count_valid_items:
                    successFlag = False
        return successFlag, queryInfo

    async def validate_data(self, data, conditions):
        valid_count, ranges, valid_enum = 0, [], []
        # 正则表达式匹配数值范围和字符串范围
        range_pattern = re.compile(
            r'\s*(-?\d+(?:\.\d+)?)\s*-\s*(-?\d+(?:\.\d+)?)\s*|'  # 匹配数值范围
            r'\s*([a-zA-Z0-9]+)\s*-\s*([a-zA-Z0-9]+)\s*'  # 匹配字符串范围
        )
        for condition in str(conditions).split(','):
            condition = condition.strip()
            match = range_pattern.match(condition)
            if match:
                # 检查匹配的组
                if match.group(1) is not None and match.group(2) is not None:
                    # 数值范围条件
                    start, end = map(float, match.groups()[:2])
                    ranges.append((start, end))
                elif match.group(3) is not None and match.group(4) is not None:
                    # 字符串范围条件
                    start, end = match.groups()[2:4]
                    ranges.append((start.strip(), end.strip()))
            else:
                # 直接解析为单个值
                valid_enum.append(condition.strip())
        Logger.debug(f"校验范围为{ranges},枚举为{valid_enum}")
        for item in data:
            item_str = str(item).strip()
            # 检查是否在枚举值中
            if item_str in valid_enum:
                valid_count += 1
                continue
            # 尝试检查数值范围
            try:
                item_value = float(item_str)
                for start, end in ranges:
                    if isinstance(start, (int, float)) and isinstance(end, (int, float)):
                        if start <= item_value <= end:
                            valid_count += 1
                            break
            except ValueError:
                # 检查字符串范围
                for start, end in ranges:
                    if isinstance(start, str) and isinstance(end, str):
                        if start <= item_str <= end:
                            valid_count += 1
                            break
        return valid_count


if __name__ == '__main__':
    import asyncio
    ndsInfo = {
        "attr":{
            "ip": "**************",
            "username": "pict",
            "password": "PaaS1!2@3#4$",
            "port": "22"
        }
    }
    nds = Nds(ndsInfo)
    print(asyncio.run(nds.check_nds_xml_data('2501', 'MRO')))

