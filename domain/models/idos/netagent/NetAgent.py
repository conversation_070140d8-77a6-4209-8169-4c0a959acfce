"""
@File: GnbFactory.py
@Author: 侯小飞10270755
@Time: 2024/11/01 上午9:54
@License: Copyright 2022-2030
@Desc: None
"""
import copy
import json
import time

from GlobalVars import status
from infrastructure.device.idos.IDosDevice import IDosDevice
from infrastructure.logger import Logger
from infrastructure.utils.Repeat import retries_on_flag
from infrastructure.utils.Role import Role
from infrastructure.utils.TimeHandler import get_datetime_stamp
from domain.models.idos.netagent.Version import Version

class NetAgent(object):
    def __init__(self, netAgentAttrs: dict, idosDevice: IDosDevice = None):
        self._name = netAgentAttrs.get("name")
        self._agentName = None
        self._rmId = None
        self._ip = netAgentAttrs.get("ip")
        self._port = netAgentAttrs.get("port")
        self._deviceType = None
        self._username = netAgentAttrs.get("username")
        self._password = netAgentAttrs.get("password")
        self._idosDevice = idosDevice
        self._init_role()


    def _init_role(self):
        Role.add_role(self, Version(self))

    @property
    def name(self) -> str:
        return self._name

    @property
    def agentName(self) -> str:
        return self._agentName or self.get_agent_info().get("agentName")

    @property
    def rmId(self) -> str:
        return self._rmId or self.get_agent_rmid()

    @property
    def idos(self):
        return self._idosDevice

    @property
    def ip(self) -> str:
        return self._ip

    @property
    def port(self) -> str:
        return self._port

    @property
    def deviceType(self) -> str:
        return self._deviceType or self.get_agent_info().get("accessSystemName").get("en").split()[0]

    def get_agent_info(self):
        result = self.idos.sync_run_cmd("queryAgents")
        agents = json.loads(result.text).get("data")
        for agent in agents:
            if agent.get("agentAlias") == self.name:
                return agent

    def get_agent_rmid(self):
        result = self.idos.sync_run_cmd("queryMesInfo")
        agents = json.loads(result.text)
        for agent in agents:
            if agent.get("*bzid") == self.agentName:
                return agent.get("*rmid")



if __name__ == '__main__':
    idos = IDosDevice({'ip': '*************', 'port': '28201', 'username': 'admin', 'password': 'Idos_1!2@'})
    netAgentAttrs = {'ip': '*************', 'port': '6514', 'username': 'admin', 'password': 'Idos_10632', "name": "201"}
    agent = NetAgent(netAgentAttrs, idos)
    print(agent.deviceType)
    import asyncio
    # print(asyncio.run(agent.query_sftp_versions()))
    print(asyncio.run(agent.query_backup_restores()))
    # print(asyncio.run(agent.wait_current_task_complete()))


