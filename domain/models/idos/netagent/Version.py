"""
@File: GnbFactory.py
@Author: 侯小飞10270755
@Time: 2024/11/01 上午9:54
@License: Copyright 2022-2030
@Desc: None
"""
import copy
import json
import time

from GlobalVars import status
from infrastructure.device.idos.IDosDevice import IDosDevice
from infrastructure.logger import Logger
from infrastructure.utils.Repeat import retries_on_flag
from infrastructure.utils.TimeHandler import get_datetime_stamp


class Version(object):
    def __init__(self, netAgent):
        self._netAgent = netAgent

    def __getattr__(self, name):
        return getattr(self._netAgent, name)

    async def create_version_task(self, taskType, verDirectory=None, object="CNF"):
        attributes = await self._init_attributes(taskType, verDirectory, object)
        response = await self.idos.run_cmd('createUpgradeTaskInSys', {"taskType": taskType, "neName": self.name,
                                                                      "neIpAddress": self.ip, "attributes": attributes})
        Logger.info("retCode:{0}, message:{1}".format(response.status_code, response.text))
        message = json.loads(response.text).get("message")
        if json.loads(response.text).get("code") != 0:
            Logger.error(f"net agent create {taskType} task error, message:{message}")
            return False, message
        result = {"taskId": json.loads(response.text).get("taskId"), "taskType": taskType}
        if taskType == "Backup":
            result.update({"backupName": attributes.get("backupName")})
        return result, None

    async def check_operate(self, taskType):
        response = await self.idos.run_cmd("operationCheck", {"taskType": taskType,
                                                                 "neIpAddress": self.ip})
        Logger.info("retCode:{0}, message:{1}".format(response.status_code, response.text))
        message = json.loads(response.text).get("message")
        if json.loads(response.text).get("code") != 0:
            Logger.error(f"check {taskType} result is:{message}")
            return False, message
        return True, None

    async def _init_attributes(self, taskType, verDirectory=None, object="CNF"):
        match taskType:
            case "Upgrade" | "Rollback":
                if not verDirectory:
                    verDirectory = (await self.query_sftp_versions())[0][0].get("verDirectory")
                return {"verDirectory": verDirectory, "operationObject": object, "rmid": self.rmId}
            case "Backup":
                return {"backupName": f"备份文件_{self.name}_{get_datetime_stamp()}", "rmId": self.rmId}
            case "Restore":
                if not verDirectory:
                    verDirectory = (await self.query_backup_restores())[0][0].get("backupName")
                return {"backupName": verDirectory, "typeList": "1", "rmId": self.rmId}

    async def query_current_version_task(self):
        response = await self.idos.run_cmd('queryUpgradeTaskInSys', {"neIpAddress": self.ip, "isLatest": True})
        Logger.info("retCode:{0}, message:{1}".format(response.status_code, response.text))
        message = json.loads(response.text).get("message")
        if json.loads(response.text).get("code") != 0:
            Logger.error(f"net agent query current task error, message:{message}")
            return False, message
        return json.loads(response.text).get("result"), None

    async def operate_version_task(self, opType, taskId):
        response = await self.idos.run_cmd('operateVersionTask', {"opType": opType, "taskId": int(taskId)})
        Logger.info("retCode:{0}, message:{1}".format(response.status_code, response.text))
        message = json.loads(response.text).get("message")
        if json.loads(response.text).get("code") != 0:
            Logger.error(f"net agent query {opType} task {taskId} error, message:{message}")
            return False, message
        return {"taskId": json.loads(response.text).get("taskId")}, message

    async def query_sftp_versions(self, verDirectory = None):
        response = await self.idos.run_cmd('querySftpversion', {"verDirectory": verDirectory})
        Logger.info("retCode:{0}, message:{1}".format(response.status_code, response.text))
        message = json.loads(response.text).get("message")
        if json.loads(response.text).get("code") != 0:
            Logger.error(f"net agent query sftp version task error, message:{message}")
            return False, message
        return json.loads(response.text).get("result"), None

    async def query_backup_restores(self):
        response = await self.idos.run_cmd('queryBackupLists', {"rmId": self.rmId})
        Logger.info("retCode:{0}, message:{1}".format(response.status_code, response.text))
        message = json.loads(response.text).get("message")
        if json.loads(response.text).get("code") != 0:
            Logger.error(f"net agent query backup task error, message:{message}")
            return False, message
        data = json.loads(response.text).get("data")
        return sorted(data, key=lambda x: x['BackupTime'], reverse=True), None

    async def wait_current_task_complete(self):
        times = 0
        @retries_on_flag(360, time.sleep, 20)
        async def _wait_current_task_until_complete(self):
            try:
                result, _ = await self.query_current_version_task()
                if result and result.get("progress") == "100":
                    return True
                detail = json.loads(result.get("detail")).get("zh") if result.get("detail") else "空。"
                status().update_progress(f'状态:{detail}进度:{result.get("progress") or 0}')
            except TimeoutError:
                nonlocal times
                times += 1
                status().update_progress(f'状态:第{times}次断链重连中...')
                if times == 90: raise Exception(f"idos:{self.idos.ip}断链超过30分钟，请确认")
            return False
        await _wait_current_task_until_complete(self)
        return await self.query_current_version_task()


if __name__ == '__main__':
    idos = IDosDevice({'ip': '*************', 'port': '28201', 'username': 'admin', 'password': 'Idos_1!2@'})
    netAgentAttrs = {'ip': '*************', 'port': '6514', 'username': 'admin', 'password': 'Idos_10632', "name": "201"}
    agent = NetAgent(netAgentAttrs, idos)
    print(agent.deviceType)
    import asyncio
    # print(asyncio.run(agent.query_sftp_versions()))
    print(asyncio.run(agent.query_backup_restores()))
    # print(asyncio.run(agent.wait_current_task_complete()))


