# coding:utf-8
"""
# Create Date:2024.08.28
# Adapted Author 10270755
"""

from abc import ABC, abstractmethod


class ChSim(ABC):

    def __init__(self, chsimAttrs: dict):
        self._id = chsimAttrs.get('id')
        self._ip = chsimAttrs.get('attr', {}).get('ip', '127.0.0.1')
        self._type = chsimAttrs.get('attr', {}).get('type', 'KSW')
        self._port = chsimAttrs.get('attr', {}).get('port') or ('5025' if self._type.lower() == 'ksw' else '3334')

    @property
    def id(self) -> str:
        return self._id

    @property
    def ip(self) -> str:
        return self._ip

    @property
    def port(self) -> str:
        return self._port

    def is_ksw(self):
        return self._type.lower() == 'ksw'

    @abstractmethod
    async def load_config_file(self, *args):
        raise (NotImplementedError("load model must be implemented!"))
    
    @abstractmethod
    async def query_chsim_attr(self, *args):
        raise NotImplementedError("query attr must be implemented!")

    @abstractmethod
    async def query_chsim_status(self, *args):
        raise NotImplementedError("query status must be implemented!")

    @abstractmethod
    async def operate_chsim(self, *args):
        raise NotImplementedError("operate chsim must be implemented!")

    @abstractmethod
    async def set_chsim_attr(self, *args):
        raise NotImplementedError("set attr must be implemented!")