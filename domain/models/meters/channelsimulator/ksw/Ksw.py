# -*- encoding: utf-8 -*-
"""
@Time    :   2024/08/29 10:47:44
<AUTHOR>   侯小飞10270755
@Version :   1.0
@Contact :
@License :   (C)Copyright 2022-2030
@Desc    :   None
"""
import asyncio
import inspect
import os.path

from api.endpoints.action.meters.channelsimulator.Schemas import ChsimLoadFile, QueryChsimAttr, OperateChsim, SetChsim
from domain.models.meters.channelsimulator.ChSim import ChSim
from infrastructure.device.meters.channelsimulator.ksw.Ksw import Ksw as KswDevice
from infrastructure.device.meters.channelsimulator.ksw.KswParam import *
from infrastructure.logger import Logger
from GlobalVars import status
from infrastructure.utils.Reflection import Reflection


class Ksw(ChSim):

    def __init__(self, chsimAttrs: dict):
        super(Ksw, self).__init__(chsimAttrs)
        self._kswDevice = KswDevice(self._ip, self._port)
        self._prjDir = chsimAttrs.get('attr', {}).get("prjDir", "")
        self._coeDir = chsimAttrs.get('attr', {}).get("coeDir", "")
        self.ueIds = None
        self.baseStationId = None


    async def load_config_file(self, funcName, paras:ChsimLoadFile):
        projectDir, projectName = self._init_file_path(paras.project, type="prjDir")
        coefficientDir, coefficient = self._init_file_path(paras.coefficient, type="coeDir")
        args = {"projectDir": projectDir,
                "projectName": projectName,
                "coefficientDir": coefficientDir,
                "coefficient": coefficient,
                "sceneIds": paras.sceneId.split(",") if paras.sceneId else (await self._kswDevice.query_scene_ids())[1],
                "updwType": UPDWTYPE.get(paras.updwType, 2),
                "mode": COE_MODE.get(paras.mode, 2),
                "enableLargeScale": paras.isLargeScale
                 }
        Logger.info(f"{self.id} load_config_file:", args)
        if funcName in CONFIG_FILE_OP_MAP and hasattr(self, CONFIG_FILE_OP_MAP.get(funcName)):
            return await Reflection.constraint_invoke(self, CONFIG_FILE_OP_MAP.get(funcName), **args)
        else:
            raise Exception(f"操作:'{funcName}'不存在,或者无对应处理函数")

    def _init_file_path(self, path, type="prjDir"):
        if path and os.path.dirname(path):
            return os.path.dirname(path), os.path.basename(path)
        dirPath = self._prjDir if type == "prjDir" else self._coeDir
        return dirPath, os.path.basename(path) if path else ""

    async def load_project(self, projectDir:str, projectName:str):
        ret, project_name = await self._kswDevice.query_project_name()
        if projectName != project_name:
            await self._kswDevice.close_project()
            if projectDir:
                await self._kswDevice.set_project_directory(projectDir)
            await self._kswDevice.set_project(projectName)
            await self._kswDevice.start_project()
        else:
            Logger.info(f"目标方案名称{projectName}和当前方案名称一致，不需要加载")
        return True, None

    async def load_coefficient(self, sceneIds, coefficientDir, coefficient, updwType=2, mode=2, enableLargeScale=False):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            if coefficientDir:
                await self._kswDevice.set_coefficient_directory(coefficientDir)
            await self._kswDevice.set_coefficient(sceneId, coefficient, updwType)
            await self._kswDevice.start_load_coefficient(sceneId, mode, enableLargeScale)
            await asyncio.sleep(5)
            retTemp, info = await self._check_load_coefficient_complete(sceneId)
            if not retTemp:
                ret = False
            resultInfo.update({sceneId: info})
        return ret, resultInfo

    async def _check_load_coefficient_complete(self, sceneId):
        now_status, process0times, times = '0', 0, 0
        while now_status == '0':
            now_status, process = await self._kswDevice.query_coefficient_load_progress(sceneId)
            setup_stage = f"{times}:模型系数下载状态:{now_status} {process}%"
            Logger.debug(setup_stage)
            status().update_progress(setup_stage)
            if process == '0':
                process0times += 1
            if process0times == 20:
                result = "启动模型系数下载时，长时状态为0，请确认是否正常!!!"
                Logger.error(result)
                return False, result
            await asyncio.sleep(3)
        return True, None

    async def query_chsim_attr(self, funcName, paras:QueryChsimAttr):
        args = {"sceneIds": paras.sceneId.split(",") if paras.sceneId else (await self._kswDevice.query_scene_ids())[1],
                "updwType": UPDWTYPE.get(paras.updwType, 0),
                "baseStationIds":  self.baseStationId,
                "ueIds":  self.ueIds,
                "channelType": CHANNEL_TYPE.get(paras.channelType, 0)
                 }
        Logger.info(f"{self.id} query_chsim_attr:", args)
        if funcName in CHSIM_ATTR_OP_MAP:
            function =  CHSIM_ATTR_OP_MAP.get(funcName)
            if hasattr(self, function):
                return await Reflection.constraint_invoke(self, function, **args)
            if hasattr(self._kswDevice, function):
                return await Reflection.constraint_invoke(self._kswDevice, function, **args)
        raise Exception(f"操作:'{funcName}'不存在,或者无对应处理函数")

    async def query_current_coefficient(self, sceneIds=[]):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            retTemp, info = await self._kswDevice.query_current_coefficient(sceneId)
            if not retTemp: ret = False
            resultInfo.update({sceneId: info})
        return ret, resultInfo

    async def query_base_station_ids(self, sceneIds=[]):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            retTemp, info = await self._kswDevice.query_base_station_ids(sceneId)
            if not retTemp: ret = False
            resultInfo.update({sceneId: info})
        return ret, resultInfo

    async def query_ue_ids(self, sceneIds=[]):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            retTemp, info = await self._kswDevice.query_ue_ids(sceneId)
            if not retTemp: ret = False
            resultInfo.update({sceneId: info})
        return ret, resultInfo

    async def query_base_station_channels(self, sceneIds=[], baseStationIds=None, channelType=0):
        ret, resultInfo, channelInfo = True, {}, {}
        for sceneId in sceneIds:
            if not baseStationIds:
                retTemp, baseStationIds = await self._kswDevice.query_base_station_ids(sceneId)
                if not retTemp:
                    ret = False
                    continue
            for baseStationId in baseStationIds:
                channelsRetTemp, info = await self._kswDevice.query_base_station_channels(sceneId, baseStationId, channelType)
                channelInfo.update({baseStationId: info})
                if not channelsRetTemp: ret = False
            resultInfo.update({sceneId: channelInfo})
        return ret, resultInfo

    async def query_ue_channels(self, sceneIds=[], ueIds=None, channelType=0):
        ret, resultInfo, channelInfo = True, {}, {}
        for sceneId in sceneIds:
            if not ueIds:
                retTemp, ueIds = await self._kswDevice.query_ue_ids(sceneId)
                if not retTemp:
                    ret = False
                    continue
            for ueId in ueIds:
                channelsRetTemp, info = await self._kswDevice.query_ue_channels(sceneId, ueId, channelType)
                channelInfo.update({ueId: info})
                if not channelsRetTemp: ret = False
            resultInfo.update({sceneId: channelInfo})
        return ret, resultInfo

    async def query_chsim_status(self, funcName, paras:QueryChsimAttr):
        args = {"sceneIds": paras.sceneId.split(",") if paras.sceneId else (await self._kswDevice.query_scene_ids())[1],
                "ueIds":  self.ueIds
                 }
        Logger.info(f"{self.id} load_config_file:", args)
        if funcName in CHSIM_STATUS_OP_MAP:
            function = CHSIM_STATUS_OP_MAP.get(funcName)
            if hasattr(self, function):
                return await Reflection.constraint_invoke(self, function, **args)
            if hasattr(self._kswDevice, function):
                return await Reflection.constraint_invoke(self._kswDevice, function, **args)
        raise Exception(f"操作:'{funcName}'不存在,或者无对应处理函数")

    async def query_slow_fading_progress(self, sceneIds=[]):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            retTemp, info = await self._kswDevice.query_slow_fading_progress(sceneId)
            if not retTemp: ret = False
            resultInfo.update({sceneId: info})
        return ret, resultInfo

    async def query_speed(self, sceneIds=[], ueIds=None):
        ret, resultInfo, speedInfo = True, {}, {}
        for sceneId in sceneIds:
            if not ueIds:
                retTemp, ueIds = await self._kswDevice.query_ue_ids(sceneId)
                if not retTemp:
                    ret = False
                    continue
            for ueId in ueIds:
                retTemp, info = await self._kswDevice.query_speed(sceneId, ueId)
                speedInfo.update({ueId: info})
                if not retTemp: ret = False
            resultInfo.update({sceneId: speedInfo})
        return ret, resultInfo


    async def operate_chsim(self, funcName, paras:OperateChsim):
        args = {"sceneIds": paras.sceneId.split(",") if paras.sceneId else (await self._kswDevice.query_scene_ids())[1],
                "coeStatus":  COE_CONTROL_MODE.get(paras.coeStatus, 0),
                "fadStatus":  FAD_CONTROL_MODE.get(paras.fadStatus, 1),
                "mode":  COE_MODE.get(paras.mode, 2),
                "cmd":  paras.commandLine,
                "queryStatusTimes":  paras.queryStatusTime,
                "expected":  paras.expected
                 }
        Logger.info(f"{self.id} operate_chsim:", args)
        if funcName in CHSIM_OPERATE_OP_MAP:
            function = CHSIM_OPERATE_OP_MAP.get(funcName)
            if hasattr(self, function):
                return await Reflection.constraint_invoke(self, function, **args)
            if hasattr(self._kswDevice, function):
                return await Reflection.constraint_invoke(self._kswDevice, function, **args)
        raise Exception(f"操作:'{funcName}'不存在,或者无对应处理函数")

    async def start_large_scale_fading(self, sceneIds=[], mode=2):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            result = await self._kswDevice.start_large_scale_fading(sceneId, mode)
            if not result[0]:
                ret = False
                resultInfo.update({sceneId: result[1]})
            else:
                resultInfo.update({sceneId: "成功"})
        return ret, resultInfo

    async def coefficient_play_control(self, sceneIds=[], coeStatus=0):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            retTemp, info = await self._kswDevice.coefficient_play_control(sceneId, coeStatus)
            if not retTemp: ret = False
            resultInfo.update({sceneId: info})
        return ret, resultInfo

    async def slow_fading_status_control(self, sceneIds=[], fadStatus=1):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            retTemp, info = await self._kswDevice.coefficient_play_control(sceneId, fadStatus)
            if not retTemp: ret = False
            resultInfo.update({sceneId: info})
        return ret, resultInfo

    async def execute_compound_cmd(self, cmd, queryStatusTimes=18, expected=None):
        ret, info = await self._kswDevice.execute_compound_cmd(cmd, queryStatusTimes, expected)
        if ret:
            return ret, info.return_info
        return ret, info

    async def set_chsim_attr(self, funcName, paras:SetChsim):
        args = dict(paras)
        args.update({
            "sceneIds": paras.sceneId.split(",") if paras.sceneId else (await self._kswDevice.query_scene_ids())[1],
            "updwType": UPDWTYPE.get(paras.updwType, 0),
            "slowFadType": DEVICE_TYPE.get(paras.slowFadType, 2),
            "loopmode": FAD_LOOP_MODE.get(paras.loopmode, 1),
            "stationKind": DEVICE_TYPE.get(paras.stationKind, 2),
            "noiseMode": NOISE_MODE.get(paras.noiseMode, 1),
            "ueIds": self.ueIds,
            "baseStationIds": self.baseStationId
        })
        Logger.info(f"{self.id} load_config_file:", args)
        if funcName in CHSIM_SET_ATTR_OP_MAP and hasattr(self, CHSIM_SET_ATTR_OP_MAP.get(funcName)):
                return await Reflection.constraint_invoke(self, CHSIM_SET_ATTR_OP_MAP.get(funcName), **args)
        raise Exception(f"操作:'{funcName}'不存在,或者无对应处理函数")


    async def set_all_ports_input_level(self, sceneIds=[], updwType=0, power=10, par=15, phase=0, loss=0):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            result = await self._kswDevice.set_all_ports_input_level(sceneId, updwType, power, par, phase, loss)
            if not result[0]:
                ret = False
                resultInfo.update({sceneId: result[1]})
            else:
                resultInfo.update({sceneId: "成功"})
        return ret, resultInfo

    async def set_all_ports_output_level(self, sceneIds=[], updwType=0, power=-30, phase=0, loss=0):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            result = await self._kswDevice.set_all_ports_output_level(sceneId, updwType, power, phase, loss)
            if not result[0]:
                ret = False
                resultInfo.update({sceneId: result[1]})
            else:
                resultInfo.update({sceneId: "成功"})
        return ret, resultInfo

    async def set_channel_speed(self, sceneIds=[], ueIds=[], speedKMh=0.01):
        ret, resultInfo, speedInfo = True, {}, {}
        for sceneId in sceneIds:
            if not ueIds:
                retTemp, ueIds = await self._kswDevice.query_ue_ids(sceneId)
                if not retTemp:
                    ret = False
                    continue
            for ueId in ueIds:
                retTemp, info = await self._kswDevice.set_channel_speed(sceneId, ueId, speedKMh)
                if not retTemp:
                    ret = False
                    speedInfo.update({ueId: info})
                else: speedInfo.update({ueId: "成功"})
            resultInfo.update({sceneId: speedInfo})
        return ret, resultInfo

    async def __set_slow_fading_detail_param(self, sceneIds, stationIds, stationKind, startValue, step):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            resultTemp = {}
            for stationId in stationIds:
                Logger.info(f"场景id{sceneId},设备id{stationId},设备类型{stationKind}设置慢衰落详细参数中")
                result = await self._kswDevice.set_slow_fading_detail_param(sceneId, stationId, stationKind,
                                                                            startValue, step)
                if not result[0]:
                    ret = False
                    resultTemp.update({stationId: result[1]})
                else:
                    resultTemp.update({stationId: "成功"})
            resultInfo.update({sceneId: resultTemp})
        return ret, resultInfo
    async def set_slow_fading_detail_param(self, sceneIds=[], ueIds=None, baseStationIds=None, stationKind=0, startValue=-30, step=1):
        ret, result = True, {}
        if stationKind != 1:
            stationIds = ueIds if ueIds else [-1]
            ret0, resultInfo0 = await self.__set_slow_fading_detail_param(sceneIds, stationIds, 0, startValue, step)
            result.update(resultInfo0)
            ret = ret and ret0
        if stationKind != 0:
            stationIds = baseStationIds if baseStationIds else [-1]
            ret1, resultInfo1 = await self.__set_slow_fading_detail_param(sceneIds, stationIds, 1, startValue, step)
            result.update(resultInfo1)
            ret = ret and ret1
        return ret, result

    async def set_slow_fading_base_param(self, sceneIds=[], slowFadType=2, steplimit=60, timeInterval=3000,
                                         loopcnt=2, loopmode=1):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            result = await self._kswDevice.set_slow_fading_base_param(sceneId, slowFadType, steplimit, timeInterval,
                                         loopcnt, loopmode)
            if not result[0]:
                ret = False
                resultInfo.update({sceneId: result[1]})
            else:
                resultInfo.update({sceneId: "成功"})
        return ret, resultInfo

    async def set_awgn_noise_basic_param(self, sceneIds=[], noiseMode=0, noiseBand=100, signalBand=100, freqOffset=0):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            result = await self._kswDevice.set_awgn_noise_basic_param(sceneId, noiseMode, noiseBand, signalBand, freqOffset)
            if not result[0]:
                ret = False
                resultInfo.update({sceneId: result[1]})
            else:
                resultInfo.update({sceneId: "成功"})
        return ret, resultInfo

    async def set_awgn_noise_detail_parameter_all_channel(self, sceneIds=[], updwType=0, isNoiseEnable=True, noiseSnr=-10, noiseAtten=5):
        ret, resultInfo = True, {}
        for sceneId in sceneIds:
            result = await self._kswDevice.set_awgn_noise_detail_parameter_all_channel(sceneId, updwType, isNoiseEnable,
                                                                                       noiseSnr, noiseAtten)
            if not result[0]:
                ret = False
                resultInfo.update({sceneId: result[1]})
            else:
                resultInfo.update({sceneId: "成功"})
        return ret, resultInfo

    async def awgn_noise_pwm(self, sceneIds=[]):
        resultInfo = {}
        for sceneId in sceneIds:
            await self._kswDevice.awgn_noise_pwm(sceneId)
            resultInfo.update({sceneId: "成功"})
        return True, resultInfo


if __name__  == "__main__":
    paraDict = {
    "attr": {
      "ip": "**************",
      "version": "KSW",
      "prjDir": "",
      "coeDir": "",
    },
    "links": [],
    "id": "CHSIM-10_226_158_75",
    "type": "CHSIM"
  }
    print(Ksw(paraDict).id)
    print(Ksw(paraDict).ip)
    print(Ksw(paraDict).port)

