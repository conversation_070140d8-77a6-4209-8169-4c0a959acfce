from fastapi import Request
from infrastructure.resource.service.DeviceManager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from domain.models.ume.Me import Me
from domain.models.ume.Gnb import Gnb
from domain.models.board.Vbp import Vbp


class VbpFactory:

    @staticmethod
    async def create(request: Request) -> list[Vbp]:
        config = request.state.resource.get("config")
        deviceManager = DeviceManager(config)
        return await deviceManager.find_devices("VBP").async_map(_create_vbp, deviceManager)

    @staticmethod
    async def create_by_alias(request: Request, cellAlias: str) -> list[Vbp]:
        config = request.state.resource.get("config")
        deviceManager = DeviceManager(config)
        return await deviceManager.find_filtered_devices(cellAlias).async_map(_create_vbp, deviceManager)


async def _create_vbp(vbpInfo, deviceManager):
    meInfo = deviceManager.find_linked_device(vbpInfo.get("id"), "GNB")
    if not meInfo:
        return
    me = Me(meInfo[0].get("attr"))
    device = Gnb(meInfo[0].get("attr"), me)
    vbp = Vbp(vbpInfo, device)
    return vbp